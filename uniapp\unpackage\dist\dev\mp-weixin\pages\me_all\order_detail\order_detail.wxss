@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-f28b71da {
  padding: 0 30rpx;
  min-height: 100vh;
  padding-top: 42rpx;
  margin-bottom: 33rpx;
  box-sizing: border-box;
}
.container .u-border-bottom.data-v-f28b71da {
  border: none;
}
.container .course-list.data-v-f28b71da {
  padding: 28rpx;
  margin-bottom: 32rpx;
  background-color: #fff;
  border-radius: 20rpx;
  width: 690rpx;
  box-sizing: border-box;
}
.container .course-list .title.data-v-f28b71da {
  padding-bottom: 18rpx;
  font-weight: bold;
  font-size: 26rpx;
  color: #060606;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .course-list .title .btns.data-v-f28b71da {
  width: 140rpx;
  height: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30rpx;
  border: 1rpx solid #8a8a8a;
}
.container .course-list .title .btns image.data-v-f28b71da {
  width: 20rpx;
  height: 20rpx;
  margin-right: 5rpx;
}
.container .course-list .title .btns text.data-v-f28b71da {
  font-size: 22rpx;
  color: #8a8a8a;
  margin-top: -5rpx;
}
.container .course-list .title .done.data-v-f28b71da {
  width: 110rpx;
  height: 45rpx;
  line-height: 45rpx;
  text-align: center;
  font-size: 22rpx;
  color: #00C2A0;
  border-radius: 30rpx;
  border: 1rpx solid #00C2A0;
}
.container .course-list .title .status.data-v-f28b71da {
  font-weight: 500;
  font-size: 24rpx;
  color: #A2A2A2;
}
.container .course-list .mid.data-v-f28b71da {
  margin-top: 6rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.container .course-list .mid .subject.data-v-f28b71da {
  font-weight: bold;
  font-size: 22rpx;
  color: #09CC8C;
  padding: 9rpx 7rpx;
  background: #EEFAF6;
  border-radius: 10rpx;
}
.container .course-list .mid .tag.data-v-f28b71da {
  padding: 0 8rpx;
  height: 48rpx;
  background-color: #EEFAF6;
  line-height: 48rpx;
  font-size: 22rpx;
  font-weight: bold;
  border-radius: 10rpx;
}
.container .course-list .mid .date.data-v-f28b71da {
  margin-left: 8rpx;
  font-size: 22rpx;
  color: #A4A4A4;
}
.container .course-list .bottom.data-v-f28b71da {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .course-list .bottom .teacher-list.data-v-f28b71da {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 20rpx;
}
.container .course-list .bottom .teacher-list .teacher-info.data-v-f28b71da {
  margin-right: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #818181;
}
.container .course-list .bottom .teacher-list .teacher-info .avatar.data-v-f28b71da {
  width: 60rpx;
  height: 60rpx;
  border-radius: 100%;
  margin-bottom: 2rpx;
}
.container .course-list .bottom .course-money.data-v-f28b71da {
  color: #E16965;
  font-size: 30rpx;
}
.container .detail.data-v-f28b71da {
  background-color: #fff;
  border-radius: 20rpx;
}
.container .detail .title.data-v-f28b71da {
  padding: 28rpx;
  padding-bottom: 20rpx;
  color: #201E2E;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #E5E5E5;
}
.container .detail .title .status.data-v-f28b71da {
  font-weight: 500;
  font-size: 24rpx;
  color: #A2A2A2;
}
.container .detail .list-info.data-v-f28b71da {
  padding-top: 28rpx;
  padding-bottom: 28rpx;
  border-bottom: 1rpx solid #E5E5E5;
}
.container .detail .list-info .info.data-v-f28b71da {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #A2A2A2;
  font-size: 24rpx;
  padding: 0 28rpx;
  padding-bottom: 18rpx;
}
.container .detail .list-info .info text.data-v-f28b71da:last-child {
  color: #201E2E;
}
.container .detail .bottom.data-v-f28b71da {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 26rpx 28rpx 36rpx;
}
.container .detail .bottom .btn.data-v-f28b71da {
  width: 110rpx;
  height: 45rpx;
  line-height: 45rpx;
  text-align: center;
  font-weight: 400;
  font-size: 22rpx;
  color: #343434;
  border-radius: 30rpx;
  border: 1rpx solid #989898;
}
.container .detail .total.data-v-f28b71da {
  color: #A4A4A4;
  font-size: 24rpx;
  display: flex;
  align-items: center;
}
.container .detail .total text.data-v-f28b71da {
  font-size: 32rpx;
  color: #E16965;
}

