<template>
	<!-- 商品卡牌 -->
	<view>
		<block>
			<view class="course-list" v-for="i in 8" :key="i">

				<view class="top">
					<view class="info">
						<text class="course_title">2026年牛蛙pro超值精品班型包含数学英语政治</text>

					</view>
					<view class="btn-test">
						去试听
					</view>
				</view>
				<view @click="toDetail(item)">
					<view class="mid">
						<view class="tag">
							考研数学
						</view>
						<text class="date">2024.02.04-2024.12.16 | 共54节</text>

					</view>
					<view class="bottom">
						<view class="teacher-list">
							<view class="teacher-info">
								<image class="avatar" src="@/static/wa.png"></image>
								<text>孙老师</text>
							</view>
							<view class="teacher-info">
								<image class="avatar" src="@/static/wa.png"></image>
								<text>孙老师</text>
							</view>
							<view class="teacher-info">
								<image class="avatar" src="@/static/wa.png"></image>
								<text>孙老师</text>
							</view>
						</view>
						<view class="money-right">
							<view class="money">
								￥3800.00
							</view>
							<view class="add-btn">添加</view>
						</view>

					</view>
				</view>

			</view>
		</block>
		<login v-if="enter" @loadpage="unloadpage" @closepage='closepage'></login>
	</view>
</template>

<script>
	import {
		order_joinCar,
	} from "@/api/comm.js"
	export default {
		name: "ordercard",
		props: {
			content: {
				type: Array,
				default: [],
				required: true
			},
			store_id: {
				type: String,
				default: ' ',
				required: true
			},
			activate_data: {
				type: Number,
				default: '',
				required: true
			},
		},
		data() {
			return {
				enter: '',
			};
		},
		methods: {
			// 添加购物车
			async add_joinCar(id) {
				if (!uni.getStorageSync('userinfo')) {
					this.$emit('login')
					return
				}

				let data = await order_joinCar({
					store_id: this.store_id,
					goods_id: id,
					count: 1,
					order_type: this.activate_data,
				})

				if (data.code == 1) {
					uni.showToast({
						title: data.msg,
						icon: "none"
					})
					this.$emit('order')
				} else {
					uni.showToast({
						title: data.msg,
						icon: "none"
					})
				}
			},

			routergo(url) {
				uni.navigateTo({
					url: url
				})
			},
			stop(e, type) {
				let obj = {
					e,
					type
				}
				this.$emit('specification', obj)
			},
			//未登录关闭弹出层需要关掉组件
			closepage() {
				this.enter = false
			},
		}
	}
</script>

<style lang="scss">
	.course-list {
		// width: 542rpx;
		margin-bottom: 22rpx;
		background-color: #fff;
		border-radius: 20rpx;
		border: 1rpx solid #00C2A0;
		margin-left: 15rpx;

		.top {
			padding: 16rpx 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.info {
				font-size: 26rpx;
				font-weight: bold;
				// color: $uni-text-main-black;
				display: flex;
				flex-direction: column;
				width: 75%;

				.course_title {
					font-weight: bold;
					font-size: 28rpx;
					color: #060606;
				}
			}

			.btn-test {
				width: 96rpx;
				height: 45rpx;
				line-height: 45rpx;
				background: #00C2A0;
				border-radius: 30rpx;
				text-align: center;
				font-weight: bold;
				font-size: 22rpx;
				color: #FFFFFF;
			}
		}

		.mid {
			width: 100%;
			margin-top: 6rpx;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			padding-left: 20rpx;
			box-sizing: border-box;

			.tag {
				padding: 0 8rpx;
				height: 48rpx;
				background-color: #EEFAF6;
				color: #09CC8C;
				line-height: 48rpx;
				font-size: 22rpx;
				font-weight: bold;
				border-radius: 10rpx;
			}

			.date {
				margin-left: 8rpx;
				font-size: 22rpx;
				color: #A4A4A4;
			}
		}

		.bottom {
			margin-top: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.teacher-list {
				padding-left: 10rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				flex-wrap: wrap;
				padding-bottom: 20rpx;

				.teacher-info {
					margin-right: 15rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-size: 22rpx;
					color: #818181;
					margin-bottom: 20rpx;

					&:last-child {
						margin-right: 0;
					}

					.avatar {
						width: 60rpx;
						height: 60rpx;
						border-radius: 100%;
						margin-bottom: 2rpx;
					}

				}
			}

			.money-right {
				display: flex;
				align-items: center;
				margin-right: 10rpx;

				.add-btn {
					width: 92rpx;
					height: 45rpx;
					line-height: 45rpx;
					text-align: center;
					font-weight: bold;
					font-size: 22rpx;
					color: #FFFFFF;
					background: #00C2A0;
					border-radius: 30rpx;
				}

				.money {
					font-size: 30rpx;
					color: #E16965;
					margin-right: 10rpx;

				}
			}

		}

	}
</style>