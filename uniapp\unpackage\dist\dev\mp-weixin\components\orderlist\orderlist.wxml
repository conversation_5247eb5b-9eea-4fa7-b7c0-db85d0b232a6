<view><view class="content"><scroll-view style="width:100%;height:100%;" scroll-y="true"><block wx:for="{{content}}" wx:for-item="item" wx:for-index="index" wx:key="*this"><block><view data-event-opts="{{[['tap',[['select',['$0',index],[[['content','',index]]]]]]]}}" class="{{['text',activeIndex==index?'active':'']}}" bindtap="__e">{{''+item.name+''}}</view></block></block></scroll-view></view></view>