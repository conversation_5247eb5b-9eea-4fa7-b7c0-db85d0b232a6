<template>
	<!-- 左边侧边 -->
	<view>
		<view class="content">
			<scroll-view style="width: 100%; height: 100%;" scroll-y="true">
				<block v-for="(item,index) in content" :key="item">
					<view :class="['text',activeIndex==index ? 'active': '']" @tap="select(item,index)">
						{{item.name}}
					</view>
				</block>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "orderlist",
		props: {
			content: {
				type: Array,
				default: [],
				required: true
			},
			activeIndex: {
				type: Number
			}
		},
		data() {
			return {
				select_index: 0
			};
		},
		watch: {
			// activeIndex: function(vlo, rol) {
			// 	this.select_index = 0
			// }
		},
		methods: {
			select(goods, index) {
				console.log(index);
				this.select_index = index
				this.$emit('aid_mgs', goods, index)
			}
		}
	}
</script>

<style lang="scss">
	.content {
		width: 179rpx;
		height: 63vh;
		padding-bottom: 200rpx;
		background: #F6F7FB;
		border-radius: 0rpx 18rpx 0rpx 0rpx;
		position: fixed;

		.text {
			width: 179rpx;
			height: 92rpx;
			font-size: 24rpx;
			font-weight: 400;
			color: #87888B;
			line-height: 92rpx;
			text-align: center;


		}

		.active {
			font-weight: bold;
			color: #00C2A0;
			position: relative;
			background-color: #fff;
			border-radius: 0rpx 16rpx 16rpx 0rpx;

			&::before {
				content: '';
				position: absolute;
				width: 10rpx;
				height: 52rpx;
				top: 50%;
				left: 0;
				transform: translateY(-50%);
				background-color: #00C2A0;
				border-radius: 0rpx 16rpx 16rpx 0rpx;
			}
		}
	}
</style>