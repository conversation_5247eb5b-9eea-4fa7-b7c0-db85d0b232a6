@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-70684b36 {
  min-height: 100vh;
  background-color: #F7F7F7 !important;
}
.content.data-v-70684b36 {
  position: relative;
  background-color: #F7F7F7 !important;
}
.content .content_hadrimg.data-v-70684b36 {
  width: 100%;
  height: 503rpx;
  position: absolute;
  top: 0;
}
.content .content_main.data-v-70684b36 {
  position: absolute;
  top: 403rpx;
  position: relative;
  padding-bottom: 80rpx;
}
.content .content_main .content_main_box.data-v-70684b36 {
  width: 703rpx;
  border-radius: 16rpx 16rpx 0 0;
  margin: 0 auto;
  position: absolute;
  top: -200rpx;
  left: 50%;
  background-color: transparent;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.content .content_main .content_main_box .content_main_box_1.data-v-70684b36 {
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left.data-v-70684b36 {
  display: flex;
  margin-left: 10rpx;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left .content_main_box_1_left_avatar.data-v-70684b36 {
  position: relative;
  width: 133rpx;
  height: 133rpx;
  margin-right: 20rpx;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left .content_main_box_1_left_avatar .content_main_box_1_left_avatar_img.data-v-70684b36 {
  width: 133rpx;
  height: 133rpx;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left .content_main_box_1_left_avatar .content_main_box_1_left_img.data-v-70684b36 {
  width: 117rpx;
  height: 117rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left .content_main_box_1_left_avatar .content_main_box_1_left_img image.data-v-70684b36 {
  border-radius: 50%;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left .content_main_box_1_left_avatar .content_main_box_1_left_LV.data-v-70684b36 {
  position: absolute;
  bottom: -16rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  font-size: 22rpx;
  color: #05B6F6;
  background-color: #F1FBFF;
  padding: 6rpx 20rpx;
  box-sizing: border-box;
  border-radius: 30rpx;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left_title.data-v-70684b36 {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left_title .content_main_box_1_left_title_1 .content_main_box_1_left_title_1_text.data-v-70684b36 {
  font-weight: 500;
  font-size: 34rpx;
  color: #FFFFFF;
  max-width: 300rpx;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left_title .content_main_box_1_left_title_1 text.data-v-70684b36:nth-child(2) {
  height: 32rpx;
  background: #F1FBFF;
  border-radius: 10rpx 10rpx 10rpx 0rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #00CCFF;
  padding: 5rpx 10rpx;
  margin-left: 10rpx;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left_title .content_main_box_1_left_title_1_btn.data-v-70684b36 {
  width: 88rpx;
  height: 41rpx;
  background: rgba(255, 255, 255, 0.35);
  border-radius: 424rpx 424rpx 424rpx 424rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 41rpx;
  text-align: center;
  margin-top: 6rpx;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left_title .content_main_box_1_left_title_2.data-v-70684b36 {
  font-size: 22rpx;
  color: #FFFFFF;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left_title .content_main_box_1_left_title_3.data-v-70684b36 {
  font-size: 22rpx;
  color: #FFFFFF;
}
.content .content_main .content_main_box .content_main_box_1 .content_main_box_1_left_title .content_main_box_1_left_title_3 .content_main_box_1_left_title_3_line.data-v-70684b36 {
  width: 230rpx;
  height: 8rpx;
  padding-top: 12rpx;
  margin-right: 10rpx;
}
.content .content_main .content_main_box .content_main_box_1_right.data-v-70684b36 {
  width: 60rpx;
  height: 60rpx;
  margin-right: 10rpx;
}
.content .content_main .content_main_box .content_main_box_2.data-v-70684b36 {
  width: 645rpx;
  height: 70rpx;
  background: #F1FBFF;
  border-radius: 8rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content .content_main .content_main_box .content_main_box_2 .content_main_box_2_left.data-v-70684b36 {
  font-size: 26rpx;
  font-weight: 400;
  color: #00C8FF;
  margin-left: 10rpx;
}
.content .content_main .content_main_box .content_main_box_2 .content_main_box_2_right.data-v-70684b36 {
  width: 120rpx;
  height: 54rpx;
  background: #00CCFF;
  border-radius: 27rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #F1FCFF;
  line-height: 54rpx;
  text-align: center;
  margin-right: 10rpx;
}
.content .card-box.data-v-70684b36 {
  width: 690rpx;
  background: #FFFFFF;
  border-radius: 15rpx 15rpx 15rpx 15rpx;
  margin: -50rpx auto 0;
  padding: 23rpx 33rpx;
  box-sizing: border-box;
}
.content .card-box .card-box_title.data-v-70684b36 {
  font-weight: bold;
  font-size: 30rpx;
  color: #343434;
  margin-bottom: 23rpx;
}
.content .card-box .card-box_title_content.data-v-70684b36 {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}
.content .card-box .card-box_title_content .card-box_title_content_item .card-box_title_content_item_top.data-v-70684b36 {
  width: 100%;
  text-align: center;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.content .card-box .card-box_title_content .card-box_title_content_item .card-box_title_content_item_top .img_1.data-v-70684b36 {
  width: 56rpx;
  height: 56rpx;
  margin-right: 10rpx;
}
.content .card-box .card-box_title_content .card-box_title_content_item .card-box_title_content_item_top .img_2.data-v-70684b36 {
  width: 58rpx;
  height: 44rpx;
  margin-right: 10rpx;
}
.content .card-box .card-box_title_content .card-box_title_content_item .card-box_title_content_item_top .img_3.data-v-70684b36 {
  width: 40rpx;
  height: 33rpx;
  margin-right: 10rpx;
}
.content .card-box .card-box_title_content .card-box_title_content_item .card-box_title_content_item_top .text_1.data-v-70684b36 {
  font-weight: bold;
  font-size: 26rpx;
  color: #343434;
}
.content .card-box .card-box_title_content .card-box_title_content_item .card-box_title_content_item_bom.data-v-70684b36 {
  text-align: center;
  font-weight: 400;
  font-size: 20rpx;
}
.content .card-box .card-box_title_content .card-box_title_content_item .card-box_title_content_item_bom ._span.data-v-70684b36:nth-child(1) {
  color: #00C2A0;
}
.content .card-box .card-box_title_content .card-box_title_content_item .card-box_title_content_item_bom ._span.data-v-70684b36:nth-child(2) {
  color: #C3C3C3;
}
.content .card.data-v-70684b36 {
  width: 690rpx;
  margin: 30rpx auto;
  padding: 23rpx 0;
  box-sizing: border-box;
  background: #FFFFFF;
  border-radius: 15rpx 15rpx 15rpx 15rpx;
}
.content .card.data-v-70684b36:last-child {
  margin-bottom: 50rpx;
}
.content .card .card_title.data-v-70684b36 {
  font-weight: bold;
  font-size: 30rpx;
  color: #343434;
  padding: 0 33rpx;
  box-sizing: border-box;
}
.content .card .card_groud.data-v-70684b36 {
  padding: 0 33rpx;
  box-sizing: border-box;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
}
.content .card .card_groud .card_groud_item.data-v-70684b36 {
  height: 100rpx;
  text-align: center;
  margin-top: 43rpx;
  font-weight: 400;
  font-size: 22rpx;
  color: #343434;
}
.content .card .card_groud .card_groud_item image.data-v-70684b36 {
  width: 49rpx;
  height: 39rpx;
  margin-bottom: 10rpx;
}
.content .card .card_groud .card_groud_item .svgImg.data-v-70684b36 {
  width: 60rpx;
  height: 50rpx;
}
.content .card .card_groud .border.data-v-70684b36 {
  margin-top: 30rpx;
  width: 38rpx;
  height: 110rpx;
}
.content .card .lastflex.data-v-70684b36 {
  padding: 0 33rpx;
  box-sizing: border-box;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: flex-start;
}
.content .card .lastflex .card_groud_item.data-v-70684b36 {
  height: 100rpx;
  text-align: center;
  margin-top: 43rpx;
  font-weight: 400;
  font-size: 22rpx;
  color: #343434;
  margin-right: 60rpx;
}

