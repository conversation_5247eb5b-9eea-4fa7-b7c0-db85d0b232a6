<template>
	<view>
		<!-- :closeable='true' -->
		<u-popup :show="show" :round="20" @close="close" :safeAreaInsetTop='true'>
			<view class="login">
				<view class="close" @click="close">
					<u-icon name='close' size='40'></u-icon>
				</view>
				<view class="hadr">
					<view>手机号登录/注册</view>
					<view>欢迎登录</view>
				</view>
				<view class="input">
					<view class="input_1">
						<view class="input_1_left">
							手机号
						</view>
						<input type="text" v-model="cell" maxlength="11" placeholder="请输入手机号">
						<view class="input_1_right" style="" v-if="cell.length!=11">
							获取验证码
						</view>
						<view class="input_1_right" v-else style="background: #05B6F6;" @tap="timeevent">
							<text v-if="time==60">获取验证码</text>
							<text v-else>{{time}}s</text>
						</view>
					</view>
					<view class="input_2">
						<view class="input_2_left">
							验证码
						</view>
						<input type="text" v-model="code" maxlength="4" placeholder="请输入验证码">
						<view class="input_2_right" :style="{background: code.length==4?'#05B6F6':''}" @tap="checkApi">
							登录
						</view>
					</view>
				</view>
				<view class="wechat">
					<view class="wechat_division" v-if="common.is_shlogin!==1">
						<u-divider text="手机号快捷登录" textSize='24rpx'></u-divider>
					</view>
					<view class="">
						<button v-if="select" open-type="getPhoneNumber" @getphonenumber="getphonenumber">
							<view class="wechat_img">
								<image src="@/static/phone.png" mode=""></image>
							</view>
						</button>
						<view v-else class="wechat_img" @click="loginClick">
							<image src="@/static/phone.png" mode=""></image>
						</view>
					</view>

					<view class="agreement">
						<label class="radio" @tap="selectevent">
							<radio value="r1" style="transform: scale(0.7);" color="#05B6F6" :checked="select" />
						</label>
						<view>
							<text>登录即代表同意</text>
							<text
								@tap="routerTo('/pages/order_all/login_protocol/login_protocol?name='+'用户协议'+'&state='+0)">《用户协议》,</text>
							<text
								@tap="routerTo('/pages/order_all/login_protocol/login_protocol?name='+'隐私政策'+'&state='+0)">《隐私政策》</text>
							<text>及</text>
							<text
								@tap="routerTo('/pages/order_all/login_protocol/login_protocol?name='+'第三方SDK类服务商说明'+'&state='+0)">《第三方SDK类服务商说明》</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>


		<u-popup :closeOnClickOverlay='false' :show="usershow" :round="10" mode='center' @close="usershow=false">
			<view class="user_box">
				<view class="user_box_title">
					<view class="" style="margin-left: 30rpx;"></view>
					<view class="">登录后可享受更多功能</view>
					<view class="" @click="usershow=false">
						<u-icon name='close' size='40'></u-icon>
					</view>
				</view>
				<button open-type='chooseAvatar' @chooseavatar="setimg">
					<view class="user_avatar">
						<image v-if="loginavatar" :src="loginavatar" mode=""></image>
						<image v-else src="../../static/avatar.png" mode=""></image>
					</view>
				</button>

				<view class="user_ipt">
					<view>昵称</view>
					<input @change='change' type="nickname" v-model="login_name" placeholder="请输入昵称" />
				</view>

				<view class="user_tag">
					99%+的用户使用选择微信头像和微信昵
					称，便于订单发货和售后沟通
				</view>

				<view class="user_btn" @click="wxlogin()">
					立即登录
				</view>
			</view>
		</u-popup>

	</view>
</template>

<script>
	import {
		send,
		check,
		login,
		wx_login,
		get_openid,
		getInfo,
		getWxPhone
	} from "@/api/user.js"
	import {
		smsSend,
		smsLogin,
		getAssets
	} from "@/api/comm.js"

	import {
		userInfo,
		common
	} from "@/api/public.js"
	import
	loginApi
	from '@/utils/wxApi.js'

	export default {
		props: ['show'],
		name: "login",
		data() {
			return {
				loginavatar: '',
				usershow: false,
				cell: '',
				time: 60,
				code: '',
				// 单选
				select: false,
				getuserinfo: {},
				logincode: '',
				common: null,
				login_name: "",
				login_url: null
			};
		},
		mounted() {
			loginApi.loginApi().then(res => {
				this.logincode = res.code
			})
			let userinfo = uni.getStorageSync('user')
			// this.commonApi()
		},
		watch: {
			show(newVal) {
				if (newVal) {

				}
			}
		},
		methods: {
			async checkApi() {
				if (this.select) {
					let {
						data,
						errCode,
						msg
					} = await smsLogin({
						phone: this.cell,
						smsCode: this.code
					})
					if (errCode == 0) {
						uni.setStorageSync('TOKEN', data)
						this.getUserDetail()
					} else {
						uni.showToast({
							title: msg,
							icon: "none"
						})
					}
				} else {
					uni.showToast({
						title: '请勾选用户协议',
						icon: "none"
					})
				}
			},
			timeevent() {
				this.sendApi()
				let timer = setInterval(() => {
					this.time--
					if (!this.time) {
						this.time = 60
						clearTimeout(timer)
					}
				}, 1000);
			},
			async sendApi() {
				let {
					data,
					errCode,
					msg
				} = await smsSend({
					phone: this.cell
				})
				if (errCode == 0) {

				} else {
					uni.showToast({
						title: msg,
						icon: "none"
					})
				}
			},
			selectevent() {
				this.select = this.select ? false : true
			},
			loginClick() {
				if (this.select) {
					this.usershow = true
					this.close()
				} else {
					uni.showToast({
						title: '请勾选用户协议',
						icon: "none"
					})
				}


			},
			close(data) {
				this.$emit('closepage', data)
			},
			async getphonenumber(e) {
				if (e.detail.errMsg.indexOf('ok') != -1) {
					const {
						errCode,
						data
					} = await getWxPhone({
						code: e.detail.code
					})
					if (errCode == 0) {
						uni.setStorageSync('TOKEN', data.token)
						this.getUserDetail()
					}
				}
			},
			async getUserDetail() {
				const {
					errCode,
					data
				} = await getInfo()
				if (errCode == 0) {
					uni.setStorageSync('user', data)
					this.setUserDetail(data)
					this.getAsset()

				}
			},
			async getAsset() {
				const {
					data,
					errCode,
					msg
				} = await getAssets()
				if (errCode == 0) {
					uni.setStorageSync('ASSET', data)
					this.close(data)
				} else {
					uni.showToast({
						title: msg,
						icon: 'none'
					})
				}
			},
			setUserDetail(res) {
				this.$store.commit('user/setUserInfo', {
					...res,
				})

			}
		}
	}
</script>

<style lang="scss">
	::v-deep.uicon-close {
		font-size: 50rpx;
	}

	.user_box {
		width: 600rpx;
		height: 700rpx;
		text-align: center;
		padding: 30rpx 30rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.user_box_title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 34rpx;
			font-weight: 600;
		}

		.user_avatar {
			image {
				width: 200rpx;
				height: 200rpx;
				border-radius: 50%;
			}
		}

		.user_ipt {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 30rpx;
			color: #3d3d3d;
			padding: 20rpx 0;
			box-sizing: border-box;
			border-bottom: 1rpx #eee solid;

			input {
				width: 400rpx;
				text-align: left;
			}
		}

		.user_tag {
			background-color: #f3f3ff;
			padding: 24rpx 30rpx;
			box-sizing: border-box;
			font-size: 24rpx;
			color: #848484;
			text-align: left;
			border-radius: 16rpx;
		}

		.user_btn {
			background-color: #05B6F6;
			height: 80rpx;
			width: 100%;
			font-size: 28rpx;
			color: #FFFFFF;
			text-align: center;
			line-height: 80rpx;
			border-radius: 50rpx;
		}
	}


	.login {
		height: 1000rpx;
		background: #FFFFFF;
		z-index: 9999999;

		.close {
			position: absolute;
			top: 30rpx;
			right: 30rpx;
			z-index: 99999991;
		}

		.hadr {
			padding: 30rpx 40rpx;

			view:nth-child(1) {
				font-size: 32rpx;
				font-weight: 600;
				color: #0D0D0D;
			}

			view:nth-child(2) {
				font-size: 24rpx;
				font-weight: 500;
				color: #9A9A9A;
				padding: 20rpx 0;
			}
		}

		.input {
			padding: 0 40rpx;

			.input_1 {
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1rpx solid #CCCCCC;
				padding: 30rpx 0;

				.input_1_left {
					font-size: 32rpx;
					font-weight: 500;
					color: #0D0D0D;
				}

				input {
					width: 300rpx;
					font-size: 32rpx;
					font-weight: 400;
					color: #9A9A9A;
				}

				.input_1_right {
					width: 162rpx;
					height: 57rpx;
					background: #95DBF5;
					border-radius: 29rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 57rpx;
					text-align: center;
				}
			}

			.input_2 {
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1rpx solid #CCCCCC;
				padding: 30rpx 0;

				.input_2_left {
					font-size: 32rpx;
					font-weight: 500;
					color: #0D0D0D;


				}

				input {
					width: 300rpx;
					font-size: 32rpx;
					font-weight: 400;
					color: #9A9A9A;
				}

				.input_2_right {
					width: 162rpx;
					height: 57rpx;
					background: #95DBF5;
					border-radius: 29rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #FFFFFF;
					line-height: 57rpx;
					text-align: center;
				}
			}
		}

		.wechat {
			.wechat_division {
				width: 80%;
				height: 30rpx;
				margin: 0 auto;
				padding: 40rpx 0;
				font-size: 24rpx
			}

			.wechat_img {
				width: 100rpx;
				height: 100rpx;
				margin: 0 auto;
				margin-top: 40rpx;
			}

			.agreement {
				width: 523rpx;
				height: 66rpx;
				margin: 30rpx auto;
				font-size: 24rpx;
				font-weight: 400;
				color: #9A9A9A;
				display: flex;
				justify-content: space-between;

				text:nth-child(1) {}

				text:nth-child(2) {
					color: #4270BA;
				}

				text:nth-child(3) {
					color: #4270BA;
				}

				text:nth-child(4) {}

				text:nth-child(5) {
					color: #4270BA;
				}
			}
		}
	}
</style>