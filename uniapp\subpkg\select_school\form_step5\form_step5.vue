<template>
	<view class="content">
		<height :hg='System_height'></height>
		<view class="content_header">
			<view class="nav-title" :style="{'top':System_height+'rpx'}">
				<text>大学生涯规划报告</text>
			</view>
			<uni-icons type="left" size="24" color="#2D2D2D" class="back-left" :style="{'top':System_height+'rpx'}"
				@tap="back"></uni-icons>
		</view>
		
		<!-- 通知栏 -->
		<view class="notification">
			<image src="/static/select_school/notification_icon-56586a.png" class="notification-icon"></image>
			<text class="notification-text">请认真完善信息，以便于精准生成报告！</text>
		</view>
		
		<!-- 步骤指示器 -->
		<view class="step-indicator">
			<view class="step-item">
				<view class="step-number">1</view>
			</view>
			<view class="step-item">
				<view class="step-number">2</view>
			</view>
			<view class="step-item active">
				<view class="step-number">3</view>
			</view>
		</view>
		
		<!-- 表单标题 -->
		<view class="form-title">
			<view class="title-bg"></view>
			<text class="title-text">兴趣类</text>
		</view>
		
		<!-- AI 图片区域 -->
		<view class="ai-section">
			<view class="ai-container">
				<image src="/static/select_school/ai4-56586a.png" class="ai-bg-4"></image>
				<image src="/static/select_school/ai1-56586a.png" class="ai-bg-1"></image>
				<image src="/static/select_school/ai2-56586a.png" class="ai-bg-2"></image>
				<view class="ai-center">
					<image src="/static/select_school/ai-56586a.png" class="ai-main"></image>
					<image src="/static/select_school/ai_text-56586a.png" class="ai-text"></image>
				</view>
			</view>
		</view>
		
		<!-- 表单内容 -->
		<view class="form-content">
			<view class="form-item">
				<view class="form-label">体育特长：</view>
				<textarea class="form-textarea" v-model="formData.sportsSkills" placeholder="比如篮球、足球、游泳等"></textarea>
			</view>
			
			<view class="form-item">
				<view class="form-label">艺术特长：</view>
				<textarea class="form-textarea" v-model="formData.artSkills" placeholder="比如乐器、唱歌、画画、舞蹈等"></textarea>
			</view>
			
			<view class="form-item">
				<view class="form-label">其他特长：</view>
				<textarea class="form-textarea" v-model="formData.otherSkills" placeholder="比如英语口语、中英文演讲、编程等"></textarea>
			</view>
			
			<view class="form-item">
				<view class="form-label">综合描述：</view>
				<textarea class="form-textarea" v-model="formData.description" placeholder="你想通过大学获得什么？"></textarea>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-buttons">
			<view class="generate-btn" @click="generateReport">
				<text>AI生成报告</text>
			</view>
			<view class="next-btn" @click="nextStep">
				<text>下一步</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				formData: {
					sportsSkills: '',
					artSkills: '',
					otherSkills: '',
					description: ''
				}
			}
		},
		computed: {
			...mapState(['System_height'])
		},
		onLoad() {
			// 加载之前保存的数据
			const savedData = uni.getStorageSync('selectSchoolStep5')
			if (savedData) {
				this.formData = { ...this.formData, ...savedData }
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			generateReport() {
				// 保存当前步骤数据
				uni.setStorageSync('selectSchoolStep5', this.formData)
				
				// 跳转到生成页面
				uni.navigateTo({
					url: '/subpkg/select_school/generating/generating'
				})
			},
			nextStep() {
				// 保存当前步骤数据
				uni.setStorageSync('selectSchoolStep5', this.formData)
				
				// 跳转到生成页面
				uni.navigateTo({
					url: '/subpkg/select_school/generating/generating'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		min-height: 100vh;
		background: linear-gradient(180deg, #CBF2E0 0%, #F6F7FB 43.3%);
		padding-bottom: 160rpx;
	}
	
	.content_header {
		position: relative;
		height: 140rpx;
		background: #00C2A0;
		
		.nav-title {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			z-index: 10;
			
			text {
				font-size: 34rpx;
				color: #2D2D2D;
				font-weight: 400;
			}
		}
		
		.back-left {
			position: absolute;
			left: 30rpx;
			z-index: 10;
		}
	}
	
	.notification {
		display: flex;
		align-items: center;
		margin: 30rpx;
		padding: 14rpx 28rpx;
		background: #F5FFFD;
		border-radius: 31rpx;
		
		.notification-icon {
			width: 49rpx;
			height: 49rpx;
			margin-right: 28rpx;
		}
		
		.notification-text {
			font-size: 28rpx;
			color: #5A5A5A;
			flex: 1;
		}
	}
	
	.step-indicator {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 30rpx 0;
		
		.step-item {
			margin: 0 162rpx;
			
			.step-number {
				width: 43rpx;
				height: 43rpx;
				border-radius: 50%;
				background: #FF9B3A;
				color: #FFFFFF;
				font-size: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			&.active .step-number {
				background: #FF9B3A;
			}
		}
	}
	
	.form-title {
		position: relative;
		margin: 30rpx;
		
		.title-bg {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 92rpx;
			height: 19rpx;
			background: #DBFF9C;
		}
		
		.title-text {
			font-size: 30rpx;
			color: #060606;
			font-weight: 400;
		}
	}
	
	.ai-section {
		padding: 30rpx 100rpx;
		
		.ai-container {
			position: relative;
			height: 545rpx;
			
			.ai-bg-4 {
				position: absolute;
				top: 0;
				left: 0;
				width: 551rpx;
				height: 545rpx;
				z-index: 1;
			}
			
			.ai-bg-1 {
				position: absolute;
				top: 22rpx;
				left: 20rpx;
				width: 510rpx;
				height: 508rpx;
				z-index: 2;
			}
			
			.ai-bg-2 {
				position: absolute;
				top: 68rpx;
				left: 27rpx;
				width: 494rpx;
				height: 419rpx;
				z-index: 3;
			}
			
			.ai-center {
				position: absolute;
				top: 126rpx;
				left: 120rpx;
				width: 306rpx;
				height: 306rpx;
				z-index: 4;
				
				.ai-main {
					width: 100%;
					height: 100%;
				}
				
				.ai-text {
					position: absolute;
					top: 77rpx;
					left: 40rpx;
					width: 227rpx;
					height: 154rpx;
				}
			}
		}
	}
	
	.form-content {
		padding: 0 30rpx;
		
		.form-item {
			background: #FFFFFF;
			border-radius: 16rpx;
			padding: 28rpx;
			margin-bottom: 30rpx;
			
			.form-label {
				font-size: 30rpx;
				color: #504E4E;
				margin-bottom: 20rpx;
			}
			
			.form-textarea {
				width: 100%;
				font-size: 28rpx;
				color: #989898;
				min-height: 80rpx;
				line-height: 1.5;
			}
		}
	}
	
	.bottom-buttons {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #F6F7FB;
		padding: 30rpx 100rpx 60rpx;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		
		.generate-btn {
			width: 100%;
			height: 80rpx;
			background: linear-gradient(135deg, #26C8AC 0%, #19C990 100%);
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			text {
				font-size: 30rpx;
				color: #FFFFFF;
				font-weight: 400;
			}
		}
		
		.next-btn {
			width: 100%;
			height: 81rpx;
			background: linear-gradient(135deg, #26C8AC 0%, #19C990 100%);
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			text {
				font-size: 30rpx;
				color: #FFFFFF;
				font-weight: 400;
			}
		}
	}
</style>
