@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container .header {
  margin-bottom: 20rpx;
  background-color: #fff;
  /* 移除原有的背景色，使用容器的背景色 */
}
.container .header .nav-bar {
  width: 100vw;
  height: 88rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  color: #fff;
}
.container .header .nav-bar .back-icon {
  position: absolute;
  left: 30rpx;
}
.container .header .nav-bar .list {
  font-weight: 600;
}
.container .header .nav-bar .title {
  flex: 1;
  text-align: center;
  font-weight: bold;
  font-size: 34rpx;
  color: #2D2D2D;
}
.container .content {
  background: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.container .right {
  width: 350rpx;
  height: 314rpx;
  margin-top: 170rpx;
}
.container .success {
  font-weight: bold;
  font-size: 34rpx;
  color: #404040;
  margin: 70rpx 0;
}
.container .btn-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
.container .btn-box .btns {
  width: 190rpx;
  height: 66rpx;
  line-height: 66rpx;
  text-align: center;
  font-weight: bold;
  font-size: 30rpx;
  color: #1BB394;
  border-radius: 40rpx;
  border: 1rpx solid #1BB394;
}
.container .btn-box .btn {
  width: 204rpx;
  height: 66rpx;
  line-height: 66rpx;
  text-align: center;
  font-weight: bold;
  font-size: 30rpx;
  color: #1BB394;
  border-radius: 40rpx;
  border: 1rpx solid #1BB394;
}
.container .btn-box .btn:first-child {
  margin-right: 170rpx;
}

