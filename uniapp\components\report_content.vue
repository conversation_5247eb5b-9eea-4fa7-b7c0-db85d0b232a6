<template>
	<view class="container">
		<view class="info" v-for="(item, index) in formattedData" :key="index">
			<view class="info-title" v-if="item.content">
				<image class="logo" src="@/static/rhomb.png" mode=""></image>
				<text class="title-text">
					{{ item.title }}
				</text>
			</view>
			<view class="info-content">
				<rich-text :nodes="item.content"></rich-text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formattedData: []
			}
		},
		props: {
			msgtype: {
				type: Array,
				default: () => []
			}
		},
		watch: {
			msgtype: {
				handler(newVal) {
					this.formattedData = newVal.map(item => ({
						title: item.label,
						content: item.content
					}))
				},
				immediate: true
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background: linear-gradient(180deg, rgba(203, 242, 224, 0.2) 0%, rgba(255, 255, 255, 0) 20%);
		border-radius: 14rpx;
		border: 2rpx dashed #1BB394;
		padding: 24rpx 28rpx;

		.info {
			padding-top: 30rpx;

			&:first-child {
				padding-top: 0;
			}

			.info-title {
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.logo {
					width: 32rpx;
					height: 24rpx;
					margin-right: 8rpx;
				}

				.title-text {
					font-weight: 800;
					font-size: 16rpx;
					color: #4C5370;
				}

			}

			.info-content {
				font-weight: 400;
				font-size: 16rpx;
				color: #504E4E;
				line-height: 24rpx;
			}
		}
	}
</style>