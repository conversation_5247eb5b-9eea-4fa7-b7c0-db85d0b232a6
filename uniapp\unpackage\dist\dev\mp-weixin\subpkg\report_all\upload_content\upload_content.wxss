@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-5a243c4a {
  padding: 30px;
  box-sizing: border-box;
}
.container .value-container.data-v-5a243c4a {
  width: 100%;
  height: 241rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 15px;
  box-sizing: border-box;
  margin-bottom: 30px;
}
.container .value-container .value-input.data-v-5a243c4a {
  border: 0;
  border-radius: 8rpx;
  outline: none;
}
.container .value-container .value-input.data-v-5a243c4a:focus {
  border-color: #1BB394;
}
.container .value-container.data-v-5a243c4a  .checkbox__inner {
  border-radius: 16rpx !important;
}
.container .image-content.data-v-5a243c4a {
  width: 100%;
  padding: 20rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
}
.container .image-content.data-v-5a243c4a .u-upload__wrap__preview {
  width: 180rpx !important;
  height: 180rpx !important;
  border-radius: 12rpx !important;
}
.container .image-content.data-v-5a243c4a .u-icon--right,
.container .image-content.data-v-5a243c4a .u-upload__button__text {
  font-size: 30rpx !important;
  border-radius: 12rpx !important;
}
.container .image-content.data-v-5a243c4a .uicon-camera-fill {
  font-size: 80rpx !important;
  line-height: 80rpx !important;
}
.container .btn.data-v-5a243c4a {
  width: 469rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: bold;
  font-size: 30rpx;
  color: #FFFFFF;
  position: absolute;
  bottom: 50rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  border-radius: 40rpx;
}

