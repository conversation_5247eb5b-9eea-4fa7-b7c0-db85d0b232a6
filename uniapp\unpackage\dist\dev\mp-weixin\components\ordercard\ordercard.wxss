@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box {
  padding: 30rpx 0;
  display: flex;
  border-bottom: 1rpx solid #E6E6E6;
}
.box .box_left .box_left_img {
  width: 220rpx;
  height: 164rpx;
  position: relative;
}
.box .box_left .box_left_img image {
  border-radius: 11rpx;
}
.box .box_left .box_left_img .box_left_img_text {
  width: 63rpx;
  height: 36rpx;
  background: #05B6F6;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #FDFEFF;
  line-height: 36rpx;
  text-align: center;
  padding: 3rpx 5rpx;
  position: absolute;
  top: -10rpx;
  right: -5rpx;
}
.box .box_right {
  width: 300rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 15rpx;
}
.box .box_right .box_right_title {
  font-weight: bold;
  font-size: 28rpx;
  color: #414141;
  display: inline-block;
  margin-bottom: 15rpx;
}
.box .box_right .box_right_ranking {
  width: 182rpx;
  height: 34rpx;
  background: #ECF3FF;
  border-radius: 6rpx;
  line-height: 34rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #05B6F6;
  margin-bottom: 20rpx;
}
.box .box_right .box_right_sell {
  font-weight: 400;
  font-size: 22rpx;
  color: #777777;
  margin-bottom: 20rpx;
}
.box .box_right .box_right_price {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.box .box_right .box_right_price .box_right_price_left {
  font-weight: 500;
  font-size: 30rpx;
  color: #FB4E44;
}
.box .box_right .box_right_price .box_right_price_right_1 {
  width: 92rpx;
  height: 45rpx;
  background: #00C2A0;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 45rpx;
  text-align: center;
  position: relative;
}
.box .box_right .box_right_price .box_right_price_right {
  width: 101rpx;
  height: 47rpx;
  background: #05B6F6;
  border-radius: 17rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #FDFEFF;
  line-height: 47rpx;
  text-align: center;
  position: relative;
}
.box .box_right .box_right_price .box_right_price_right .box_right_price_right_count {
  width: 30rpx;
  height: 30rpx;
  background: #F65329;
  border-radius: 50%;
  text-align: center;
  line-height: 30rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: #FFF8F6;
  position: absolute;
  top: -10rpx;
  right: -10rpx;
}

