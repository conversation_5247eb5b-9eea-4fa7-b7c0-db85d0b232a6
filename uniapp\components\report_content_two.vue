<template>
	<view class="container">
		<view class="info" v-for="(item, index) in planningList" :key="index">
			<view class="info-title">
				<image class="logo" src="@/static/rhomb.png" mode=""></image>
				<text class="title-text">
					{{ item.title }}
				</text>
			</view>
			<view class="info-content" v-for="(obj, key) in item.content" :key="key">
				<view class="content-title">
					<view class="cricle">

					</view>
					<view class="content-title-text">
						{{ obj.title }}
					</view>
				</view>

				<view class="subtitle-content-container">
					<view class="subtitle-content" v-for="(value, k) in obj.list" :key="k">
						<view class="subtitle-text">
							{{ value.name | formatName }}
						</view>
						<view class="subtitle-content-text">
							<rich-text :nodes="value.content"></rich-text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import sseEvent from "@/utils/sse_event.js"
	import {
		getReportTagList
	} from "@/api/user.js"
	export default {
		props: {
			list: {
				type: Array,
				required: true,
				default: () => []
			},
		},
		computed: {
			planningList() {
				console.log("list", this.list)
				return this.list
			}
		},
		created() {

		},
		filters: {
			formatName(name) {
				if (name == "jyzb_zonghe") {
					return "综合描述"
				}
				if (name == "zzsh_zonghe") {
					return "综合推荐"
				}
				return name
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background: linear-gradient(180deg, rgba(203, 242, 224, 0.2) 0%, rgba(255, 255, 255, 0) 20%);
		border-radius: 14rpx;
		border: 2rpx dashed #1BB394;
		padding: 24rpx 28rpx;

		.info {
			padding-top: 30rpx;

			.info-title {
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.logo {
					width: 32rpx;
					height: 24rpx;
					margin-right: 8rpx;
				}

				.title-text {
					font-weight: 800;
					font-size: 16rpx;
					color: #4C5370;
				}

			}

			.info-content {
				margin-top: 20rpx;

				.content-title {
					display: flex;
					justify-content: flex-start;
					align-items: center;

					.cricle {
						width: 6rpx;
						height: 6rpx;
						border-radius: 50%;
						background-color: #AEE8CD;
						margin-right: 10rpx;
					}

					.content-title-text {
						font-weight: bold;
						font-size: 16rpx;
						color: #5A5A5A;
					}

				}

				.subtitle-content {
					margin-top: 20rpx;
				}

				.subtitle-content-container {
					margin-top: 20rpx;
					border-radius: 9rpx;
					border: 1rpx solid #A0E4C4;
					padding: 28rpx;
					padding-top: 0;

					.subtitle-text {
						display: inline-block;
						padding: 0 6rpx;
						border-radius: 4rpx 4rpx 4rpx 4rpx;
						border: 1rpx solid #FF9B3A;
						font-weight: 400;
						font-size: 16rpx;
						line-height: 32rpx;
						text-align: center;
						color: #FF9B3A;
					}

					.subtitle-content-text {
						margin-top: 10rpx;
						font-weight: 400;
						font-size: 16rpx;
						color: #504E4E;
						line-height: 24rpx;
					}
				}
			}
		}
	}
</style>