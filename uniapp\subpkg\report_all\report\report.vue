<template>
	<view class="tmp-container">
		<view class="container" :class="current > 1 ? 'container-bg' : ''">
			<!-- 顶部标题 -->
			<view class="header">
				<view class="top">
					<uni-icons @tap="back" class="left-icon" type="left" size="20"></uni-icons>
					<text class="main-title">大学生涯规划报告</text>
				</view>

				<view class="alert-msg">
					<image class="sound-img" src="@/static/sound.png"></image>
					<text class="info">请认真完善信息，以便于精准生成报告！</text>
				</view>
			</view>

			<!-- 步骤导航 -->
			<view class="steps">
				<view class="step active">1</view>
				<view class="step-line" :class="current > 0 ? 'line-bg' : ''"></view>
				<view class="step active ">2</view>
				<view class="step-line" :class="current > 1 ? 'line-bg' : ''"></view>
				<view class="step active">3</view>
			</view>

			<view class="tip" :style="{ visibility: showMask || showSearchList ? 'hidden' : 'visible' }">
				<text class="tip-text">{{ title[current] }}</text>
			</view>



			<!-- 表单内容 -->
			<swiper class="swiper" :current="current"
				:style='{ height: (current == 0 || current == 2) ? "75vh" : "95vh" }' :disable-touch="true">
				<swiper-item>
					<view class="form">
						<view class="form-item">
							<text class="label"><text class="red">*</text>学生姓名</text>
							<input class="value" style="text-align: right;" v-model.trim="stuInfo.name"
								placeholder="请输入姓名" />
						</view>
						<view class="form-item">
							<text class="label">性别</text>
							<picker mode="selector" :range="genderOptions" @change="onGenderChange">
								<view class="right">
									<view class="value">
										<view class="picker" v-if="stuInfo.gender ==0">
											未知
										</view>
										<view class="picker" v-if="stuInfo.gender == 2">
											女
										</view>
										<view class="picker" v-if="stuInfo.gender == 1">
											男
										</view>

									</view>
									<uni-icons type="right" size="20" color="#C3C3C3"></uni-icons>

								</view>
							</picker>
						</view>
						<view class="form-item">
							<text class="label"><text class="red">*</text>本科入学年份</text>
							<view class="right">
								<picker mode="selector" :range="yearOptions" @change="onYearChange">
									<text class="value">{{ stuInfo.joinYear }}</text>
									<uni-icons type="right" size="20" color="#C3C3C3"></uni-icons>
								</picker>
							</view>

						</view>
						<view class="form-item">
							<text class="label"><text class="red">*</text>本科院校</text>
							<view class="right" @click="searchSchool">
								<text class="value">{{ stuInfo.schoolName }}</text>
								<uni-icons type="right" size="20" color="#C3C3C3"></uni-icons>
							</view>
						</view>

						<view class="form-item">
							<text class="label"><text class="red">*</text>本科专业</text>
							<view class="right" @click="searchMajor">
								<text class="value">{{ stuInfo.majorName }}</text>
								<uni-icons type="right" size="20" color="#C3C3C3"></uni-icons>
							</view>
						</view>
						<view class="form-item-box">
							<view class="form-item">
								<text class="label">本科学院</text>
								<view class="right" @click="searchCollege">
									<text class="value">{{ stuInfo.collegeNames }}</text>
									<uni-icons type="right" size="20" color="#C3C3C3"></uni-icons>
								</view>
							</view>
							<text class="cursor" @click="cursorClick">点击手动添加本科院校</text>
						</view>
						<view class="form-item" v-if="cursorFlag">
							<text class="label">手动添加本科学院</text>
							<input class="value" style="text-align: right;" @blur="collegeBlur"
								v-model="stuInfo.collegeName" placeholder="请输入本科学院" />
						</view>
						<view class="form-item">
							<text class="label">学生性格</text>
							<view class="value">
								<uni-data-checkbox v-model="stuInfo.personality" mode="button" selectedColor="#1BB394"
									:localdata="sex1"></uni-data-checkbox>
							</view>
						</view>
						<view class="form-item">
							<text class="label"><text class="red">*</text>职业发展</text>
							<view class="value career">
								<uni-data-checkbox :multiple="true" v-model.trim="stuInfo.postGraduation" mode="button"
									selectedColor="#1BB394" :localdata="occupationData"></uni-data-checkbox>
							</view>
						</view>
					</view>
				</swiper-item>
				<swiper-item>

					<view class="form">
						<view class="form-item">
							<text class="label"><text class="red">*</text>总分</text>
							<input placeholder="请输入总分(必填)0-750" type="number" class="value"
								v-model="stuInfo.totalScore" />
						</view>
						<view class="form-item">
							<text class="label">位次</text>
							<input placeholder="请输入位次(选填)" type="number" class="value" v-model="stuInfo.position" />
						</view>

						<view class="form-item">
							<text class="label">语文</text>
							<input placeholder="请输入语文分数(选填)0-150" type="number" class="value"
								v-model="stuInfo.chineseScore" />
						</view>
						<view class="form-item">
							<text class="label"><text class="red">*</text>数学</text>
							<input placeholder="请输入数学分数(必填)0-150" type="number" class="value"
								v-model="stuInfo.mathScore" />
						</view>

						<view class="form-item">
							<text class="label"><text class="red">*</text>外语</text>
							<input placeholder="请输入外语分数(必填)0-150" type="number" class="value"
								v-model="stuInfo.foreignLangScore" />
						</view>

						<view class="form-item">
							<text class="label">物理</text>
							<input placeholder="请输入物理分数(选填)0-100" type="number" class="value"
								v-model="stuInfo.physicsScore" />
						</view>


						<view class="form-item">
							<text class="label">化学</text>
							<input placeholder="请输入化学分数(选填)0-100" type="number" class="value"
								v-model="stuInfo.chemistryScore" />
						</view>

						<view class="form-item">
							<text class="label">生物</text>
							<input placeholder="请输入生物分数(选填)0-100" type="number" class="value"
								v-model="stuInfo.biologyScore" />
						</view>

						<view class="form-item">
							<text class="label">政治</text>
							<input placeholder="请输入政治分数(选填)0-100" type="number" class="value"
								v-model="stuInfo.politicsScore" />
						</view>

						<view class="form-item">
							<text class="label">历史</text>
							<input placeholder="请输入历史分数(选填)0-100" type="number" class="value"
								v-model="stuInfo.historyScore" />
						</view>

						<view class="form-item">
							<text class="label">地理</text>
							<input placeholder="请输入地理分数(选填)0-100" type="number" class="value"
								v-model="stuInfo.geographyScore" />
						</view>
					</view>

				</swiper-item>


				<swiper-item>

					<view class="form">
						<view class="form-item-step-three">
							<text class="label">体育特长：</text>
							<view class="value-container">
								<textarea maxlength='100' v-model="stuInfo.sportsInterest" class="value-input"
									placeholder="比如篮球、足球、游泳等" />
							</view>
						</view>
						<view class="form-item-step-three">
							<text class="label">艺术特长：</text>
							<view class="value-container">
								<textarea maxlength='100' v-model="stuInfo.artInterest" class="value-input"
									placeholder="比如乐器、唱歌、画画、舞蹈等" />
							</view>
						</view>
						<view class="form-item-step-three">
							<text class="label">其他特长：</text>
							<view class="value-container">
								<textarea maxlength='100' v-model="stuInfo.academicInterest" class="value-input"
									placeholder="比如英语口语、中英文演讲、编程等" />
							</view>
						</view>

						<view class="form-item-step-three">
							<text class="label">综合描述：</text>
							<view class="value-container">
								<textarea maxlength='100' v-model="stuInfo.collegePlan" class="value-input"
									placeholder="你想通过大学获得什么？" />
							</view>
						</view>
					</view>

				</swiper-item>



			</swiper>
			<!-- 底部按钮 -->
			<view class="footers" v-if="current === 0">
				<button class="next-button" @click="next">下一步</button>
			</view>
			<view class="footer" v-else>
				<view style="width: 305rpx;" :class="[current==2 ? 'white' : 'prev-button']" @click="prev">上一步</view>
				<view style="width: 305rpx;" class="next-button" @click="next">下一步</view>
			</view>


		</view>

		<view class="mask" v-if="showMask" @click="closeMask">

			<view class="bg-container" @click.stop>
				<view class="ai3-bg-container">
					<view class="ai3-bg" :class='{ "ai3-bg-animation": showAnimation }'>
					</view>
				</view>
				<view class="ai2-bg-container">
					<view class="ai2-bg" :class='{ "ai2-bg-animation": showAnimation }'>
					</view>
				</view>

				<view class="ai-bg">
				</view>
			</view>

			<button :loading='showAnimation' class="submit" @click="submit">
				AI生成报告
			</button>

		</view>

		<view class="mask" v-if="showSearchList">
			<search-list :typeNum="typeNum" @choose='choose' @closeSerachList='closeSerachList'
				:url="searchListUrl"></search-list>
		</view>

	</view>
</template>

<script>
	import {
		mapState
	} from "vuex"
	import moment from "moment"
	import searchList from "@/components/search_list/search_list.vue";
	import {
		prepareReport
	} from "@/api/user.js"
	export default {
		data() {
			return {
				showAnimation: false,
				showMask: false,
				genderOptions: ['未知', '男', '女'],
				yearOptions: this.getYearOptions(),
				selectedYear: moment().year(),
				selectedGender: '',
				current: 0,
				searchListUrl: '',
				showSearchList: false,
				stuInfo: {
					name: "",
					schoolName: '请选择本科院校',
					schoolId: 0,
					majorName: '请选择专业',
					majorId: 0,
					collegeId: '',
					collegeNames: '请选择本科学院',
					collegeName: '',
					gender: 0,
					joinYear: '',
					personality: 0,
					postGraduation: [],
					totalScore: "",
					// rank: "",
					position: "",
					chineseScore: "",
					mathScore: "",
					foreignLangScore: "",
					physicsScore: "",
					chemistryScore: "",
					biologyScore: "",
					politicsScore: '',
					historyScore: "",
					geographyScore: "",
					sportsInterest: '',
					artInterest: '',
					academicInterest: '',
					collegePlan: ''
				},
				sex1: [{
						text: '外向',
						value: 1,
					},
					{
						text: '内向',
						value: 2
					},
				],
				title: [
					'个人基础信息',
					'高考成绩信息',
					'兴趣类'
				],
				occupationData: [{
						text: '保研',
						value: "1"
					}, {
						text: '考研',
						value: "2",
					},
					{
						text: '留学',
						value: "3",
					},
					{
						text: '读博',
						value: "4",
					},
					{
						text: '就业',
						value: "5",
					},
					{
						text: '创业',
						value: "6",
					}

				],
				searchFlag: '',
				cursorFlag: false,
				typeNum: 1, //1是院校  2是专业  3是学院
			}
		},
		created() {
			// if (this.$store.state.user.userInfo.name) {
			// 	this.stuInfo = {
			// 		...this.$store.state.user.userInfo
			// 	}
			// }
			// let formData = this.$store.state.user.userInfo
			// for (const key in this.$store.state.user.userInfo) {
			// 	if (formData[key] != null && formData[key] != undefined) {
			// 		this.stuInfo[key] = formData[key]
			// 	}
			// 	this.stuInfo.collegeNames = formData.collegeName
			// 	this.stuInfo.collegeName = ''
			// }

		},
		methods: {
			closeMask() {
				this.showMask = false;
			},
			// 手动输入本科学院输入框失去焦点
			collegeBlur() {
				if (this.stuInfo.collegeName) {
					this.stuInfo.collegeId = ''
					this.stuInfo.collegeNames = '请选择本科学院'
				}
			},
			// 点击手动添加本科学院
			cursorClick() {
				this.cursorFlag = !this.cursorFlag
			},
			getYearOptions() {
				const currentYear = new Date().getFullYear();
				return Array.from({
					length: 5
				}, (_, index) => currentYear - index + '');
			},
			next() {
				if (this.current == 2) {
					// const {sportsInterest, artInterest, academicInterest, collegePlan} = this.stuInfo
					// const validateTextLength = (text) => {
					//   return text !== "" && text.length <= 100;
					// };

					// if (!validateTextLength(sportsInterest)) {
					//   return uni.tip("体育特长必须填写且不超过100个字符");
					// }
					// if (!validateTextLength(artInterest)) {
					//   return uni.tip("艺术特长必须填写且不超过100个字符");
					// }
					// if (!validateTextLength(academicInterest)) {
					//   return uni.tip("学术/其他特长必须填写且不超过100个字符");
					// }
					// if (!validateTextLength(collegePlan)) {
					//   return uni.tip("综合描述必须填写且不超过100个字符");
					// }
					this.showMask = true
				} else {
					if (this.current == 0) {
						if (this.stuInfo.name == "") {
							return uni.tip("姓名不能为空")
						}
						if (this.stuInfo.gender == -1) {
							return uni.tip("请选择性别")
						}
						if (this.stuInfo.joinYear == "") {
							return uni.tip("请选择入学年份")
						}
						if (this.stuInfo.schoolId == 0) {
							return uni.tip("请选择院校")
						}
						if (this.stuInfo.majorId == 0) {
							return uni.tip("请选择专业")
						}
						// if (this.stuInfo.personality == 0) {
						// 	return uni.tip("请选择性格")
						// }
						if (this.stuInfo.postGraduation.length == 0) {
							return uni.tip("请选择职业发展")
						}

					}

					if (this.current == 1) {
						const {
							totalScore,
							rank,
							position,
							chineseScore,
							mathScore,
							foreignLangScore,
							physicsScore,
							chemistryScore,
							biologyScore,
							historyScore,
							politicsScore,
							geographyScore
						} = this.stuInfo;

						// 验证总分
						if (totalScore == "" || totalScore < 0 || totalScore > 750) {
							return uni.tip("请输入正确的总分");
						}

						// 验证必填项：rank, position, chineseScore, mathScore, foreignLangScore
						// if (position <= 0) {
						// 	return uni.tip("位次必须大于0");
						// }
						// if (chineseScore == "" || chineseScore < 0 || chineseScore > 150) {
						// 	return uni.tip("语文成绩必须在0到150之间");
						// }
						if (mathScore == "" || mathScore < 0 || mathScore > 150) {
							return uni.tip("数学成绩必须在0到150之间");
						}
						if (foreignLangScore == "" || foreignLangScore < 0 || foreignLangScore > 150) {
							return uni.tip("外语成绩必须在0到150之间");
						}

						// 验证选填科目（如果填写了分数）
						const validateScore = (score) => {
							return score === "" || (score >= 0 && score <= 100);
						};
						const chineseValidate = (score) => {
							return score === "" || (score >= 0 && score <= 150);
						};
						if (position !== "" && position < 0) {
							return uni.tip("位次必须大于0");
						}
						if (chineseScore !== "" && !chineseValidate(chineseScore)) {
							return uni.tip("语文成绩应在0到150之间");
						}
						if (physicsScore !== "" && !validateScore(physicsScore)) {
							return uni.tip("物理成绩应在0到100之间");
						}
						if (chemistryScore !== "" && !validateScore(chemistryScore)) {
							return uni.tip("化学成绩应在0到100之间");
						}
						if (biologyScore !== "" && !validateScore(biologyScore)) {
							return uni.tip("生物成绩应在0到100之间");
						}
						if (politicsScore !== "" && !validateScore(politicsScore)) {
							return uni.tip("政治成绩应在0到100之间");
						}
						if (historyScore !== "" && !validateScore(historyScore)) {
							return uni.tip("历史成绩应在0到100之间");
						}
						if (geographyScore !== "" && !validateScore(geographyScore)) {
							return uni.tip("地理成绩应在0到100之间");
						}

						// // 计算所有分数的总和
						// let totalCalculatedScore = 0;
						// totalCalculatedScore += chineseScore ? parseInt(chineseScore * 10) : 0;
						// totalCalculatedScore += mathScore ? parseInt(mathScore * 10) : 0;
						// totalCalculatedScore += foreignLangScore ? parseInt(foreignLangScore * 10) : 0;
						// totalCalculatedScore += physicsScore ? parseInt(physicsScore * 10) : 0;
						// totalCalculatedScore += chemistryScore ? parseInt(chemistryScore * 10) : 0;
						// totalCalculatedScore += biologyScore ? parseInt(biologyScore * 10) : 0;
						// totalCalculatedScore += historyScore ? parseInt(historyScore * 10) : 0;
						// totalCalculatedScore += geographyScore ? parseInt(geographyScore * 10) : 0;

						// // 验证总分是否匹配
						// if (totalCalculatedScore != totalScore * 10) {
						// 	return uni.tip("各科成绩的总和应等于总分");
						// }

					}

					this.current = this.current + 1 > 2 ? 2 : this.current + 1
				}
			},
			prev() {
				this.current = this.current - 1 < 0 ? 0 : this.current - 1
			},
			back() {
				// uni.navigateBack()
				if (this.current == 0) {
					uni.navigateTo({
						url: '/subpkg/report_all/index/index'
					})
				} else {
					this.current = this.current - 1 < 0 ? 0 : this.current - 1
				}

			},
			onGenderChange(e) {
				//更新选择的性别
				this.stuInfo.gender = e.detail.value
			},
			onYearChange(e) {
				this.stuInfo.joinYear = this.yearOptions[e.detail.value]
			},
			searchSchool() {
				this.typeNum = 1
				this.showSearchList = true
				this.searchFlag = 'school'
				this.searchListUrl = '/stu/school/fetchBy'
			},
			searchMajor() {
				if (this.stuInfo.schoolId == 0) {
					return uni.tip("请先选择院校")
				}
				this.typeNum = 2
				this.showSearchList = true
				this.searchFlag = 'major'
				this.searchListUrl = '/stu/school/fetchMajorBy?schoolId=' + this.stuInfo.schoolId
			},
			searchCollege() {

				if (this.stuInfo.schoolId == 0) {
					return uni.tip("请先选择院校")
				}
				this.typeNum = 3
				this.showSearchList = true
				this.searchFlag = 'college'
				this.searchListUrl = '/stu/school/fetchCollegeBy?schoolId=' + this.stuInfo.schoolId
			},
			closeSerachList() {
				this.showSearchList = false
				this.searchFlag = ''
				this.searchListUrl = ''
			},
			choose(item) {
				switch (this.searchFlag) {
					case 'school':
						this.stuInfo.schoolName = item.name
						this.stuInfo.schoolId = item.id
						break;
					case 'major':
						this.stuInfo.majorName = item.name
						this.stuInfo.majorId = item.id
						// const url = '/stu/school/fetchCollegeBy?schoolId=' + this.stuInfo.schoolId;
						// uni.http.get(url).then(res => {
						// 	if (res.errCode == 0 && typeof res.data.data[0] != 'undefined') {
						// 		this.stuInfo.collegeId = res.data.data[0].id
						// 		this.stuInfo.collegeName = res.data.data[0].name
						// 	}
						// })
						break;
					case 'college':
						this.stuInfo.collegeNames = item.name
						this.stuInfo.collegeId = item.id
						this.stuInfo.collegeName = ''
						// const url = '/stu/school/fetchCollegeBy?schoolId=' + this.stuInfo.schoolId;
						// uni.http.get(this.searchListUrl).then(res => {
						// 	if (res.errCode == 0 && typeof res.data.data[0] != 'undefined') {
						// 		this.stuInfo.collegeId = res.data.data[0].id
						// 		this.stuInfo.collegeName = res.data.data[0].name
						// 	}
						// })
						break;
				}

				this.closeSerachList()
			},
			async submit() {

				//测试代码，正式生成替换成 this.stuInfo
				// const data = {
				// 	"name": "陈测试",
				// 	"schoolName": "合肥工业大学",
				// 	"schoolId": 71,
				// 	"majorName": "金融工程",
				// 	"majorId": 249,
				// 	"collegeId": 1313,
				// 	"collegeName": "经济学院",
				// 	"gender": 1,
				// 	"joinYear": 2026,
				// 	"personality": 2,
				// 	"postGraduation": [
				// 		3,
				// 		6
				// 	],
				// 	"totalScore": "600",
				// 	"rank": "15000",
				// 	"position": "11000",
				// 	"chineseScore": "120",
				// 	"mathScore": "120",
				// 	"foreignLangScore": "120",
				// 	"physicsScore": "80",
				// 	"chemistryScore": "80",
				// 	"biologyScore": "80",
				// 	"politicsScore": "",
				// 	"historyScore": "",
				// 	"geographyScore": "",
				// 	"sportsInterest": "篮球",
				// 	"artInterest": "唱歌",
				// 	"academicInterest": "没有",
				// 	"collegePlan": "有留学的想法，想在大学时候开始准备雅思，为了留学做打算，同时注重大学期间的基础考试， 类似四级、六级."
				// }

				// data['postGraduationLabel'] = this.occupationData
				// 	.filter(item => data.postGraduation.includes(item.value))
				// 	.map(item => item.label)
				// 	.join(',')


				this.stuInfo['postGraduationLabel'] = this.occupationData
					.filter(item => this.stuInfo.postGraduation.includes(item.value))
					.map(item => item.text)
					.join(',')
				try {
					this.showAnimation = true
					const result = await prepareReport(this.stuInfo)
					// const result = await prepareReport(data)
					if (result.errCode == 0) {
						//记录报告信息
						this.$store.commit('user/setReportInfo', result.data);
						//记录报告的用户信息
						this.$store.commit('user/setReportUserInfo', this.stuInfo);
						// this.$store.commit('user/setReportUserInfo', data);
						uni.navigateTo({
							url: '/subpkg/report_all/plan/plan'
						})
						this.$store.dispatch('user/getReportData')
						this.showMask = false
					} else {
						uni.tip(result.msg)
					}

				} catch (e) {
					console.error(e)
				} finally {
					this.showAnimation = false
				}
			}
		},
		computed: {
			...mapState('user', ['userInfo'])
		},
		components: {
			searchList
		}
	};
</script>

<style scoped lang="scss">
	/* 容器样式 */
	.container {
		display: flex;
		flex-direction: column;
		background: linear-gradient(180deg, #CBF2E0 0%, #EEF7F6 30%, #FFFFFF 61%, #FFFFFF 100%);
		height: 100%;
		padding: 0 32rpx;
		min-height: 100vh;
		padding-bottom: 20px;
	}

	.container-bg {
		background: linear-gradient(180deg, #CBF2E0 0%, #EEF7F6 30%, #F6F7FB 100%);
	}

	.swiper {
		height: 68vh;
	}

	/* 顶部标题样式 */
	.header {
		margin-top: 70rpx;
		text-align: center;
		margin-bottom: 30rpx;
		display: flex;
		flex-direction: column;
	}

	.top {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
	}

	.left-icon {
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
	}

	.alert-msg {
		margin-top: 28rpx;
		padding: 10rpx 0;
		padding-left: 20rpx;
		border-radius: 16rpx;
		background-color: #F5FFFD;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		font-weight: 400;
		font-size: 28rpx;
		color: #5A5A5A;

		.info {
			margin-left: 4rpx;
		}
	}

	.sound-img {
		width: 50rpx;
		height: 50rpx;
	}

	.main-title {
		font-weight: bold;
		font-size: 34rpx;
		color: #2D2D2D;
	}

	.subtitle {
		font-size: 26rpx;
		color: #999;
		margin-top: 10rpx;
	}

	/* 步骤导航样式 */
	.steps {
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	.tip {
		margin-top: 36rpx;

		.tip-text {
			position: relative;
			display: inline-block;
			font-weight: 800;
			font-size: 30rpx;
			color: #060606;
			z-index: 100;
		}

		.tip-text::after {
			content: "";
			position: absolute;
			bottom: -6rpx;
			left: 0;
			width: 100%;
			height: 20rpx;
			/* 指定高度 */
			background-color: #DBFF9C;
			/* 底部背景颜色 */
			z-index: -1;
		}
	}

	.step {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		background-color: #ccc;
		text-align: center;
		line-height: 40rpx;
		color: #fff;
		font-size: 24rpx;
		gap: 0;
	}

	.step.active {
		background-color: #FF9D3E;
		width: 43rpx;
		height: 43rpx;
	}

	.step-line {
		flex: 1;
		height: 20rpx;
		background-color: #fff;
	}

	.line-bg {
		background-color: #FFBD3A;
	}

	/* 表单样式 */
	.form {
		background-color: transparent;
		border-radius: 10rpx;
		padding: 20rpx 0;
	}

	.form-item-box {
		display: flex;
		flex-direction: column;
		min-height: 106rpx;
		border-bottom: 1px solid #eee;

		.form-item {
			min-height: 80rpx;
			border-bottom: none;
			margin-top: 20rpx;
		}

		.cursor {
			color: #f56c6c;
			font-size: 22rpx;
		}
	}

	.form-item {
		display: flex;
		min-height: 106rpx;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid #eee;

		.right {
			display: flex;
			align-items: center;
			justify-content: flex-end;
		}
	}


	.label {
		min-width: 140rpx;
		font-size: 28rpx;
		color: #666;

		.red {
			color: #f56c6c;
		}
	}

	.value {
		font-size: 28rpx;
		color: #333;
		text-align: right;
		min-width: 440rpx;
		margin-right: 20rpx;
	}

	.career {}

	::v-deep .checklist-group {
		justify-content: flex-end;
	}

	.tag {
		font-size: 24rpx;
		color: #1aad19;
		background-color: #e8f5e9;
		padding: 6rpx 12rpx;
		border-radius: 8rpx;
		margin-right: 10rpx;
	}

	/* 底部按钮样式 */
	.footer {
		width: 100%;
		margin-top: 40rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.footers {
		width: 100%;
		margin-top: 40rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.next-button {
		width: 80%;
		height: 80rpx;
		background-color: #1BB394;
		color: #fff;
		font-size: 28rpx;
		border-radius: 40rpx;
		line-height: 80rpx;
		text-align: center;
	}

	.prev-button {
		width: 280rpx;
		height: 80rpx;
		background: #F6F7FB;
		border-radius: 40rpx;
		font-weight: bold;
		font-size: 30rpx;
		color: #5A5A5A;
		text-align: center;
		line-height: 80rpx;
	}

	.white {
		width: 280rpx;
		height: 80rpx;
		background: #fff;
		border-radius: 40rpx;
		font-weight: bold;
		font-size: 30rpx;
		color: #5A5A5A;
		text-align: center;
		line-height: 80rpx;
	}

	.form-item-step-three {
		height: 200rpx;
		background: #FFFFFF;
		border-radius: 17rpx;
		padding: 26rpx 28rpx;
		margin-bottom: 26rpx;

		.label {
			font-weight: bold;
			font-size: 30rpx;
			color: #504E4E;
		}
	}

	.value-container {
		display: flex;
		align-items: center;
		margin-top: 16rpx;
	}

	.value-input {
		flex: 1;
		padding: 10rpx;
		font-size: 28rpx;
		color: #333;
		border: 0;
		border-radius: 8rpx;
		outline: none;

	}

	.value-input:focus {
		border-color: #1BB394;
	}


	::v-deep .checkbox__inner {
		border-radius: 16rpx !important;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}

	@keyframes rotateCounter {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(-360deg);
			/* 负值实现逆时针旋转 */
		}
	}

	.mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.8);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		.bg-container {
			height: 550rpx;
			width: 550rpx;
			position: relative;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}

		.ai3-bg-container {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		.ai3-bg {
			background-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai3.png');
			height: 550rpx;
			width: 550rpx;
			background-repeat: no-repeat;
			background-size: contain;
		}

		.ai3-bg-animation {
			animation: rotate 1.5s linear infinite;
		}

		.ai2-bg-container {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		.ai2-bg {
			background-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai2.png');
			height: 420rpx;
			width: 494rpx;
			background-repeat: no-repeat;
			background-size: contain;
			z-index: 99;
		}

		.ai2-bg-animation {
			animation: rotateCounter 1.5s linear infinite;
		}

		.ai-bg {
			background-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai.png');
			height: 306rpx;
			width: 306rpx;
			background-repeat: no-repeat;
			background-size: contain;
			z-index: 100;
		}

		.submit {
			margin-top: 108rpx;
			width: 550rpx;
			height: 80rpx;
			background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
			border-radius: 40rpx 40rpx 40rpx 40rpx;
			font-weight: bold;
			font-size: 30rpx;
			line-height: 80rpx;
			text-align: center;
			color: #FFFFFF;
		}
	}
</style>