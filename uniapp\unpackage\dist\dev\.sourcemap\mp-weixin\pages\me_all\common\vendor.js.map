{"version": 3, "sources": ["uni-app:///utils/time.js", "uni-app:///uni_modules/Sansnn-uQRCode/js_sdk/uqrcode/uqrcode.js"], "names": ["formatMsgTime", "number", "dateTime", "Date", "Y", "getFullYear", "M", "getMonth", "D", "getDate", "h", "getHours", "m", "getMinutes", "millisecond", "getTime", "now", "nowNew", "milliseconds", "numberStr", "Math", "round", "toDate", "type", "date", "s", "getSeconds", "module", "exports", "o", "mode", "r", "MODE_8BIT_BYTE", "data", "e", "typeNumber", "errorCorrectLevel", "modules", "moduleCount", "dataCache", "dataList", "Array", "prototype", "<PERSON><PERSON><PERSON><PERSON>", "length", "write", "put", "charCodeAt", "addData", "push", "isDark", "Error", "getModuleCount", "make", "v", "getRSBlocks", "p", "t", "i", "dataCount", "n", "getLengthInBits", "makeImpl", "getBestMaskPattern", "setupPositionProbePattern", "setupPositionAdjustPattern", "setupTimingPattern", "setupTypeInfo", "setupTypeNumber", "createData", "mapData", "getLostPoint", "createMovieClip", "createEmptyMovieClip", "a", "d", "beginFill", "moveTo", "lineTo", "endFill", "getPatternPosition", "getBCHTypeNumber", "floor", "getBCHTypeInfo", "u", "getMask", "PAD0", "PAD1", "putBit", "createBytes", "totalCount", "max", "g", "buffer", "l", "getErrorCorrectPolynomial", "c", "f", "mod", "get", "C", "MODE_NUMBER", "MODE_ALPHA_NUM", "MODE_KANJI", "L", "Q", "H", "PATTERN_POSITION_TABLE", "G15", "G18", "G15_MASK", "getBCHDigit", "multiply", "gexp", "abs", "glog", "LOG_TABLE", "EXP_TABLE", "num", "setFillStyle", "fillStyle", "setFontSize", "font", "setTextAlign", "textAlign", "setTextBaseline", "textBaseline", "setGlobalAlpha", "globalAlpha", "setStrokeStyle", "strokeStyle", "setShadow", "shadowOffsetX", "shadowOffsetY", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "draw", "clearRect", "b", "size", "useDynamicSize", "dynamicSize", "margin", "areaColor", "backgroundColor", "backgroundImageSrc", "backgroundImageWidth", "backgroundImageHeight", "backgroundImageX", "backgroundImageY", "backgroundImageAlpha", "backgroundImageBorderRadius", "backgroundPadding", "foregroundColor", "foregroundImageSrc", "foregroundImageWidth", "foregroundImageHeight", "foregroundImageX", "foregroundImageY", "foregroundImagePadding", "foregroundImageBackgroundColor", "foregroundImageBorderRadius", "foregroundImageShadowOffsetX", "k", "foregroundImageShadowOffsetY", "y", "foregroundImageShadowBlur", "foregroundImageShadowColor", "w", "foregroundPadding", "I", "positionProbeBackgroundColor", "B", "positionProbeForegroundColor", "S", "separatorColor", "P", "positionAdjustBackgroundColor", "positionAdjustForegroundColor", "timingBackgroundColor", "A", "timingForegroundColor", "E", "typeNumberBackgroundColor", "T", "typeNumberForegroundColor", "N", "darkBlockColor", "base", "drawModules", "canvasContext", "loadImage", "drawReserve", "isMaked", "Object", "defineProperties", "console", "error", "set", "String", "Number", "plugins", "for<PERSON>ach", "setOptions", "RS_BLOCK_TABLE", "getRsBlockTable", "errMsg", "use", "Promise", "resolve", "z", "R", "_", "O", "F", "x", "X", "j", "W", "G", "K", "U", "$", "J", "q", "V", "Z", "oo", "eo", "ro", "keys", "constructor", "deepReplace", "text", "background", "color", "image", "src", "width", "height", "alpha", "borderRadius", "foreground", "padding", "positionProbe", "positionDetection", "separator", "positionAdjust", "alignment", "timing", "versionInformation", "dark<PERSON>lock", "toString", "char<PERSON>t", "fromCharCode", "ceil", "isBlack", "isDrawn", "destX", "destY", "destWidth", "destHeight", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "includes", "getDrawModules", "name", "imageSrc", "mappingName", "rowIndex", "colIndex", "draw<PERSON><PERSON>vas", "reject", "save", "fillRect", "beginPath", "arcTo", "closePath", "stroke", "clip", "drawImage", "fill", "restore", "setTimeout", "register"], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AACA,SAASA,aAAa,CAACC,MAAM,EAAE;EAC7B,IAAIC,QAAQ,GAAG,IAAIC,IAAI,CAACF,MAAM,CAAC,CAAC,CAAC;EACjC,IAAIG,CAAC,GAAGF,QAAQ,CAACG,WAAW,EAAE,CAAC,CAAC;EAChC,IAAIC,CAAC,GAAGJ,QAAQ,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;EACjC,IAAIC,CAAC,GAAGN,QAAQ,CAACO,OAAO,EAAE,CAAC,CAAC;EAC5B,IAAIC,CAAC,GAAGR,QAAQ,CAACS,QAAQ,EAAE,CAAC,CAAC;EAC7B,IAAIC,CAAC,GAAGV,QAAQ,CAACW,UAAU,EAAE,CAAC,CAAC;EAC/B,IAAIC,WAAW,GAAGZ,QAAQ,CAACa,OAAO,EAAE,CAAC,CAAC;EACtC,IAAIC,GAAG,GAAG,IAAIb,IAAI,EAAE,CAAC,CAAC;EACtB,IAAIc,MAAM,GAAGD,GAAG,CAACD,OAAO,EAAE,CAAC,CAAC;EAC5B,IAAIG,YAAY,GAAG,CAAC;EACpB,IAAIC,SAAS;EACbD,YAAY,GAAGD,MAAM,GAAGH,WAAW;EACnC,IAAII,YAAY,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE;IAAE;IACnCC,SAAS,GAAG,IAAI;EAClB,CAAC,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,GAAGD,YAAY,IAAIA,YAAY,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE;IAAE;IAC3EC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAEH,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,CAAE,GAAG,KAAK;EAC9D,CAAC,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAGA,YAAY,IAAIA,YAAY,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAAE;IACrFC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,KAAK;EACjE,CAAC,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAGA,YAAY,IAAIA,YAAY,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAAE;IAC3FC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI;EACrE,CAAC,MAAM,IAAIA,YAAY,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAId,CAAC,KAAKY,GAAG,CAACX,WAAW,EAAE,EAAE;IAC7Ec,SAAS,GAAGb,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,GAAG,GAAGE,CAAC;EAC7C,CAAC,MAAM;IACLO,SAAS,GAAGf,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,GAAG,GAAGE,CAAC;EACvD;EACA,OAAOO,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASG,MAAM,CAACrB,MAAM,EAAEsB,IAAI,EAAE;EAC5B,IAAIC,IAAI,GAAG,IAAIrB,IAAI,CAACF,MAAM,CAAC;EAC3B,IAAIG,CAAC,GAAGoB,IAAI,CAACnB,WAAW,EAAE;EAC1B,IAAIC,CAAC,GAAIkB,IAAI,CAACjB,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,IAAIiB,IAAI,CAACjB,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAGiB,IAAI,CAACjB,QAAQ,EAAE,GAAG,CAAE;EACtF,IAAIC,CAAC,GAAGgB,IAAI,CAACf,OAAO,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGe,IAAI,CAACf,OAAO,EAAE,GAAGe,IAAI,CAACf,OAAO,EAAE;EACnE,IAAIC,CAAC,GAAGc,IAAI,CAACb,QAAQ,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGa,IAAI,CAACb,QAAQ,EAAE,GAAGa,IAAI,CAACb,QAAQ,EAAE;EACtE,IAAIC,CAAC,GAAGY,IAAI,CAACX,UAAU,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGW,IAAI,CAACX,UAAU,EAAE,GAAGW,IAAI,CAACX,UAAU,EAAE;EAC5E,IAAIY,CAAC,GAAGD,IAAI,CAACE,UAAU,EAAE,GAAG,EAAE,GAAG,GAAG,GAAGF,IAAI,CAACE,UAAU,EAAE,GAAGF,IAAI,CAACE,UAAU,EAAE;EAC5E,IAAIH,IAAI,IAAI,GAAG,EAAE;IACf,OAAOnB,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,GAAG,GAAGa,CAAC;EAC5D,CAAC,MAAM,IAAIF,IAAI,IAAI,GAAG,EAAE;IACtB,OAAOnB,CAAC,GAAG,GAAG,GAAGE,CAAC,GAAG,GAAG,GAAGE,CAAC;EAC9B;AACF;AAEAmB,MAAM,CAACC,OAAO,GAAG;EACfN,MAAM,EAAEA,MAAM;EACdtB,aAAa,EAAEA;AACjB,CAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACxDD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS6B,CAAC,CAACA,CAAC,EAAC;EAAC,IAAI,CAACC,IAAI,GAACC,CAAC,CAACC,cAAc,EAAC,IAAI,CAACC,IAAI,GAACJ,CAAC;AAAC;AAAC,SAASK,CAAC,CAACL,CAAC,EAACK,CAAC,EAAC;EAAC,IAAI,CAACC,UAAU,GAACN,CAAC,EAAC,IAAI,CAACO,iBAAiB,GAACF,CAAC,EAAC,IAAI,CAACG,OAAO,GAAC,IAAI,EAAC,IAAI,CAACC,WAAW,GAAC,CAAC,EAAC,IAAI,CAACC,SAAS,GAAC,IAAI,EAAC,IAAI,CAACC,QAAQ,GAAC,IAAIC,KAAK;AAAC;AAACZ,CAAC,CAACa,SAAS,GAAC;EAACC,SAAS,EAAC,mBAASd,CAAC,EAAC;IAAC,OAAO,IAAI,CAACI,IAAI,CAACW,MAAM;EAAA,CAAC;EAACC,KAAK,EAAC,eAAShB,CAAC,EAAC;IAAC,KAAI,IAAIK,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACD,IAAI,CAACW,MAAM,EAACV,CAAC,EAAE;MAACL,CAAC,CAACiB,GAAG,CAAC,IAAI,CAACb,IAAI,CAACc,UAAU,CAACb,CAAC,CAAC,EAAC,CAAC,CAAC;IAAC;EAAA;AAAC,CAAC,EAACA,CAAC,CAACQ,SAAS,GAAC;EAACM,OAAO,EAAC,iBAASd,CAAC,EAAC;IAAC,IAAIH,CAAC,GAAC,IAAIF,CAAC,CAACK,CAAC,CAAC;IAAC,IAAI,CAACM,QAAQ,CAACS,IAAI,CAAClB,CAAC,CAAC,EAAC,IAAI,CAACQ,SAAS,GAAC,IAAI;EAAC,CAAC;EAACW,MAAM,EAAC,gBAASrB,CAAC,EAACK,CAAC,EAAC;IAAC,IAAGL,CAAC,GAAC,CAAC,IAAE,IAAI,CAACS,WAAW,IAAET,CAAC,IAAEK,CAAC,GAAC,CAAC,IAAE,IAAI,CAACI,WAAW,IAAEJ,CAAC,EAAC,MAAM,IAAIiB,KAAK,CAACtB,CAAC,GAAC,GAAG,GAACK,CAAC,CAAC;IAAC,OAAO,IAAI,CAACG,OAAO,CAACR,CAAC,CAAC,CAACK,CAAC,CAAC;EAAA,CAAC;EAACkB,cAAc,EAAC,0BAAU;IAAC,OAAO,IAAI,CAACd,WAAW;EAAA,CAAC;EAACe,IAAI,EAAC,gBAAU;IAAC,IAAG,IAAI,CAAClB,UAAU,GAAC,CAAC,EAAC;MAAC,IAAIN,CAAC,GAAC,CAAC;MAAC,KAAIA,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,EAAE,EAACA,CAAC,EAAE,EAAC;QAAC,KAAI,IAAIK,CAAC,GAACoB,CAAC,CAACC,WAAW,CAAC1B,CAAC,EAAC,IAAI,CAACO,iBAAiB,CAAC,EAACL,CAAC,GAAC,IAAIyB,CAAC,IAACC,CAAC,GAAC,CAAC,EAACC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACxB,CAAC,CAACU,MAAM,EAACc,CAAC,EAAE;UAACD,CAAC,IAAEvB,CAAC,CAACwB,CAAC,CAAC,CAACC,SAAS;QAAC;QAAA,KAAID,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAAClB,QAAQ,CAACI,MAAM,EAACc,CAAC,EAAE,EAAC;UAAC,IAAIE,CAAC,GAAC,IAAI,CAACpB,QAAQ,CAACkB,CAAC,CAAC;UAAC3B,CAAC,CAACe,GAAG,CAACc,CAAC,CAAC9B,IAAI,EAAC,CAAC,CAAC,EAACC,CAAC,CAACe,GAAG,CAACc,CAAC,CAACjB,SAAS,EAAE,EAACjC,CAAC,CAACmD,eAAe,CAACD,CAAC,CAAC9B,IAAI,EAACD,CAAC,CAAC,CAAC,EAAC+B,CAAC,CAACf,KAAK,CAACd,CAAC,CAAC;QAAC;QAAC,IAAGA,CAAC,CAAC8B,eAAe,EAAE,IAAE,CAAC,GAACJ,CAAC,EAAC;MAAK;MAAC,IAAI,CAACtB,UAAU,GAACN,CAAC;IAAC;IAAC,IAAI,CAACiC,QAAQ,CAAC,CAAC,CAAC,EAAC,IAAI,CAACC,kBAAkB,EAAE,CAAC;EAAC,CAAC;EAACD,QAAQ,EAAC,kBAASjC,CAAC,EAACE,CAAC,EAAC;IAAC,IAAI,CAACO,WAAW,GAAC,CAAC,GAAC,IAAI,CAACH,UAAU,GAAC,EAAE,EAAC,IAAI,CAACE,OAAO,GAAC,IAAII,KAAK,CAAC,IAAI,CAACH,WAAW,CAAC;IAAC,KAAI,IAAImB,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACnB,WAAW,EAACmB,CAAC,EAAE,EAAC;MAAC,IAAI,CAACpB,OAAO,CAACoB,CAAC,CAAC,GAAC,IAAIhB,KAAK,CAAC,IAAI,CAACH,WAAW,CAAC;MAAC,KAAI,IAAIoB,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACpB,WAAW,EAACoB,CAAC,EAAE;QAAC,IAAI,CAACrB,OAAO,CAACoB,CAAC,CAAC,CAACC,CAAC,CAAC,GAAC,IAAI;MAAC;IAAA;IAAC,IAAI,CAACM,yBAAyB,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,IAAI,CAACA,yBAAyB,CAAC,IAAI,CAAC1B,WAAW,GAAC,CAAC,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC0B,yBAAyB,CAAC,CAAC,EAAC,IAAI,CAAC1B,WAAW,GAAC,CAAC,CAAC,EAAC,IAAI,CAAC2B,0BAA0B,EAAE,EAAC,IAAI,CAACC,kBAAkB,EAAE,EAAC,IAAI,CAACC,aAAa,CAACtC,CAAC,EAACE,CAAC,CAAC,EAAC,IAAI,CAACI,UAAU,IAAE,CAAC,IAAE,IAAI,CAACiC,eAAe,CAACvC,CAAC,CAAC,EAAC,IAAI,IAAE,IAAI,CAACU,SAAS,KAAG,IAAI,CAACA,SAAS,GAACL,CAAC,CAACmC,UAAU,CAAC,IAAI,CAAClC,UAAU,EAAC,IAAI,CAACC,iBAAiB,EAAC,IAAI,CAACI,QAAQ,CAAC,CAAC,EAAC,IAAI,CAAC8B,OAAO,CAAC,IAAI,CAAC/B,SAAS,EAACR,CAAC,CAAC;EAAC,CAAC;EAACiC,yBAAyB,EAAC,mCAASnC,CAAC,EAACK,CAAC,EAAC;IAAC,KAAI,IAAIH,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE;MAAC,IAAG,EAAEF,CAAC,GAACE,CAAC,IAAE,CAAC,CAAC,IAAE,IAAI,CAACO,WAAW,IAAET,CAAC,GAACE,CAAC,CAAC,EAAC,KAAI,IAAI0B,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE;QAACvB,CAAC,GAACuB,CAAC,IAAE,CAAC,CAAC,IAAE,IAAI,CAACnB,WAAW,IAAEJ,CAAC,GAACuB,CAAC,KAAG,IAAI,CAACpB,OAAO,CAACR,CAAC,GAACE,CAAC,CAAC,CAACG,CAAC,GAACuB,CAAC,CAAC,GAAC,CAAC,IAAE1B,CAAC,IAAEA,CAAC,IAAE,CAAC,KAAG,CAAC,IAAE0B,CAAC,IAAE,CAAC,IAAEA,CAAC,CAAC,IAAE,CAAC,IAAEA,CAAC,IAAEA,CAAC,IAAE,CAAC,KAAG,CAAC,IAAE1B,CAAC,IAAE,CAAC,IAAEA,CAAC,CAAC,IAAE,CAAC,IAAEA,CAAC,IAAEA,CAAC,IAAE,CAAC,IAAE,CAAC,IAAE0B,CAAC,IAAEA,CAAC,IAAE,CAAC,CAAC;MAAC;IAAA;EAAA,CAAC;EAACM,kBAAkB,EAAC,8BAAU;IAAC,KAAI,IAAIlC,CAAC,GAAC,CAAC,EAACK,CAAC,GAAC,CAAC,EAACH,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,CAAC,EAACA,CAAC,EAAE,EAAC;MAAC,IAAI,CAAC+B,QAAQ,CAAC,CAAC,CAAC,EAAC/B,CAAC,CAAC;MAAC,IAAI0B,CAAC,GAAC/C,CAAC,CAAC6D,YAAY,CAAC,IAAI,CAAC;MAAC,CAAC,CAAC,IAAExC,CAAC,IAAEF,CAAC,GAAC4B,CAAC,MAAI5B,CAAC,GAAC4B,CAAC,EAACvB,CAAC,GAACH,CAAC,CAAC;IAAC;IAAC,OAAOG,CAAC;EAAA,CAAC;EAACsC,eAAe,EAAC,yBAAS3C,CAAC,EAACK,CAAC,EAACH,CAAC,EAAC;IAAC,IAAI0B,CAAC,GAAC5B,CAAC,CAAC4C,oBAAoB,CAACvC,CAAC,EAACH,CAAC,CAAC;IAAC,IAAI,CAACsB,IAAI,EAAE;IAAC,KAAI,IAAIK,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACrB,OAAO,CAACO,MAAM,EAACc,CAAC,EAAE;MAAC,KAAI,IAAIE,CAAC,GAAC,CAAC,GAACF,CAAC,EAACgB,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACrC,OAAO,CAACqB,CAAC,CAAC,CAACd,MAAM,EAAC8B,CAAC,EAAE,EAAC;QAAC,IAAIC,CAAC,GAAC,CAAC,GAACD,CAAC;QAAC,IAAI,CAACrC,OAAO,CAACqB,CAAC,CAAC,CAACgB,CAAC,CAAC,KAAGjB,CAAC,CAACmB,SAAS,CAAC,CAAC,EAAC,GAAG,CAAC,EAACnB,CAAC,CAACoB,MAAM,CAACF,CAAC,EAACf,CAAC,CAAC,EAACH,CAAC,CAACqB,MAAM,CAACH,CAAC,GAAC,CAAC,EAACf,CAAC,CAAC,EAACH,CAAC,CAACqB,MAAM,CAACH,CAAC,GAAC,CAAC,EAACf,CAAC,GAAC,CAAC,CAAC,EAACH,CAAC,CAACqB,MAAM,CAACH,CAAC,EAACf,CAAC,GAAC,CAAC,CAAC,EAACH,CAAC,CAACsB,OAAO,EAAE,CAAC;MAAC;IAAC;IAAA,OAAOtB,CAAC;EAAA,CAAC;EAACS,kBAAkB,EAAC,8BAAU;IAAC,KAAI,IAAIrC,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACS,WAAW,GAAC,CAAC,EAACT,CAAC,EAAE;MAAC,IAAI,IAAE,IAAI,CAACQ,OAAO,CAACR,CAAC,CAAC,CAAC,CAAC,CAAC,KAAG,IAAI,CAACQ,OAAO,CAACR,CAAC,CAAC,CAAC,CAAC,CAAC,GAACA,CAAC,GAAC,CAAC,IAAE,CAAC,CAAC;IAAC;IAAA,KAAI,IAAIK,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACI,WAAW,GAAC,CAAC,EAACJ,CAAC,EAAE;MAAC,IAAI,IAAE,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,CAACH,CAAC,CAAC,KAAG,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,CAACH,CAAC,CAAC,GAACA,CAAC,GAAC,CAAC,IAAE,CAAC,CAAC;IAAC;EAAA,CAAC;EAAC+B,0BAA0B,EAAC,sCAAU;IAAC,KAAI,IAAIpC,CAAC,GAACnB,CAAC,CAACsE,kBAAkB,CAAC,IAAI,CAAC7C,UAAU,CAAC,EAACD,CAAC,GAAC,CAAC,EAACA,CAAC,GAACL,CAAC,CAACe,MAAM,EAACV,CAAC,EAAE;MAAC,KAAI,IAAIH,CAAC,GAAC,CAAC,EAACA,CAAC,GAACF,CAAC,CAACe,MAAM,EAACb,CAAC,EAAE,EAAC;QAAC,IAAI0B,CAAC,GAAC5B,CAAC,CAACK,CAAC,CAAC;UAACwB,CAAC,GAAC7B,CAAC,CAACE,CAAC,CAAC;QAAC,IAAG,IAAI,IAAE,IAAI,CAACM,OAAO,CAACoB,CAAC,CAAC,CAACC,CAAC,CAAC,EAAC,KAAI,IAAIE,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE;UAAC,KAAI,IAAIc,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE;YAAC,IAAI,CAACrC,OAAO,CAACoB,CAAC,GAACG,CAAC,CAAC,CAACF,CAAC,GAACgB,CAAC,CAAC,GAAC,CAAC,CAAC,IAAEd,CAAC,IAAE,CAAC,IAAEA,CAAC,IAAE,CAAC,CAAC,IAAEc,CAAC,IAAE,CAAC,IAAEA,CAAC,IAAE,CAAC,IAAEd,CAAC,IAAE,CAAC,IAAEc,CAAC;UAAC;QAAA;MAAA;IAAC;EAAA,CAAC;EAACN,eAAe,EAAC,yBAASvC,CAAC,EAAC;IAAC,KAAI,IAAIK,CAAC,GAACxB,CAAC,CAACuE,gBAAgB,CAAC,IAAI,CAAC9C,UAAU,CAAC,EAACJ,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,EAAE,EAACA,CAAC,EAAE,EAAC;MAAC,IAAI0B,CAAC,GAAC,CAAC5B,CAAC,IAAE,CAAC,KAAGK,CAAC,IAAEH,CAAC,GAAC,CAAC,CAAC;MAAC,IAAI,CAACM,OAAO,CAACjB,IAAI,CAAC8D,KAAK,CAACnD,CAAC,GAAC,CAAC,CAAC,CAAC,CAACA,CAAC,GAAC,CAAC,GAAC,IAAI,CAACO,WAAW,GAAC,CAAC,GAAC,CAAC,CAAC,GAACmB,CAAC;IAAC;IAAC,KAAI1B,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,EAAE,EAACA,CAAC,EAAE,EAAC;MAAC0B,CAAC,GAAC,CAAC5B,CAAC,IAAE,CAAC,KAAGK,CAAC,IAAEH,CAAC,GAAC,CAAC,CAAC;MAAC,IAAI,CAACM,OAAO,CAACN,CAAC,GAAC,CAAC,GAAC,IAAI,CAACO,WAAW,GAAC,CAAC,GAAC,CAAC,CAAC,CAAClB,IAAI,CAAC8D,KAAK,CAACnD,CAAC,GAAC,CAAC,CAAC,CAAC,GAAC0B,CAAC;IAAC;EAAC,CAAC;EAACU,aAAa,EAAC,uBAAStC,CAAC,EAACK,CAAC,EAAC;IAAC,KAAI,IAAIH,CAAC,GAAC,IAAI,CAACK,iBAAiB,IAAE,CAAC,GAACF,CAAC,EAACuB,CAAC,GAAC/C,CAAC,CAACyE,cAAc,CAACpD,CAAC,CAAC,EAAC2B,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,EAAE,EAACA,CAAC,EAAE,EAAC;MAAC,IAAIE,CAAC,GAAC,CAAC/B,CAAC,IAAE,CAAC,KAAG4B,CAAC,IAAEC,CAAC,GAAC,CAAC,CAAC;MAACA,CAAC,GAAC,CAAC,GAAC,IAAI,CAACrB,OAAO,CAACqB,CAAC,CAAC,CAAC,CAAC,CAAC,GAACE,CAAC,GAACF,CAAC,GAAC,CAAC,GAAC,IAAI,CAACrB,OAAO,CAACqB,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACE,CAAC,GAAC,IAAI,CAACvB,OAAO,CAAC,IAAI,CAACC,WAAW,GAAC,EAAE,GAACoB,CAAC,CAAC,CAAC,CAAC,CAAC,GAACE,CAAC;IAAC;IAAC,KAAIF,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,EAAE,EAACA,CAAC,EAAE,EAAC;MAACE,CAAC,GAAC,CAAC/B,CAAC,IAAE,CAAC,KAAG4B,CAAC,IAAEC,CAAC,GAAC,CAAC,CAAC;MAACA,CAAC,GAAC,CAAC,GAAC,IAAI,CAACrB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAACC,WAAW,GAACoB,CAAC,GAAC,CAAC,CAAC,GAACE,CAAC,GAACF,CAAC,GAAC,CAAC,GAAC,IAAI,CAACrB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAACqB,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,GAACE,CAAC,GAAC,IAAI,CAACvB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAACqB,CAAC,GAAC,CAAC,CAAC,GAACE,CAAC;IAAC;IAAC,IAAI,CAACvB,OAAO,CAAC,IAAI,CAACC,WAAW,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,CAACT,CAAC;EAAC,CAAC;EAACyC,OAAO,EAAC,iBAASzC,CAAC,EAACK,CAAC,EAAC;IAAC,KAAI,IAAIH,CAAC,GAAC,CAAC,CAAC,EAAC0B,CAAC,GAAC,IAAI,CAACnB,WAAW,GAAC,CAAC,EAACoB,CAAC,GAAC,CAAC,EAACE,CAAC,GAAC,CAAC,EAACc,CAAC,GAAC,IAAI,CAACpC,WAAW,GAAC,CAAC,EAACoC,CAAC,GAAC,CAAC,EAACA,CAAC,IAAE,CAAC;MAAC,KAAI,CAAC,IAAEA,CAAC,IAAEA,CAAC,EAAE,IAAG;QAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,CAAC,EAACA,CAAC,EAAE;UAAC,IAAG,IAAI,IAAE,IAAI,CAACtC,OAAO,CAACoB,CAAC,CAAC,CAACiB,CAAC,GAACC,CAAC,CAAC,EAAC;YAAC,IAAIS,CAAC,GAAC,CAAC,CAAC;YAACxB,CAAC,GAAC/B,CAAC,CAACe,MAAM,KAAGwC,CAAC,GAAC,CAAC,KAAGvD,CAAC,CAAC+B,CAAC,CAAC,KAAGF,CAAC,GAAC,CAAC,CAAC,CAAC,EAAChD,CAAC,CAAC2E,OAAO,CAACnD,CAAC,EAACuB,CAAC,EAACiB,CAAC,GAACC,CAAC,CAAC,KAAGS,CAAC,GAAC,CAACA,CAAC,CAAC,EAAC,IAAI,CAAC/C,OAAO,CAACoB,CAAC,CAAC,CAACiB,CAAC,GAACC,CAAC,CAAC,GAACS,CAAC,EAAC,CAAC,CAAC,IAAE,EAAE1B,CAAC,KAAGE,CAAC,EAAE,EAACF,CAAC,GAAC,CAAC,CAAC;UAAC;QAAC;QAAA,IAAG,CAACD,CAAC,IAAE1B,CAAC,IAAE,CAAC,IAAE,IAAI,CAACO,WAAW,IAAEmB,CAAC,EAAC;UAACA,CAAC,IAAE1B,CAAC,EAACA,CAAC,GAAC,CAACA,CAAC;UAAC;QAAK;MAAC;IAAC;EAAA;AAAC,CAAC,EAACG,CAAC,CAACoD,IAAI,GAAC,GAAG,EAACpD,CAAC,CAACqD,IAAI,GAAC,EAAE,EAACrD,CAAC,CAACmC,UAAU,GAAC,UAASxC,CAAC,EAACE,CAAC,EAAC0B,CAAC,EAAC;EAAC,KAAI,IAAIC,CAAC,GAACJ,CAAC,CAACC,WAAW,CAAC1B,CAAC,EAACE,CAAC,CAAC,EAAC6B,CAAC,GAAC,IAAIJ,CAAC,IAACkB,CAAC,GAAC,CAAC,EAACA,CAAC,GAACjB,CAAC,CAACb,MAAM,EAAC8B,CAAC,EAAE,EAAC;IAAC,IAAIC,CAAC,GAAClB,CAAC,CAACiB,CAAC,CAAC;IAACd,CAAC,CAACd,GAAG,CAAC6B,CAAC,CAAC7C,IAAI,EAAC,CAAC,CAAC,EAAC8B,CAAC,CAACd,GAAG,CAAC6B,CAAC,CAAChC,SAAS,EAAE,EAACjC,CAAC,CAACmD,eAAe,CAACc,CAAC,CAAC7C,IAAI,EAACD,CAAC,CAAC,CAAC,EAAC8C,CAAC,CAAC9B,KAAK,CAACe,CAAC,CAAC;EAAC;EAAC,IAAIwB,CAAC,GAAC,CAAC;EAAC,KAAIV,CAAC,GAAC,CAAC,EAACA,CAAC,GAAChB,CAAC,CAACd,MAAM,EAAC8B,CAAC,EAAE;IAACU,CAAC,IAAE1B,CAAC,CAACgB,CAAC,CAAC,CAACf,SAAS;EAAC;EAAA,IAAGC,CAAC,CAACC,eAAe,EAAE,GAAC,CAAC,GAACuB,CAAC,EAAC,MAAM,IAAIjC,KAAK,CAAC,yBAAyB,GAACS,CAAC,CAACC,eAAe,EAAE,GAAC,GAAG,GAAC,CAAC,GAACuB,CAAC,GAAC,GAAG,CAAC;EAAC,KAAIxB,CAAC,CAACC,eAAe,EAAE,GAAC,CAAC,IAAE,CAAC,GAACuB,CAAC,IAAExB,CAAC,CAACd,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,EAACc,CAAC,CAACC,eAAe,EAAE,GAAC,CAAC,IAAE,CAAC;IAAED,CAAC,CAAC4B,MAAM,CAAC,CAAC,CAAC,CAAC;EAAC;EAAA,OAAK,EAAE5B,CAAC,CAACC,eAAe,EAAE,IAAE,CAAC,GAACuB,CAAC,KAAGxB,CAAC,CAACd,GAAG,CAACZ,CAAC,CAACoD,IAAI,EAAC,CAAC,CAAC,EAAC1B,CAAC,CAACC,eAAe,EAAE,IAAE,CAAC,GAACuB,CAAC,CAAC,CAAC;IAAExB,CAAC,CAACd,GAAG,CAACZ,CAAC,CAACqD,IAAI,EAAC,CAAC,CAAC;EAAC;EAAA,OAAOrD,CAAC,CAACuD,WAAW,CAAC7B,CAAC,EAACF,CAAC,CAAC;AAAA,CAAC,EAACxB,CAAC,CAACuD,WAAW,GAAC,UAAS5D,CAAC,EAACK,CAAC,EAAC;EAAC,KAAI,IAAIH,CAAC,GAAC,CAAC,EAAC0B,CAAC,GAAC,CAAC,EAACC,CAAC,GAAC,CAAC,EAACE,CAAC,GAAC,IAAInB,KAAK,CAACP,CAAC,CAACU,MAAM,CAAC,EAAC8B,CAAC,GAAC,IAAIjC,KAAK,CAACP,CAAC,CAACU,MAAM,CAAC,EAAC+B,CAAC,GAAC,CAAC,EAACA,CAAC,GAACzC,CAAC,CAACU,MAAM,EAAC+B,CAAC,EAAE,EAAC;IAAC,IAAIS,CAAC,GAAClD,CAAC,CAACyC,CAAC,CAAC,CAAChB,SAAS;MAAClC,CAAC,GAACS,CAAC,CAACyC,CAAC,CAAC,CAACe,UAAU,GAACN,CAAC;IAAC3B,CAAC,GAACrC,IAAI,CAACuE,GAAG,CAAClC,CAAC,EAAC2B,CAAC,CAAC,EAAC1B,CAAC,GAACtC,IAAI,CAACuE,GAAG,CAACjC,CAAC,EAACjC,CAAC,CAAC,EAACmC,CAAC,CAACe,CAAC,CAAC,GAAC,IAAIlC,KAAK,CAAC2C,CAAC,CAAC;IAAC,KAAI,IAAIQ,CAAC,GAAC,CAAC,EAACA,CAAC,GAAChC,CAAC,CAACe,CAAC,CAAC,CAAC/B,MAAM,EAACgD,CAAC,EAAE;MAAChC,CAAC,CAACe,CAAC,CAAC,CAACiB,CAAC,CAAC,GAAC,GAAG,GAAC/D,CAAC,CAACgE,MAAM,CAACD,CAAC,GAAC7D,CAAC,CAAC;IAAC;IAAAA,CAAC,IAAEqD,CAAC;IAAC,IAAIU,CAAC,GAACpF,CAAC,CAACqF,yBAAyB,CAACtE,CAAC,CAAC;MAACuE,CAAC,GAAC,IAAIC,CAAC,CAACrC,CAAC,CAACe,CAAC,CAAC,EAACmB,CAAC,CAACnD,SAAS,EAAE,GAAC,CAAC,CAAC,CAACuD,GAAG,CAACJ,CAAC,CAAC;IAACpB,CAAC,CAACC,CAAC,CAAC,GAAC,IAAIlC,KAAK,CAACqD,CAAC,CAACnD,SAAS,EAAE,GAAC,CAAC,CAAC;IAAC,KAAIiD,CAAC,GAAC,CAAC,EAACA,CAAC,GAAClB,CAAC,CAACC,CAAC,CAAC,CAAC/B,MAAM,EAACgD,CAAC,EAAE,EAAC;MAAC,IAAIhF,CAAC,GAACgF,CAAC,GAACI,CAAC,CAACrD,SAAS,EAAE,GAAC+B,CAAC,CAACC,CAAC,CAAC,CAAC/B,MAAM;MAAC8B,CAAC,CAACC,CAAC,CAAC,CAACiB,CAAC,CAAC,GAAChF,CAAC,IAAE,CAAC,GAACoF,CAAC,CAACG,GAAG,CAACvF,CAAC,CAAC,GAAC,CAAC;IAAC;EAAC;EAAC,IAAI0C,CAAC,GAAC,CAAC;EAAC,KAAIsC,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC1D,CAAC,CAACU,MAAM,EAACgD,CAAC,EAAE;IAACtC,CAAC,IAAEpB,CAAC,CAAC0D,CAAC,CAAC,CAACF,UAAU;EAAC;EAAA,IAAIlC,CAAC,GAAC,IAAIf,KAAK,CAACa,CAAC,CAAC;IAAC8C,CAAC,GAAC,CAAC;EAAC,KAAIR,CAAC,GAAC,CAAC,EAACA,CAAC,GAACnC,CAAC,EAACmC,CAAC,EAAE;IAAC,KAAIjB,CAAC,GAAC,CAAC,EAACA,CAAC,GAACzC,CAAC,CAACU,MAAM,EAAC+B,CAAC,EAAE;MAACiB,CAAC,GAAChC,CAAC,CAACe,CAAC,CAAC,CAAC/B,MAAM,KAAGY,CAAC,CAAC4C,CAAC,EAAE,CAAC,GAACxC,CAAC,CAACe,CAAC,CAAC,CAACiB,CAAC,CAAC,CAAC;IAAC;EAAA;EAAA,KAAIA,CAAC,GAAC,CAAC,EAACA,CAAC,GAAClC,CAAC,EAACkC,CAAC,EAAE;IAAC,KAAIjB,CAAC,GAAC,CAAC,EAACA,CAAC,GAACzC,CAAC,CAACU,MAAM,EAAC+B,CAAC,EAAE;MAACiB,CAAC,GAAClB,CAAC,CAACC,CAAC,CAAC,CAAC/B,MAAM,KAAGY,CAAC,CAAC4C,CAAC,EAAE,CAAC,GAAC1B,CAAC,CAACC,CAAC,CAAC,CAACiB,CAAC,CAAC,CAAC;IAAC;EAAA;EAAA,OAAOpC,CAAC;AAAA,CAAC;AAAC,KAAI,IAAIzB,CAAC,GAAC;IAACsE,WAAW,EAAC,CAAC;IAACC,cAAc,EAAC,CAAC;IAACtE,cAAc,EAAC,CAAC;IAACuE,UAAU,EAAC;EAAC,CAAC,EAAC9C,CAAC,GAAC;IAAC+C,CAAC,EAAC,CAAC;IAAClG,CAAC,EAAC,CAAC;IAACmG,CAAC,EAAC,CAAC;IAACC,CAAC,EAAC;EAAC,CAAC,EAAChD,CAAC,GAAC,CAAC,EAACE,CAAC,GAAC,CAAC,EAACc,CAAC,GAAC,CAAC,EAACC,CAAC,GAAC,CAAC,EAACS,CAAC,GAAC,CAAC,EAAC3D,CAAC,GAAC,CAAC,EAACmE,CAAC,GAAC,CAAC,EAACE,CAAC,GAAC,CAAC,EAACpF,CAAC,GAAC;IAACiG,sBAAsB,EAAC,CAAC,EAAE,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC;IAACC,GAAG,EAAC,IAAI;IAACC,GAAG,EAAC,IAAI;IAACC,QAAQ,EAAC,KAAK;IAAC3B,cAAc,EAAC,wBAAStD,CAAC,EAAC;MAAC,KAAI,IAAIK,CAAC,GAACL,CAAC,IAAE,EAAE,EAACnB,CAAC,CAACqG,WAAW,CAAC7E,CAAC,CAAC,GAACxB,CAAC,CAACqG,WAAW,CAACrG,CAAC,CAACkG,GAAG,CAAC,IAAE,CAAC;QAAE1E,CAAC,IAAExB,CAAC,CAACkG,GAAG,IAAElG,CAAC,CAACqG,WAAW,CAAC7E,CAAC,CAAC,GAACxB,CAAC,CAACqG,WAAW,CAACrG,CAAC,CAACkG,GAAG,CAAC;MAAC;MAAA,OAAO,CAAC/E,CAAC,IAAE,EAAE,GAACK,CAAC,IAAExB,CAAC,CAACoG,QAAQ;IAAA,CAAC;IAAC7B,gBAAgB,EAAC,0BAASpD,CAAC,EAAC;MAAC,KAAI,IAAIK,CAAC,GAACL,CAAC,IAAE,EAAE,EAACnB,CAAC,CAACqG,WAAW,CAAC7E,CAAC,CAAC,GAACxB,CAAC,CAACqG,WAAW,CAACrG,CAAC,CAACmG,GAAG,CAAC,IAAE,CAAC;QAAE3E,CAAC,IAAExB,CAAC,CAACmG,GAAG,IAAEnG,CAAC,CAACqG,WAAW,CAAC7E,CAAC,CAAC,GAACxB,CAAC,CAACqG,WAAW,CAACrG,CAAC,CAACmG,GAAG,CAAC;MAAC;MAAA,OAAOhF,CAAC,IAAE,EAAE,GAACK,CAAC;IAAA,CAAC;IAAC6E,WAAW,EAAC,qBAASlF,CAAC,EAAC;MAAC,KAAI,IAAIK,CAAC,GAAC,CAAC,EAAC,CAAC,IAAEL,CAAC;QAAEK,CAAC,EAAE,EAACL,CAAC,MAAI,CAAC;MAAC;MAAA,OAAOK,CAAC;IAAA,CAAC;IAAC8C,kBAAkB,EAAC,4BAASnD,CAAC,EAAC;MAAC,OAAOnB,CAAC,CAACiG,sBAAsB,CAAC9E,CAAC,GAAC,CAAC,CAAC;IAAA,CAAC;IAACwD,OAAO,EAAC,iBAASxD,CAAC,EAACK,CAAC,EAACH,CAAC,EAAC;MAAC,QAAOF,CAAC;QAAE,KAAK6B,CAAC;UAAC,OAAO,CAACxB,CAAC,GAACH,CAAC,IAAE,CAAC,IAAE,CAAC;QAAC,KAAK6B,CAAC;UAAC,OAAO1B,CAAC,GAAC,CAAC,IAAE,CAAC;QAAC,KAAKwC,CAAC;UAAC,OAAO3C,CAAC,GAAC,CAAC,IAAE,CAAC;QAAC,KAAK4C,CAAC;UAAC,OAAO,CAACzC,CAAC,GAACH,CAAC,IAAE,CAAC,IAAE,CAAC;QAAC,KAAKqD,CAAC;UAAC,OAAO,CAAChE,IAAI,CAAC8D,KAAK,CAAChD,CAAC,GAAC,CAAC,CAAC,GAACd,IAAI,CAAC8D,KAAK,CAACnD,CAAC,GAAC,CAAC,CAAC,IAAE,CAAC,IAAE,CAAC;QAAC,KAAKN,CAAC;UAAC,OAAOS,CAAC,GAACH,CAAC,GAAC,CAAC,GAACG,CAAC,GAACH,CAAC,GAAC,CAAC,IAAE,CAAC;QAAC,KAAK6D,CAAC;UAAC,OAAO,CAAC1D,CAAC,GAACH,CAAC,GAAC,CAAC,GAACG,CAAC,GAACH,CAAC,GAAC,CAAC,IAAE,CAAC,IAAE,CAAC;QAAC,KAAK+D,CAAC;UAAC,OAAO,CAAC5D,CAAC,GAACH,CAAC,GAAC,CAAC,GAAC,CAACG,CAAC,GAACH,CAAC,IAAE,CAAC,IAAE,CAAC,IAAE,CAAC;QAAC;UAAQ,MAAM,IAAIoB,KAAK,CAAC,kBAAkB,GAACtB,CAAC,CAAC;MAAA;IAAC,CAAC;IAACkE,yBAAyB,EAAC,mCAASlE,CAAC,EAAC;MAAC,KAAI,IAAIK,CAAC,GAAC,IAAI+D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAClE,CAAC,GAAC,CAAC,EAACA,CAAC,GAACF,CAAC,EAACE,CAAC,EAAE;QAACG,CAAC,GAACA,CAAC,CAAC8E,QAAQ,CAAC,IAAIf,CAAC,CAAC,CAAC,CAAC,EAACD,CAAC,CAACiB,IAAI,CAAClF,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;MAAC;MAAA,OAAOG,CAAC;IAAA,CAAC;IAAC2B,eAAe,EAAC,yBAAShC,CAAC,EAACK,CAAC,EAAC;MAAC,IAAG,CAAC,IAAEA,CAAC,IAAEA,CAAC,GAAC,EAAE,EAAC,QAAOL,CAAC;QAAE,KAAKE,CAAC,CAACsE,WAAW;UAAC,OAAO,EAAE;QAAC,KAAKtE,CAAC,CAACuE,cAAc;UAAC,OAAO,CAAC;QAAC,KAAKvE,CAAC,CAACC,cAAc;QAAC,KAAKD,CAAC,CAACwE,UAAU;UAAC,OAAO,CAAC;QAAC;UAAQ,MAAM,IAAIpD,KAAK,CAAC,OAAO,GAACtB,CAAC,CAAC;MAAA,CAAC,MAAK,IAAGK,CAAC,GAAC,EAAE,EAAC,QAAOL,CAAC;QAAE,KAAKE,CAAC,CAACsE,WAAW;UAAC,OAAO,EAAE;QAAC,KAAKtE,CAAC,CAACuE,cAAc;UAAC,OAAO,EAAE;QAAC,KAAKvE,CAAC,CAACC,cAAc;UAAC,OAAO,EAAE;QAAC,KAAKD,CAAC,CAACwE,UAAU;UAAC,OAAO,EAAE;QAAC;UAAQ,MAAM,IAAIpD,KAAK,CAAC,OAAO,GAACtB,CAAC,CAAC;MAAA,CAAC,MAAK;QAAC,IAAG,EAAEK,CAAC,GAAC,EAAE,CAAC,EAAC,MAAM,IAAIiB,KAAK,CAAC,OAAO,GAACjB,CAAC,CAAC;QAAC,QAAOL,CAAC;UAAE,KAAKE,CAAC,CAACsE,WAAW;YAAC,OAAO,EAAE;UAAC,KAAKtE,CAAC,CAACuE,cAAc;YAAC,OAAO,EAAE;UAAC,KAAKvE,CAAC,CAACC,cAAc;YAAC,OAAO,EAAE;UAAC,KAAKD,CAAC,CAACwE,UAAU;YAAC,OAAO,EAAE;UAAC;YAAQ,MAAM,IAAIpD,KAAK,CAAC,OAAO,GAACtB,CAAC,CAAC;QAAA;MAAC;IAAC,CAAC;IAAC0C,YAAY,EAAC,sBAAS1C,CAAC,EAAC;MAAC,KAAI,IAAIK,CAAC,GAACL,CAAC,CAACuB,cAAc,EAAE,EAACrB,CAAC,GAAC,CAAC,EAAC0B,CAAC,GAAC,CAAC,EAACA,CAAC,GAACvB,CAAC,EAACuB,CAAC,EAAE;QAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACxB,CAAC,EAACwB,CAAC,EAAE,EAAC;UAAC,KAAI,IAAIE,CAAC,GAAC,CAAC,EAACc,CAAC,GAAC7C,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,CAAC,EAACiB,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE;YAAC,IAAG,EAAElB,CAAC,GAACkB,CAAC,GAAC,CAAC,IAAEzC,CAAC,IAAEuB,CAAC,GAACkB,CAAC,CAAC,EAAC,KAAI,IAAIS,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,IAAE,CAAC,EAACA,CAAC,EAAE;cAAC1B,CAAC,GAAC0B,CAAC,GAAC,CAAC,IAAElD,CAAC,IAAEwB,CAAC,GAAC0B,CAAC,IAAE,CAAC,IAAET,CAAC,IAAE,CAAC,IAAES,CAAC,IAAEV,CAAC,IAAE7C,CAAC,CAACqB,MAAM,CAACO,CAAC,GAACkB,CAAC,EAACjB,CAAC,GAAC0B,CAAC,CAAC,IAAExB,CAAC,EAAE;YAAC;UAAA;UAAAA,CAAC,GAAC,CAAC,KAAG7B,CAAC,IAAE,CAAC,GAAC6B,CAAC,GAAC,CAAC,CAAC;QAAC;MAAC;MAAA,KAAIH,CAAC,GAAC,CAAC,EAACA,CAAC,GAACvB,CAAC,GAAC,CAAC,EAACuB,CAAC,EAAE;QAAC,KAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACxB,CAAC,GAAC,CAAC,EAACwB,CAAC,EAAE,EAAC;UAAC,IAAIjC,CAAC,GAAC,CAAC;UAACI,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,CAAC,IAAEjC,CAAC,EAAE,EAACI,CAAC,CAACqB,MAAM,CAACO,CAAC,GAAC,CAAC,EAACC,CAAC,CAAC,IAAEjC,CAAC,EAAE,EAACI,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,IAAEjC,CAAC,EAAE,EAACI,CAAC,CAACqB,MAAM,CAACO,CAAC,GAAC,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,IAAEjC,CAAC,EAAE,EAAC,CAAC,IAAEA,CAAC,IAAE,CAAC,IAAEA,CAAC,KAAGM,CAAC,IAAE,CAAC,CAAC;QAAC;MAAC;MAAA,KAAI0B,CAAC,GAAC,CAAC,EAACA,CAAC,GAACvB,CAAC,EAACuB,CAAC,EAAE;QAAC,KAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACxB,CAAC,GAAC,CAAC,EAACwB,CAAC,EAAE;UAAC7B,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,CAAC,IAAE,CAAC7B,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,IAAE7B,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,IAAE7B,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,IAAE7B,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,IAAE,CAAC7B,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,IAAE7B,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,KAAG3B,CAAC,IAAE,EAAE,CAAC;QAAC;MAAA;MAAA,KAAI2B,CAAC,GAAC,CAAC,EAACA,CAAC,GAACxB,CAAC,EAACwB,CAAC,EAAE;QAAC,KAAID,CAAC,GAAC,CAAC,EAACA,CAAC,GAACvB,CAAC,GAAC,CAAC,EAACuB,CAAC,EAAE;UAAC5B,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,CAAC,IAAE,CAAC7B,CAAC,CAACqB,MAAM,CAACO,CAAC,GAAC,CAAC,EAACC,CAAC,CAAC,IAAE7B,CAAC,CAACqB,MAAM,CAACO,CAAC,GAAC,CAAC,EAACC,CAAC,CAAC,IAAE7B,CAAC,CAACqB,MAAM,CAACO,CAAC,GAAC,CAAC,EAACC,CAAC,CAAC,IAAE7B,CAAC,CAACqB,MAAM,CAACO,CAAC,GAAC,CAAC,EAACC,CAAC,CAAC,IAAE,CAAC7B,CAAC,CAACqB,MAAM,CAACO,CAAC,GAAC,CAAC,EAACC,CAAC,CAAC,IAAE7B,CAAC,CAACqB,MAAM,CAACO,CAAC,GAAC,CAAC,EAACC,CAAC,CAAC,KAAG3B,CAAC,IAAE,EAAE,CAAC;QAAC;MAAA;MAAA,IAAI6D,CAAC,GAAC,CAAC;MAAC,KAAIlC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACxB,CAAC,EAACwB,CAAC,EAAE;QAAC,KAAID,CAAC,GAAC,CAAC,EAACA,CAAC,GAACvB,CAAC,EAACuB,CAAC,EAAE;UAAC5B,CAAC,CAACqB,MAAM,CAACO,CAAC,EAACC,CAAC,CAAC,IAAEkC,CAAC,EAAE;QAAC;MAAA;MAAA,OAAO7D,CAAC,IAAE,EAAE,IAAEX,IAAI,CAAC8F,GAAG,CAAC,GAAG,GAACtB,CAAC,GAAC1D,CAAC,GAACA,CAAC,GAAC,EAAE,CAAC,GAAC,CAAC,CAAC;IAAA;EAAC,CAAC,EAAC8D,CAAC,GAAC;IAACmB,IAAI,EAAC,cAAStF,CAAC,EAAC;MAAC,IAAGA,CAAC,GAAC,CAAC,EAAC,MAAM,IAAIsB,KAAK,CAAC,OAAO,GAACtB,CAAC,GAAC,GAAG,CAAC;MAAC,OAAOmE,CAAC,CAACoB,SAAS,CAACvF,CAAC,CAAC;IAAA,CAAC;IAACoF,IAAI,EAAC,cAASpF,CAAC,EAAC;MAAC,OAAKA,CAAC,GAAC,CAAC;QAAEA,CAAC,IAAE,GAAG;MAAC;MAAA,OAAKA,CAAC,IAAE,GAAG;QAAEA,CAAC,IAAE,GAAG;MAAC;MAAA,OAAOmE,CAAC,CAACqB,SAAS,CAACxF,CAAC,CAAC;IAAA,CAAC;IAACwF,SAAS,EAAC,IAAI5E,KAAK,CAAC,GAAG,CAAC;IAAC2E,SAAS,EAAC,IAAI3E,KAAK,CAAC,GAAG;EAAC,CAAC,EAAC7B,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,CAAC,EAACA,CAAC,EAAE;EAACoF,CAAC,CAACqB,SAAS,CAACzG,CAAC,CAAC,GAAC,CAAC,IAAEA,CAAC;AAAC;AAAA,KAAIA,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,GAAG,EAACA,CAAC,EAAE;EAACoF,CAAC,CAACqB,SAAS,CAACzG,CAAC,CAAC,GAACoF,CAAC,CAACqB,SAAS,CAACzG,CAAC,GAAC,CAAC,CAAC,GAACoF,CAAC,CAACqB,SAAS,CAACzG,CAAC,GAAC,CAAC,CAAC,GAACoF,CAAC,CAACqB,SAAS,CAACzG,CAAC,GAAC,CAAC,CAAC,GAACoF,CAAC,CAACqB,SAAS,CAACzG,CAAC,GAAC,CAAC,CAAC;AAAC;AAAA,KAAIA,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,GAAG,EAACA,CAAC,EAAE;EAACoF,CAAC,CAACoB,SAAS,CAACpB,CAAC,CAACqB,SAAS,CAACzG,CAAC,CAAC,CAAC,GAACA,CAAC;AAAC;AAAA,SAASqF,CAAC,CAACpE,CAAC,EAACK,CAAC,EAAC;EAAC,IAAG,IAAI,IAAEL,CAAC,CAACe,MAAM,EAAC,MAAM,IAAIO,KAAK,CAACtB,CAAC,CAACe,MAAM,GAAC,GAAG,GAACV,CAAC,CAAC;EAAC,KAAI,IAAIH,CAAC,GAAC,CAAC,EAACA,CAAC,GAACF,CAAC,CAACe,MAAM,IAAE,CAAC,IAAEf,CAAC,CAACE,CAAC,CAAC;IAAEA,CAAC,EAAE;EAAC;EAAA,IAAI,CAACuF,GAAG,GAAC,IAAI7E,KAAK,CAACZ,CAAC,CAACe,MAAM,GAACb,CAAC,GAACG,CAAC,CAAC;EAAC,KAAI,IAAIuB,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC5B,CAAC,CAACe,MAAM,GAACb,CAAC,EAAC0B,CAAC,EAAE;IAAC,IAAI,CAAC6D,GAAG,CAAC7D,CAAC,CAAC,GAAC5B,CAAC,CAAC4B,CAAC,GAAC1B,CAAC,CAAC;EAAC;AAAA;AAAC,SAASuB,CAAC,CAACzB,CAAC,EAACK,CAAC,EAAC;EAAC,IAAI,CAACwD,UAAU,GAAC7D,CAAC,EAAC,IAAI,CAAC8B,SAAS,GAACzB,CAAC;AAAC;AAAC,SAASsB,CAAC,GAAE;EAAC,IAAI,CAACqC,MAAM,GAAC,IAAIpD,KAAK,IAAC,IAAI,CAACG,MAAM,GAAC,CAAC;AAAC;AAAC,SAASwD,CAAC,CAACvE,CAAC,EAAC;EAAC,OAAOA,CAAC,CAAC0F,YAAY,GAAC1F,CAAC,CAAC0F,YAAY,IAAE,UAASrF,CAAC,EAAC;IAACL,CAAC,CAAC2F,SAAS,GAACtF,CAAC;EAAC,CAAC,EAACL,CAAC,CAAC4F,WAAW,GAAC5F,CAAC,CAAC4F,WAAW,IAAE,UAASvF,CAAC,EAAC;IAACL,CAAC,CAAC6F,IAAI,aAAIxF,CAAC,OAAI;EAAC,CAAC,EAACL,CAAC,CAAC8F,YAAY,GAAC9F,CAAC,CAAC8F,YAAY,IAAE,UAASzF,CAAC,EAAC;IAACL,CAAC,CAAC+F,SAAS,GAAC1F,CAAC;EAAC,CAAC,EAACL,CAAC,CAACgG,eAAe,GAAChG,CAAC,CAACgG,eAAe,IAAE,UAAS3F,CAAC,EAAC;IAACL,CAAC,CAACiG,YAAY,GAAC5F,CAAC;EAAC,CAAC,EAACL,CAAC,CAACkG,cAAc,GAAClG,CAAC,CAACkG,cAAc,IAAE,UAAS7F,CAAC,EAAC;IAACL,CAAC,CAACmG,WAAW,GAAC9F,CAAC;EAAC,CAAC,EAACL,CAAC,CAACoG,cAAc,GAACpG,CAAC,CAACoG,cAAc,IAAE,UAAS/F,CAAC,EAAC;IAACL,CAAC,CAACqG,WAAW,GAAChG,CAAC;EAAC,CAAC,EAACL,CAAC,CAACsG,SAAS,GAACtG,CAAC,CAACsG,SAAS,IAAE,UAASjG,CAAC,EAACH,CAAC,EAAC0B,CAAC,EAACC,CAAC,EAAC;IAAC7B,CAAC,CAACuG,aAAa,GAAClG,CAAC,EAACL,CAAC,CAACwG,aAAa,GAACtG,CAAC,EAACF,CAAC,CAACyG,UAAU,GAAC7E,CAAC,EAAC5B,CAAC,CAAC0G,WAAW,GAAC7E,CAAC;EAAC,CAAC,EAAC7B,CAAC,CAAC2G,IAAI,GAAC3G,CAAC,CAAC2G,IAAI,IAAE,UAAS3G,CAAC,EAACK,CAAC,EAAC;IAACA,CAAC,IAAEA,CAAC,EAAE;EAAC,CAAC,EAACL,CAAC,CAAC4G,SAAS,GAAC5G,CAAC,CAAC4G,SAAS,IAAE,UAASvG,CAAC,EAACH,CAAC,EAAC0B,CAAC,EAACC,CAAC,EAAC;IAAC7B,CAAC,CAAC2G,IAAI,CAAC,CAAC,CAAC,CAAC;EAAC,CAAC,EAAC3G,CAAC;AAAA;AAAC,SAAS6G,CAAC,CAAC7G,CAAC,EAACK,CAAC,EAAC;EAAA;EAAC,IAAIH,CAAC,GAAC,IAAI,CAACE,IAAI,GAAC,EAAE;IAACwB,CAAC,GAAC,IAAI,CAACkF,IAAI,GAAC,GAAG;EAAC,IAAI,CAACC,cAAc,GAAC,CAAC,CAAC,EAAC,IAAI,CAACC,WAAW,GAACpF,CAAC;EAAC,IAAIC,CAAC,GAAC,IAAI,CAACvB,UAAU,GAAC,CAAC,CAAC;EAAC,IAAI,CAACC,iBAAiB,GAACsG,CAAC,CAACtG,iBAAiB,CAACsE,CAAC;EAAC,IAAI9C,CAAC,GAAC,IAAI,CAACkF,MAAM,GAAC,CAAC;EAAC,IAAI,CAACC,SAAS,GAAC,SAAS,EAAC,IAAI,CAACC,eAAe,GAAC,qBAAqB,EAAC,IAAI,CAACC,kBAAkB,GAAC,KAAK,CAAC;EAAC,IAAIvE,CAAC,GAAC,IAAI,CAACwE,oBAAoB,GAAC,KAAK,CAAC;IAACvE,CAAC,GAAC,IAAI,CAACwE,qBAAqB,GAAC,KAAK,CAAC;IAAC/D,CAAC,GAAC,IAAI,CAACgE,gBAAgB,GAAC,KAAK,CAAC;IAAC3H,CAAC,GAAC,IAAI,CAAC4H,gBAAgB,GAAC,KAAK,CAAC;EAAC,IAAI,CAACC,oBAAoB,GAAC,CAAC,EAAC,IAAI,CAACC,2BAA2B,GAAC,CAAC;EAAC,IAAI3D,CAAC,GAAC,IAAI,CAAC4D,iBAAiB,GAAC,CAAC;EAAC,IAAI,CAACC,eAAe,GAAC,SAAS,EAAC,IAAI,CAACC,kBAAkB,GAAC,KAAK,CAAC;EAAC,IAAI5D,CAAC,GAAC,IAAI,CAAC6D,oBAAoB,GAAC,KAAK,CAAC;IAACjJ,CAAC,GAAC,IAAI,CAACkJ,qBAAqB,GAAC,KAAK,CAAC;IAAC5D,CAAC,GAAC,IAAI,CAAC6D,gBAAgB,GAAC,KAAK,CAAC;IAACjJ,CAAC,GAAC,IAAI,CAACkJ,gBAAgB,GAAC,KAAK,CAAC;IAAC7D,CAAC,GAAC,IAAI,CAAC8D,sBAAsB,GAAC,CAAC;EAAC,IAAI,CAACC,8BAA8B,GAAC,SAAS;EAAC,IAAI1G,CAAC,GAAC,IAAI,CAAC2G,2BAA2B,GAAC,CAAC;IAACzG,CAAC,GAAC,IAAI,CAAC0G,4BAA4B,GAAC,CAAC;IAACC,CAAC,GAAC,IAAI,CAACC,4BAA4B,GAAC,CAAC;IAACC,CAAC,GAAC,IAAI,CAACC,yBAAyB,GAAC,CAAC;EAAC,IAAI,CAACC,0BAA0B,GAAC,SAAS;EAAC,IAAIC,CAAC,GAAC,IAAI,CAACC,iBAAiB,GAAC,CAAC;IAACC,CAAC,GAAC,IAAI,CAACC,4BAA4B,GAAC,KAAK,CAAC;IAACC,CAAC,GAAC,IAAI,CAACC,4BAA4B,GAAC,KAAK,CAAC;IAACC,CAAC,GAAC,IAAI,CAACC,cAAc,GAAC,KAAK,CAAC;IAACC,CAAC,GAAC,IAAI,CAACC,6BAA6B,GAAC,KAAK,CAAC;IAACzE,CAAC,GAAC,IAAI,CAAC0E,6BAA6B,GAAC,KAAK,CAAC;IAAC1K,CAAC,GAAC,IAAI,CAAC2K,qBAAqB,GAAC,KAAK,CAAC;IAACC,CAAC,GAAC,IAAI,CAACC,qBAAqB,GAAC,KAAK,CAAC;IAACC,CAAC,GAAC,IAAI,CAACC,yBAAyB,GAAC,KAAK,CAAC;IAACC,CAAC,GAAC,IAAI,CAACC,yBAAyB,GAAC,KAAK,CAAC;IAACC,CAAC,GAAC,IAAI,CAACC,cAAc,GAAC,KAAK,CAAC;EAAC,IAAI,CAACC,IAAI,GAAC,KAAK,CAAC,EAAC,IAAI,CAACvJ,OAAO,GAAC,EAAE,EAAC,IAAI,CAACC,WAAW,GAAC,CAAC,EAAC,IAAI,CAACuJ,WAAW,GAAC,EAAE;EAAC,IAAIvL,CAAC,GAAC,IAAI,CAACwL,aAAa,GAAC,KAAK,CAAC;EAAC,IAAI,CAACC,SAAS,EAAC,IAAI,CAACC,WAAW,GAAC,CAAC,CAAC,EAAC,IAAI,CAACC,OAAO,GAAC,CAAC,CAAC,EAACC,MAAM,CAACC,gBAAgB,CAAC,IAAI,EAAC;IAAClK,IAAI,EAAC;MAACkE,GAAG,iBAAE;QAAC,IAAG,EAAE,KAAGpE,CAAC,IAAE,KAAK,CAAC,KAAGA,CAAC,EAAC,MAAMqK,OAAO,CAACC,KAAK,CAAC,8BAA8B,CAAC,EAAC,IAAI3D,CAAC,CAACvF,KAAK,CAAC,mBAAmB,CAAC;QAAC,OAAOpB,CAAC;MAAA,CAAC;MAACuK,GAAG,eAACzK,CAAC,EAAC;QAACE,CAAC,GAACwK,MAAM,CAAC1K,CAAC,CAAC;MAAC;IAAC,CAAC;IAAC8G,IAAI,EAAC;MAACxC,GAAG,EAAC;QAAA,OAAI1C,CAAC;MAAA;MAAC6I,GAAG,eAACzK,CAAC,EAAC;QAAC4B,CAAC,GAAC+I,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACM,UAAU,EAAC;MAACgE,GAAG,EAAC;QAAA,OAAIzC,CAAC;MAAA;MAAC4I,GAAG,eAACzK,CAAC,EAAC;QAAC6B,CAAC,GAAC8I,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACiH,MAAM,EAAC;MAAC3C,GAAG,EAAC;QAAA,OAAIvC,CAAC;MAAA;MAAC0I,GAAG,eAACzK,CAAC,EAAC;QAAC+B,CAAC,GAAC4I,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACqH,oBAAoB,EAAC;MAAC/C,GAAG,iBAAE;QAAC,OAAO,KAAK,CAAC,KAAGzB,CAAC,GAAC,IAAI,CAACmE,WAAW,GAAC,IAAI,CAACD,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAACjE,CAAC,GAACA,CAAC;MAAA,CAAC;MAAC4H,GAAG,eAACzK,CAAC,EAAC;QAAC6C,CAAC,GAAC8H,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACsH,qBAAqB,EAAC;MAAChD,GAAG,iBAAE;QAAC,OAAO,KAAK,CAAC,KAAGxB,CAAC,GAAC,IAAI,CAACkE,WAAW,GAAC,IAAI,CAACD,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAAChE,CAAC,GAACA,CAAC;MAAA,CAAC;MAAC2H,GAAG,eAACzK,CAAC,EAAC;QAAC8C,CAAC,GAAC6H,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACuH,gBAAgB,EAAC;MAACjD,GAAG,iBAAE;QAAC,OAAO,KAAK,CAAC,KAAGf,CAAC,GAAC,CAAC,GAAC,IAAI,CAACwD,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAACvD,CAAC,GAACA,CAAC;MAAA,CAAC;MAACkH,GAAG,eAACzK,CAAC,EAAC;QAACuD,CAAC,GAACoH,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACwH,gBAAgB,EAAC;MAAClD,GAAG,iBAAE;QAAC,OAAO,KAAK,CAAC,KAAG1E,CAAC,GAAC,CAAC,GAAC,IAAI,CAACmH,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAAClH,CAAC,GAACA,CAAC;MAAA,CAAC;MAAC6K,GAAG,eAACzK,CAAC,EAAC;QAACJ,CAAC,GAAC+K,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAAC2H,iBAAiB,EAAC;MAACrD,GAAG,EAAC;QAAA,OAAIP,CAAC;MAAA;MAAC0G,GAAG,eAACzK,CAAC,EAAC;QAAC+D,CAAC,GAAC/D,CAAC,GAAC,CAAC,GAAC,CAAC,GAACA,CAAC,GAAC,CAAC,GAAC,CAAC,GAACA,CAAC;MAAC;IAAC,CAAC;IAAC8H,oBAAoB,EAAC;MAACxD,GAAG,iBAAE;QAAC,OAAO,KAAK,CAAC,KAAGL,CAAC,GAAC,CAAC,IAAI,CAAC+C,WAAW,GAAC,CAAC,GAAC,IAAI,CAACC,MAAM,IAAE,CAAC,GAAC,IAAI,CAACF,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAAC7C,CAAC,GAACA,CAAC;MAAA,CAAC;MAACwG,GAAG,eAACzK,CAAC,EAAC;QAACiE,CAAC,GAAC0G,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAAC+H,qBAAqB,EAAC;MAACzD,GAAG,iBAAE;QAAC,OAAO,KAAK,CAAC,KAAGzF,CAAC,GAAC,CAAC,IAAI,CAACmI,WAAW,GAAC,CAAC,GAAC,IAAI,CAACC,MAAM,IAAE,CAAC,GAAC,IAAI,CAACF,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAACjI,CAAC,GAACA,CAAC;MAAA,CAAC;MAAC4L,GAAG,eAACzK,CAAC,EAAC;QAACnB,CAAC,GAAC8L,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACgI,gBAAgB,EAAC;MAAC1D,GAAG,iBAAE;QAAC,OAAO,KAAK,CAAC,KAAGH,CAAC,GAAC,IAAI,CAAC6C,WAAW,GAAC,CAAC,GAAC,IAAI,CAACc,oBAAoB,GAAC,CAAC,GAAC,IAAI,CAACf,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAAC3C,CAAC,GAACA,CAAC;MAAA,CAAC;MAACsG,GAAG,eAACzK,CAAC,EAAC;QAACmE,CAAC,GAACwG,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACiI,gBAAgB,EAAC;MAAC3D,GAAG,iBAAE;QAAC,OAAO,KAAK,CAAC,KAAGvF,CAAC,GAAC,IAAI,CAACiI,WAAW,GAAC,CAAC,GAAC,IAAI,CAACe,qBAAqB,GAAC,CAAC,GAAC,IAAI,CAAChB,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAAC/H,CAAC,GAACA,CAAC;MAAA,CAAC;MAAC0L,GAAG,eAACzK,CAAC,EAAC;QAACjB,CAAC,GAAC4L,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACkI,sBAAsB,EAAC;MAAC5D,GAAG,iBAAE;QAAC,OAAO,IAAI,CAACyC,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAAC1C,CAAC,GAACA,CAAC;MAAA,CAAC;MAACqG,GAAG,eAACzK,CAAC,EAAC;QAACoE,CAAC,GAACuG,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACoI,2BAA2B,EAAC;MAAC9D,GAAG,iBAAE;QAAC,OAAO,IAAI,CAACyC,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAACrF,CAAC,GAACA,CAAC;MAAA,CAAC;MAACgJ,GAAG,eAACzK,CAAC,EAAC;QAACyB,CAAC,GAACkJ,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACqI,4BAA4B,EAAC;MAAC/D,GAAG,iBAAE;QAAC,OAAO,IAAI,CAACyC,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAACnF,CAAC,GAACA,CAAC;MAAA,CAAC;MAAC8I,GAAG,eAACzK,CAAC,EAAC;QAAC2B,CAAC,GAACgJ,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACuI,4BAA4B,EAAC;MAACjE,GAAG,iBAAE;QAAC,OAAO,IAAI,CAACyC,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAACwB,CAAC,GAACA,CAAC;MAAA,CAAC;MAACmC,GAAG,eAACzK,CAAC,EAAC;QAACsI,CAAC,GAACqC,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAACyI,yBAAyB,EAAC;MAACnE,GAAG,iBAAE;QAAC,OAAO,IAAI,CAACyC,cAAc,GAAC,IAAI,CAACC,WAAW,GAAC,IAAI,CAACF,IAAI,GAAC0B,CAAC,GAACA,CAAC;MAAA,CAAC;MAACiC,GAAG,eAACzK,CAAC,EAAC;QAACwI,CAAC,GAACmC,MAAM,CAAC3K,CAAC,CAAC;MAAC;IAAC,CAAC;IAAC4I,iBAAiB,EAAC;MAACtE,GAAG,EAAC;QAAA,OAAIqE,CAAC;MAAA;MAAC8B,GAAG,eAACzK,CAAC,EAAC;QAAC2I,CAAC,GAAC3I,CAAC,GAAC,CAAC,GAAC,CAAC,GAACA,CAAC,GAAC,CAAC,GAAC,CAAC,GAACA,CAAC;MAAC;IAAC,CAAC;IAAC8I,4BAA4B,EAAC;MAACxE,GAAG,iBAAE;QAAC,OAAOuE,CAAC,IAAE,IAAI,CAAC1B,eAAe;MAAA,CAAC;MAACsD,GAAG,eAACzK,CAAC,EAAC;QAAC6I,CAAC,GAAC7I,CAAC;MAAC;IAAC,CAAC;IAACgJ,4BAA4B,EAAC;MAAC1E,GAAG,iBAAE;QAAC,OAAOyE,CAAC,IAAE,IAAI,CAACnB,eAAe;MAAA,CAAC;MAAC6C,GAAG,eAACzK,CAAC,EAAC;QAAC+I,CAAC,GAAC/I,CAAC;MAAC;IAAC,CAAC;IAACkJ,cAAc,EAAC;MAAC5E,GAAG,iBAAE;QAAC,OAAO2E,CAAC,IAAE,IAAI,CAAC9B,eAAe;MAAA,CAAC;MAACsD,GAAG,eAACzK,CAAC,EAAC;QAACiJ,CAAC,GAACjJ,CAAC;MAAC;IAAC,CAAC;IAACoJ,6BAA6B,EAAC;MAAC9E,GAAG,iBAAE;QAAC,OAAO6E,CAAC,IAAE,IAAI,CAAChC,eAAe;MAAA,CAAC;MAACsD,GAAG,eAACzK,CAAC,EAAC;QAACmJ,CAAC,GAACnJ,CAAC;MAAC;IAAC,CAAC;IAACqJ,6BAA6B,EAAC;MAAC/E,GAAG,iBAAE;QAAC,OAAOK,CAAC,IAAE,IAAI,CAACiD,eAAe;MAAA,CAAC;MAAC6C,GAAG,eAACzK,CAAC,EAAC;QAAC2E,CAAC,GAAC3E,CAAC;MAAC;IAAC,CAAC;IAACsJ,qBAAqB,EAAC;MAAChF,GAAG,iBAAE;QAAC,OAAO3F,CAAC,IAAE,IAAI,CAACwI,eAAe;MAAA,CAAC;MAACsD,GAAG,eAACzK,CAAC,EAAC;QAACrB,CAAC,GAACqB,CAAC;MAAC;IAAC,CAAC;IAACwJ,qBAAqB,EAAC;MAAClF,GAAG,iBAAE;QAAC,OAAOiF,CAAC,IAAE,IAAI,CAAC3B,eAAe;MAAA,CAAC;MAAC6C,GAAG,eAACzK,CAAC,EAAC;QAACuJ,CAAC,GAACvJ,CAAC;MAAC;IAAC,CAAC;IAAC0J,yBAAyB,EAAC;MAACpF,GAAG,iBAAE;QAAC,OAAOmF,CAAC,IAAE,IAAI,CAACtC,eAAe;MAAA,CAAC;MAACsD,GAAG,eAACzK,CAAC,EAAC;QAACyJ,CAAC,GAACzJ,CAAC;MAAC;IAAC,CAAC;IAAC4J,yBAAyB,EAAC;MAACtF,GAAG,iBAAE;QAAC,OAAOqF,CAAC,IAAE,IAAI,CAAC/B,eAAe;MAAA,CAAC;MAAC6C,GAAG,eAACzK,CAAC,EAAC;QAAC2J,CAAC,GAAC3J,CAAC;MAAC;IAAC,CAAC;IAAC8J,cAAc,EAAC;MAACxF,GAAG,iBAAE;QAAC,OAAOuF,CAAC,IAAE,IAAI,CAACjC,eAAe;MAAA,CAAC;MAAC6C,GAAG,eAACzK,CAAC,EAAC;QAAC6J,CAAC,GAAC7J,CAAC;MAAC;IAAC,CAAC;IAACiK,aAAa,EAAC;MAAC3F,GAAG,iBAAE;QAAC,IAAG,KAAK,CAAC,KAAG7F,CAAC,EAAC,MAAM8L,OAAO,CAACC,KAAK,CAAC,+DAA+D,CAAC,EAAC,IAAI3D,CAAC,CAACvF,KAAK,CAAC,oDAAoD,CAAC;QAAC,OAAO7C,CAAC;MAAA,CAAC;MAACgM,GAAG,eAACzK,CAAC,EAAC;QAACvB,CAAC,GAAC8F,CAAC,CAACvE,CAAC,CAAC;MAAC;IAAC;EAAC,CAAC,CAAC,EAAC6G,CAAC,CAAC+D,OAAO,CAACC,OAAO,CAAE,UAAA7K,CAAC;IAAA,OAAEA,CAAC,CAAC6G,CAAC,EAAC,KAAI,EAAC,CAAC,CAAC,CAAC;EAAA,EAAE,EAAC7G,CAAC,IAAE,IAAI,CAAC8K,UAAU,CAAC9K,CAAC,CAAC,EAACK,CAAC,KAAG,IAAI,CAAC4J,aAAa,GAAC1F,CAAC,CAAClE,CAAC,CAAC,CAAC;AAAC;AAAC+D,CAAC,CAACvD,SAAS,GAAC;EAACyD,GAAG,EAAC,aAAStE,CAAC,EAAC;IAAC,OAAO,IAAI,CAACyF,GAAG,CAACzF,CAAC,CAAC;EAAA,CAAC;EAACc,SAAS,EAAC,qBAAU;IAAC,OAAO,IAAI,CAAC2E,GAAG,CAAC1E,MAAM;EAAA,CAAC;EAACoE,QAAQ,EAAC,kBAASnF,CAAC,EAAC;IAAC,KAAI,IAAIK,CAAC,GAAC,IAAIO,KAAK,CAAC,IAAI,CAACE,SAAS,EAAE,GAACd,CAAC,CAACc,SAAS,EAAE,GAAC,CAAC,CAAC,EAACZ,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACY,SAAS,EAAE,EAACZ,CAAC,EAAE;MAAC,KAAI,IAAI0B,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC5B,CAAC,CAACc,SAAS,EAAE,EAACc,CAAC,EAAE;QAACvB,CAAC,CAACH,CAAC,GAAC0B,CAAC,CAAC,IAAEuC,CAAC,CAACiB,IAAI,CAACjB,CAAC,CAACmB,IAAI,CAAC,IAAI,CAAChB,GAAG,CAACpE,CAAC,CAAC,CAAC,GAACiE,CAAC,CAACmB,IAAI,CAACtF,CAAC,CAACsE,GAAG,CAAC1C,CAAC,CAAC,CAAC,CAAC;MAAC;IAAA;IAAA,OAAO,IAAIwC,CAAC,CAAC/D,CAAC,EAAC,CAAC,CAAC;EAAA,CAAC;EAACgE,GAAG,EAAC,aAASrE,CAAC,EAAC;IAAC,IAAG,IAAI,CAACc,SAAS,EAAE,GAACd,CAAC,CAACc,SAAS,EAAE,GAAC,CAAC,EAAC,OAAO,IAAI;IAAC,KAAI,IAAIT,CAAC,GAAC8D,CAAC,CAACmB,IAAI,CAAC,IAAI,CAAChB,GAAG,CAAC,CAAC,CAAC,CAAC,GAACH,CAAC,CAACmB,IAAI,CAACtF,CAAC,CAACsE,GAAG,CAAC,CAAC,CAAC,CAAC,EAACpE,CAAC,GAAC,IAAIU,KAAK,CAAC,IAAI,CAACE,SAAS,EAAE,CAAC,EAACc,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC,IAAI,CAACd,SAAS,EAAE,EAACc,CAAC,EAAE;MAAC1B,CAAC,CAAC0B,CAAC,CAAC,GAAC,IAAI,CAAC0C,GAAG,CAAC1C,CAAC,CAAC;IAAC;IAAA,KAAIA,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC5B,CAAC,CAACc,SAAS,EAAE,EAACc,CAAC,EAAE;MAAC1B,CAAC,CAAC0B,CAAC,CAAC,IAAEuC,CAAC,CAACiB,IAAI,CAACjB,CAAC,CAACmB,IAAI,CAACtF,CAAC,CAACsE,GAAG,CAAC1C,CAAC,CAAC,CAAC,GAACvB,CAAC,CAAC;IAAC;IAAA,OAAO,IAAI+D,CAAC,CAAClE,CAAC,EAAC,CAAC,CAAC,CAACmE,GAAG,CAACrE,CAAC,CAAC;EAAA;AAAC,CAAC,EAACyB,CAAC,CAACsJ,cAAc,GAAC,CAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,EAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,EAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,EAAE,EAAC,CAAC,EAAC,GAAG,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,GAAG,EAAC,GAAG,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,CAAC,EAACtJ,CAAC,CAACC,WAAW,GAAC,UAAS1B,CAAC,EAACK,CAAC,EAAC;EAAC,IAAIH,CAAC,GAACuB,CAAC,CAACuJ,eAAe,CAAChL,CAAC,EAACK,CAAC,CAAC;EAAC,IAAG,IAAI,IAAEH,CAAC,EAAC,MAAM,IAAIoB,KAAK,CAAC,4BAA4B,GAACtB,CAAC,GAAC,qBAAqB,GAACK,CAAC,CAAC;EAAC,KAAI,IAAIuB,CAAC,GAAC1B,CAAC,CAACa,MAAM,GAAC,CAAC,EAACc,CAAC,GAAC,IAAIjB,KAAK,IAACmB,CAAC,GAAC,CAAC,EAACA,CAAC,GAACH,CAAC,EAACG,CAAC,EAAE;IAAC,KAAI,IAAIc,CAAC,GAAC3C,CAAC,CAAC,CAAC,GAAC6B,CAAC,GAAC,CAAC,CAAC,EAACe,CAAC,GAAC5C,CAAC,CAAC,CAAC,GAAC6B,CAAC,GAAC,CAAC,CAAC,EAACwB,CAAC,GAACrD,CAAC,CAAC,CAAC,GAAC6B,CAAC,GAAC,CAAC,CAAC,EAACnC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACiD,CAAC,EAACjD,CAAC,EAAE;MAACiC,CAAC,CAACT,IAAI,CAAC,IAAIK,CAAC,CAACqB,CAAC,EAACS,CAAC,CAAC,CAAC;IAAC;EAAA;EAAA,OAAO1B,CAAC;AAAA,CAAC,EAACJ,CAAC,CAACuJ,eAAe,GAAC,UAAShL,CAAC,EAACK,CAAC,EAAC;EAAC,QAAOA,CAAC;IAAE,KAAKuB,CAAC,CAAC+C,CAAC;MAAC,OAAOlD,CAAC,CAACsJ,cAAc,CAAC,CAAC,IAAE/K,CAAC,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC;IAAC,KAAK4B,CAAC,CAACnD,CAAC;MAAC,OAAOgD,CAAC,CAACsJ,cAAc,CAAC,CAAC,IAAE/K,CAAC,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC;IAAC,KAAK4B,CAAC,CAACgD,CAAC;MAAC,OAAOnD,CAAC,CAACsJ,cAAc,CAAC,CAAC,IAAE/K,CAAC,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC;IAAC,KAAK4B,CAAC,CAACiD,CAAC;MAAC,OAAOpD,CAAC,CAACsJ,cAAc,CAAC,CAAC,IAAE/K,CAAC,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC;IAAC;MAAQ;EAAM;AAAC,CAAC,EAAC2B,CAAC,CAACd,SAAS,GAAC;EAACyD,GAAG,EAAC,aAAStE,CAAC,EAAC;IAAC,IAAIK,CAAC,GAACd,IAAI,CAAC8D,KAAK,CAACrD,CAAC,GAAC,CAAC,CAAC;IAAC,OAAO,CAAC,KAAG,IAAI,CAACgE,MAAM,CAAC3D,CAAC,CAAC,KAAG,CAAC,GAACL,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC;EAAA,CAAC;EAACiB,GAAG,EAAC,aAASjB,CAAC,EAACK,CAAC,EAAC;IAAC,KAAI,IAAIH,CAAC,GAAC,CAAC,EAACA,CAAC,GAACG,CAAC,EAACH,CAAC,EAAE;MAAC,IAAI,CAACyD,MAAM,CAAC,CAAC,KAAG3D,CAAC,KAAGK,CAAC,GAACH,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,CAAC;IAAC;EAAA,CAAC;EAAC8B,eAAe,EAAC,2BAAU;IAAC,OAAO,IAAI,CAACjB,MAAM;EAAA,CAAC;EAAC4C,MAAM,EAAC,gBAAS3D,CAAC,EAAC;IAAC,IAAIK,CAAC,GAACd,IAAI,CAAC8D,KAAK,CAAC,IAAI,CAACtC,MAAM,GAAC,CAAC,CAAC;IAAC,IAAI,CAACiD,MAAM,CAACjD,MAAM,IAAEV,CAAC,IAAE,IAAI,CAAC2D,MAAM,CAAC5C,IAAI,CAAC,CAAC,CAAC,EAACpB,CAAC,KAAG,IAAI,CAACgE,MAAM,CAAC3D,CAAC,CAAC,IAAE,GAAG,KAAG,IAAI,CAACU,MAAM,GAAC,CAAC,CAAC,EAAC,IAAI,CAACA,MAAM,EAAE;EAAC;AAAC,CAAC,EAACV,CAAC,CAACE,iBAAiB,GAACqB,CAAC,EAACiF,CAAC,CAACtG,iBAAiB,GAACF,CAAC,CAACE,iBAAiB,EAACsG,CAAC,CAACvF,KAAK,GAAC,UAAStB,CAAC,EAAC;EAAC,IAAI,CAACiL,MAAM,GAAC,aAAa,GAACjL,CAAC;AAAC,CAAC,EAAC6G,CAAC,CAAC+D,OAAO,GAAC,EAAE,EAAC/D,CAAC,CAACqE,GAAG,GAAC,UAASlL,CAAC,EAAC;EAAC,UAAU,IAAE,OAAOA,CAAC,IAAE6G,CAAC,CAAC+D,OAAO,CAACxJ,IAAI,CAACpB,CAAC,CAAC;AAAC,CAAC,EAAC6G,CAAC,CAAChG,SAAS,CAACqJ,SAAS,GAAC,UAASlK,CAAC,EAAC;EAAC,OAAOmL,OAAO,CAACC,OAAO,CAACpL,CAAC,CAAC;AAAA,CAAC,EAAC6G,CAAC,CAAChG,SAAS,CAACiK,UAAU,GAAC,UAAS9K,CAAC,EAAC;EAAA;EAAC,IAAIK,CAAC,EAACH,CAAC,EAAC0B,CAAC,EAACC,CAAC,EAACE,CAAC,EAACc,CAAC,EAACC,CAAC,EAACS,CAAC,EAAC3D,CAAC,EAACmE,CAAC,EAACE,CAAC,EAACpF,CAAC,EAACsF,CAAC,EAACpF,CAAC,EAACqF,CAAC,EAAC3C,CAAC,EAACE,CAAC,EAAC4C,CAAC,EAACsC,CAAC,EAACyB,CAAC,EAACE,CAAC,EAACG,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACxE,CAAC,EAAChG,CAAC,EAAC4K,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACpL,CAAC,EAAC4M,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC7G,CAAC,EAAC8G,CAAC,EAACpN,CAAC,EAACqN,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACnH,CAAC,EAACoH,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,EAAE,EAACC,EAAE,EAACC,EAAE;EAACxM,CAAC,KAAGqK,MAAM,CAACoC,IAAI,CAACzM,CAAC,CAAC,CAAC6K,OAAO,CAAE,UAAAxK,CAAC,EAAE;IAAC,MAAI,CAACA,CAAC,CAAC,GAACL,CAAC,CAACK,CAAC,CAAC;EAAC,CAAC,CAAE,EAAC,YAAwB;IAAA,IAAfL,CAAC,uEAAC,CAAC,CAAC;IAAA,IAACK,CAAC,uEAAC,CAAC,CAAC;IAAA,IAACH,CAAC,uEAAC,CAAC,CAAC;IAAE,IAAI0B,CAAC;IAACA,CAAC,GAAC1B,CAAC,GAACF,CAAC,qBAAKA,CAAC,CAAC;IAAC,KAAI,IAAIA,EAAC,IAAIK,CAAC,EAAC;MAAC,IAAIwB,CAAC,GAACxB,CAAC,CAACL,EAAC,CAAC;MAAC,IAAI,IAAE6B,CAAC,KAAGA,CAAC,CAAC6K,WAAW,IAAErC,MAAM,GAACzI,CAAC,CAAC5B,EAAC,CAAC,GAAC,IAAI,CAAC2M,WAAW,CAAC/K,CAAC,CAAC5B,EAAC,CAAC,EAAC6B,CAAC,CAAC,GAACA,CAAC,CAAC6K,WAAW,IAAEhC,MAAM,IAAE7I,CAAC,GAACD,CAAC,CAAC5B,EAAC,CAAC,GAAC6B,CAAC,GAACD,CAAC,CAAC5B,EAAC,CAAC,GAAC4B,CAAC,CAAC5B,EAAC,CAAC,CAAC;IAAC;EAAC,CAAC,CAAC,IAAI,EAAC;IAACI,IAAI,EAACJ,CAAC,CAACI,IAAI,IAAEJ,CAAC,CAAC4M,IAAI;IAAC9F,IAAI,EAAC9G,CAAC,CAAC8G,IAAI;IAACC,cAAc,EAAC/G,CAAC,CAAC+G,cAAc;IAACzG,UAAU,EAACN,CAAC,CAACM,UAAU;IAACC,iBAAiB,EAACP,CAAC,CAACO,iBAAiB;IAAC0G,MAAM,EAACjH,CAAC,CAACiH,MAAM;IAACC,SAAS,EAAClH,CAAC,CAACkH,SAAS;IAACC,eAAe,EAACnH,CAAC,CAACmH,eAAe,KAAG,IAAI,MAAI9G,CAAC,GAACL,CAAC,CAAC6M,UAAU,CAAC,IAAE,KAAK,CAAC,KAAGxM,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACyM,KAAK,CAAC;IAAC1F,kBAAkB,EAACpH,CAAC,CAACoH,kBAAkB,KAAG,IAAI,MAAIlH,CAAC,GAACF,CAAC,CAAC6M,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG3M,CAAC,IAAE,IAAI,MAAI0B,CAAC,GAAC1B,CAAC,CAAC6M,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGnL,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACoL,GAAG,CAAC;IAAC3F,oBAAoB,EAACrH,CAAC,CAACqH,oBAAoB,KAAG,IAAI,MAAIxF,CAAC,GAAC7B,CAAC,CAAC6M,UAAU,CAAC,IAAE,KAAK,CAAC,KAAGhL,CAAC,IAAE,IAAI,MAAIE,CAAC,GAACF,CAAC,CAACkL,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGhL,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACkL,KAAK,CAAC;IAAC3F,qBAAqB,EAACtH,CAAC,CAACsH,qBAAqB,KAAG,IAAI,MAAIzE,CAAC,GAAC7C,CAAC,CAAC6M,UAAU,CAAC,IAAE,KAAK,CAAC,KAAGhK,CAAC,IAAE,IAAI,MAAIC,CAAC,GAACD,CAAC,CAACkK,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGjK,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACoK,MAAM,CAAC;IAAC3F,gBAAgB,EAACvH,CAAC,CAACuH,gBAAgB,KAAG,IAAI,MAAIhE,CAAC,GAACvD,CAAC,CAAC6M,UAAU,CAAC,IAAE,KAAK,CAAC,KAAGtJ,CAAC,IAAE,IAAI,MAAI3D,CAAC,GAAC2D,CAAC,CAACwJ,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGnN,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC8L,CAAC,CAAC;IAAClE,gBAAgB,EAACxH,CAAC,CAACwH,gBAAgB,KAAG,IAAI,MAAIzD,CAAC,GAAC/D,CAAC,CAAC6M,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG9I,CAAC,IAAE,IAAI,MAAIE,CAAC,GAACF,CAAC,CAACgJ,KAAK,CAAC,IAAE,KAAK,CAAC,KAAG9I,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACuE,CAAC,CAAC;IAACf,oBAAoB,EAACzH,CAAC,CAACyH,oBAAoB,KAAG,IAAI,MAAI5I,CAAC,GAACmB,CAAC,CAAC6M,UAAU,CAAC,IAAE,KAAK,CAAC,KAAGhO,CAAC,IAAE,IAAI,MAAIsF,CAAC,GAACtF,CAAC,CAACkO,KAAK,CAAC,IAAE,KAAK,CAAC,KAAG5I,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACgJ,KAAK,CAAC;IAACzF,2BAA2B,EAAC1H,CAAC,CAAC0H,2BAA2B,KAAG,IAAI,MAAI3I,CAAC,GAACiB,CAAC,CAAC6M,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG9N,CAAC,IAAE,IAAI,MAAIqF,CAAC,GAACrF,CAAC,CAACgO,KAAK,CAAC,IAAE,KAAK,CAAC,KAAG3I,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACgJ,YAAY,CAAC;IAACzF,iBAAiB,EAAC3H,CAAC,CAAC2H,iBAAiB;IAACC,eAAe,EAAC5H,CAAC,CAAC4H,eAAe,KAAG,IAAI,MAAInG,CAAC,GAACzB,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG5L,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACqL,KAAK,CAAC;IAACjF,kBAAkB,EAAC7H,CAAC,CAAC6H,kBAAkB,KAAG,IAAI,MAAIlG,CAAC,GAAC3B,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG1L,CAAC,IAAE,IAAI,MAAI4C,CAAC,GAAC5C,CAAC,CAACoL,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGxI,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACyI,GAAG,CAAC;IAAClF,oBAAoB,EAAC9H,CAAC,CAAC8H,oBAAoB,KAAG,IAAI,MAAIjB,CAAC,GAAC7G,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAGxG,CAAC,IAAE,IAAI,MAAIyB,CAAC,GAACzB,CAAC,CAACkG,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGzE,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC2E,KAAK,CAAC;IAAClF,qBAAqB,EAAC/H,CAAC,CAAC+H,qBAAqB,KAAG,IAAI,MAAIS,CAAC,GAACxI,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG7E,CAAC,IAAE,IAAI,MAAIG,CAAC,GAACH,CAAC,CAACuE,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGpE,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACuE,MAAM,CAAC;IAAClF,gBAAgB,EAAChI,CAAC,CAACgI,gBAAgB,KAAG,IAAI,MAAIa,CAAC,GAAC7I,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAGxE,CAAC,IAAE,IAAI,MAAIE,CAAC,GAACF,CAAC,CAACkE,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGhE,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC2C,CAAC,CAAC;IAACzD,gBAAgB,EAACjI,CAAC,CAACiI,gBAAgB,KAAG,IAAI,MAAIgB,CAAC,GAACjJ,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAGpE,CAAC,IAAE,IAAI,MAAIE,CAAC,GAACF,CAAC,CAAC8D,KAAK,CAAC,IAAE,KAAK,CAAC,KAAG5D,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACX,CAAC,CAAC;IAACN,sBAAsB,EAAClI,CAAC,CAACkI,sBAAsB,KAAG,IAAI,MAAIvD,CAAC,GAAC3E,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG1I,CAAC,IAAE,IAAI,MAAIhG,CAAC,GAACgG,CAAC,CAACoI,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGpO,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC2O,OAAO,CAAC;IAACnF,8BAA8B,EAACnI,CAAC,CAACmI,8BAA8B,KAAG,IAAI,MAAIoB,CAAC,GAACvJ,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG9D,CAAC,IAAE,IAAI,MAAIE,CAAC,GAACF,CAAC,CAACwD,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGtD,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACtC,eAAe,CAAC;IAACiB,2BAA2B,EAACpI,CAAC,CAACoI,2BAA2B,KAAG,IAAI,MAAIuB,CAAC,GAAC3J,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG1D,CAAC,IAAE,IAAI,MAAIE,CAAC,GAACF,CAAC,CAACoD,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGlD,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACuD,YAAY,CAAC;IAAC/E,4BAA4B,EAACrI,CAAC,CAACqI,4BAA4B,KAAG,IAAI,MAAI5J,CAAC,GAACuB,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG5O,CAAC,IAAE,IAAI,MAAI4M,CAAC,GAAC5M,CAAC,CAACsO,KAAK,CAAC,IAAE,KAAK,CAAC,KAAG1B,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC9E,aAAa,CAAC;IAACgC,4BAA4B,EAACvI,CAAC,CAACuI,4BAA4B,KAAG,IAAI,MAAI+C,CAAC,GAACtL,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG/B,CAAC,IAAE,IAAI,MAAIC,CAAC,GAACD,CAAC,CAACyB,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGxB,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC/E,aAAa,CAAC;IAACiC,yBAAyB,EAACzI,CAAC,CAACyI,yBAAyB,KAAG,IAAI,MAAI+C,CAAC,GAACxL,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG7B,CAAC,IAAE,IAAI,MAAIC,CAAC,GAACD,CAAC,CAACuB,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGtB,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAChF,UAAU,CAAC;IAACiC,0BAA0B,EAAC1I,CAAC,CAAC0I,0BAA0B,KAAG,IAAI,MAAIgD,CAAC,GAAC1L,CAAC,CAACqN,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG3B,CAAC,IAAE,IAAI,MAAI7G,CAAC,GAAC6G,CAAC,CAACqB,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGlI,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC6B,WAAW,CAAC;IAACkC,iBAAiB,EAAC5I,CAAC,CAAC4I,iBAAiB;IAACE,4BAA4B,EAAC9I,CAAC,CAAC8I,4BAA4B,KAAG,IAAI,MAAI6C,CAAC,GAAC3L,CAAC,CAACuN,aAAa,CAAC,IAAE,KAAK,CAAC,KAAG5B,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACxE,eAAe,CAAC,KAAG,IAAI,MAAI5I,CAAC,GAACyB,CAAC,CAACwN,iBAAiB,CAAC,IAAE,KAAK,CAAC,KAAGjP,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC4I,eAAe,CAAC;IAAC6B,4BAA4B,EAAChJ,CAAC,CAACgJ,4BAA4B,KAAG,IAAI,MAAI4C,CAAC,GAAC5L,CAAC,CAACuN,aAAa,CAAC,IAAE,KAAK,CAAC,KAAG3B,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAChE,eAAe,CAAC,KAAG,IAAI,MAAIiE,CAAC,GAAC7L,CAAC,CAACwN,iBAAiB,CAAC,IAAE,KAAK,CAAC,KAAG3B,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACjE,eAAe,CAAC;IAACsB,cAAc,EAAClJ,CAAC,CAACkJ,cAAc,KAAG,IAAI,MAAI4C,CAAC,GAAC9L,CAAC,CAACyN,SAAS,CAAC,IAAE,KAAK,CAAC,KAAG3B,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACgB,KAAK,CAAC;IAAC1D,6BAA6B,EAACpJ,CAAC,CAACoJ,6BAA6B,KAAG,IAAI,MAAI2C,CAAC,GAAC/L,CAAC,CAAC0N,cAAc,CAAC,IAAE,KAAK,CAAC,KAAG3B,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC5E,eAAe,CAAC,KAAG,IAAI,MAAIvC,CAAC,GAAC5E,CAAC,CAAC2N,SAAS,CAAC,IAAE,KAAK,CAAC,KAAG/I,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACuC,eAAe,CAAC;IAACkC,6BAA6B,EAACrJ,CAAC,CAACqJ,6BAA6B,KAAG,IAAI,MAAI2C,CAAC,GAAChM,CAAC,CAAC0N,cAAc,CAAC,IAAE,KAAK,CAAC,KAAG1B,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACpE,eAAe,CAAC,KAAG,IAAI,MAAIqE,CAAC,GAACjM,CAAC,CAAC2N,SAAS,CAAC,IAAE,KAAK,CAAC,KAAG1B,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACrE,eAAe,CAAC;IAAC0B,qBAAqB,EAACtJ,CAAC,CAACsJ,qBAAqB,KAAG,IAAI,MAAI4C,CAAC,GAAClM,CAAC,CAAC4N,MAAM,CAAC,IAAE,KAAK,CAAC,KAAG1B,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC/E,eAAe,CAAC;IAACqC,qBAAqB,EAACxJ,CAAC,CAACwJ,qBAAqB,KAAG,IAAI,MAAI2C,CAAC,GAACnM,CAAC,CAAC4N,MAAM,CAAC,IAAE,KAAK,CAAC,KAAGzB,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACvE,eAAe,CAAC;IAAC8B,yBAAyB,EAAC1J,CAAC,CAAC0J,yBAAyB,KAAG,IAAI,MAAI0C,CAAC,GAACpM,CAAC,CAACM,UAAU,CAAC,IAAE,KAAK,CAAC,KAAG8L,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACjF,eAAe,CAAC,KAAG,IAAI,MAAIkF,CAAC,GAACrM,CAAC,CAAC6N,kBAAkB,CAAC,IAAE,KAAK,CAAC,KAAGxB,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAAClF,eAAe,CAAC;IAACyC,yBAAyB,EAAC5J,CAAC,CAAC4J,yBAAyB,KAAG,IAAI,MAAI0C,EAAE,GAACtM,CAAC,CAACM,UAAU,CAAC,IAAE,KAAK,CAAC,KAAGgM,EAAE,GAAC,KAAK,CAAC,GAACA,EAAE,CAAC1E,eAAe,CAAC,KAAG,IAAI,MAAI2E,EAAE,GAACvM,CAAC,CAAC6N,kBAAkB,CAAC,IAAE,KAAK,CAAC,KAAGtB,EAAE,GAAC,KAAK,CAAC,GAACA,EAAE,CAAC3E,eAAe,CAAC;IAACkC,cAAc,EAAC9J,CAAC,CAAC8J,cAAc,KAAG,IAAI,MAAI0C,EAAE,GAACxM,CAAC,CAAC8N,SAAS,CAAC,IAAE,KAAK,CAAC,KAAGtB,EAAE,GAAC,KAAK,CAAC,GAACA,EAAE,CAACM,KAAK;EAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;AAAC,CAAC,EAACjG,CAAC,CAAChG,SAAS,CAACW,IAAI,GAAC,YAAU;EAAC,IAAoBxB,CAAC,GAA6F,IAAI,CAAlH4H,eAAe;IAAmB1H,CAAC,GAA2E,IAAI,CAAhGiH,eAAe;IAAcvF,CAAC,GAA8D,IAAI,CAA9EtB,UAAU;IAAqBuB,CAAC,GAA0C,IAAI,CAAjEtB,iBAAiB;IAAQwB,CAAC,GAAmC,IAAI,CAA7C3B,IAAI;IAAQyC,CAAC,GAA4B,IAAI,CAAtCiE,IAAI;IAAUhE,CAAC,GAAmB,IAAI,CAA/BmE,MAAM;IAAkB1D,CAAC,GAAE,IAAI,CAAtBwD,cAAc;EAAS,IAAG/G,CAAC,KAAGE,CAAC,EAAC,MAAMqK,OAAO,CAACC,KAAK,CAAC,oEAAoE,CAAC,EAAC,IAAI3D,CAAC,CAACvF,KAAK,CAAC,yDAAyD,CAAC;EAAC,IAAI1B,CAAC,GAAC,IAAIS,CAAC,CAACuB,CAAC,EAACC,CAAC,CAAC;EAACjC,CAAC,CAACuB,OAAO,CAAC,UAASnB,CAAC,EAAC;IAACA,CAAC,GAACA,CAAC,CAAC+N,QAAQ,EAAE;IAAC,KAAI,IAAI1N,CAAC,EAACH,CAAC,GAAC,EAAE,EAAC0B,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC5B,CAAC,CAACe,MAAM,EAACa,CAAC,EAAE;MAAC,CAACvB,CAAC,GAACL,CAAC,CAACkB,UAAU,CAACU,CAAC,CAAC,KAAG,CAAC,IAAEvB,CAAC,IAAE,GAAG,GAACH,CAAC,IAAEF,CAAC,CAACgO,MAAM,CAACpM,CAAC,CAAC,GAACvB,CAAC,GAAC,IAAI,IAAEH,CAAC,IAAEwK,MAAM,CAACuD,YAAY,CAAC,GAAG,GAAC5N,CAAC,IAAE,EAAE,GAAC,EAAE,CAAC,EAACH,CAAC,IAAEwK,MAAM,CAACuD,YAAY,CAAC,GAAG,GAAC5N,CAAC,IAAE,CAAC,GAAC,EAAE,CAAC,EAACH,CAAC,IAAEwK,MAAM,CAACuD,YAAY,CAAC,GAAG,GAAC5N,CAAC,IAAE,CAAC,GAAC,EAAE,CAAC,KAAGH,CAAC,IAAEwK,MAAM,CAACuD,YAAY,CAAC,GAAG,GAAC5N,CAAC,IAAE,CAAC,GAAC,EAAE,CAAC,EAACH,CAAC,IAAEwK,MAAM,CAACuD,YAAY,CAAC,GAAG,GAAC5N,CAAC,IAAE,CAAC,GAAC,EAAE,CAAC,CAAC;IAAC;IAAA,OAAOH,CAAC;EAAA,CAAC,CAAC6B,CAAC,CAAC,CAAC,EAACnC,CAAC,CAAC4B,IAAI,EAAE,EAAC,IAAI,CAACuI,IAAI,GAACnK,CAAC,EAAC,IAAI,CAACU,UAAU,GAACV,CAAC,CAACU,UAAU,EAAC,IAAI,CAACE,OAAO,GAACZ,CAAC,CAACY,OAAO,EAAC,IAAI,CAACC,WAAW,GAACb,CAAC,CAACa,WAAW,EAAC,IAAI,CAACuG,WAAW,GAACzD,CAAC,GAAChE,IAAI,CAAC2O,IAAI,CAAC,CAACrL,CAAC,GAAC,CAAC,GAACC,CAAC,IAAElD,CAAC,CAACa,WAAW,CAAC,GAACb,CAAC,CAACa,WAAW,GAAC,CAAC,GAACqC,CAAC,GAACD,CAAC,EAAC,UAAS7C,CAAC,EAAC;IAAC,IAAgBK,CAAC,GAA+GL,CAAC,CAA7HgH,WAAW;MAAU9G,CAAC,GAAsGF,CAAC,CAA/GiH,MAAM;MAAmBrF,CAAC,GAAoF5B,CAAC,CAAtGmH,eAAe;MAAqBtF,CAAC,GAAgE7B,CAAC,CAApF2H,iBAAiB;MAAmB5F,CAAC,GAA8C/B,CAAC,CAAhE4H,eAAe;MAAqB/E,CAAC,GAA0B7C,CAAC,CAA9C4I,iBAAiB;MAAW9F,CAAC,GAAgB9C,CAAC,CAA1BQ,OAAO;MAAe+C,CAAC,GAAEvD,CAAC,CAAhBS,WAAW;MAAMb,CAAC,GAAC,CAACS,CAAC,GAAC,CAAC,GAACH,CAAC,IAAEqD,CAAC;MAACQ,CAAC,GAACnE,CAAC;MAACqE,CAAC,GAAC,CAAC;IAACpC,CAAC,GAAC,CAAC,KAAGoC,CAAC,GAACF,CAAC,GAAClC,CAAC,GAAC,CAAC,EAACkC,CAAC,IAAE,CAAC,GAACE,CAAC,CAAC;IAAC,IAAIpF,CAAC,GAACe,CAAC;MAACuE,CAAC,GAAC,CAAC;IAACtB,CAAC,GAAC,CAAC,KAAGsB,CAAC,GAACtF,CAAC,GAACgE,CAAC,GAAC,CAAC,EAAChE,CAAC,IAAE,CAAC,GAACsF,CAAC,CAAC;IAAC,KAAI,IAAIpF,CAAC,GAAC,CAAC,EAACA,CAAC,GAACwE,CAAC,EAACxE,CAAC,EAAE;MAAC,KAAI,IAAIqF,CAAC,GAAC,CAAC,EAACA,CAAC,GAACb,CAAC,EAACa,CAAC,EAAE,EAAC;QAAC,IAAI3C,CAAC,GAAC2C,CAAC,GAACxE,CAAC,GAACM,CAAC;UAACyB,CAAC,GAAC5C,CAAC,GAACa,CAAC,GAACM,CAAC;QAAC,IAAG4C,CAAC,CAAC/D,CAAC,CAAC,CAACqF,CAAC,CAAC,EAAC;UAAC,IAAIG,CAAC,GAACJ,CAAC;YAAC0C,CAAC,GAACpF,CAAC,GAAC0C,CAAC;YAACmE,CAAC,GAAC3G,CAAC,GAACwC,CAAC;YAACqE,CAAC,GAAC3J,CAAC;YAAC8J,CAAC,GAAC9J,CAAC;UAACiE,CAAC,CAAC/D,CAAC,CAAC,CAACqF,CAAC,CAAC,GAAC;YAAC1E,IAAI,EAAC,CAAC,YAAY,CAAC;YAACoN,KAAK,EAAC/K,CAAC;YAACoM,OAAO,EAAC,CAAC,CAAC;YAACC,OAAO,EAAC,CAAC,CAAC;YAACC,KAAK,EAAC5M,CAAC;YAAC6M,KAAK,EAAC3M,CAAC;YAAC4M,SAAS,EAAC3O,CAAC;YAAC4O,UAAU,EAAC5O,CAAC;YAAC8L,CAAC,EAAC7E,CAAC;YAAC2B,CAAC,EAACF,CAAC;YAAC2E,KAAK,EAACzE,CAAC;YAAC0E,MAAM,EAACvE,CAAC;YAAC8F,UAAU,EAAClK,CAAC;YAACmK,YAAY,EAACnK,CAAC;YAACoK,aAAa,EAACpK,CAAC;YAACqK,WAAW,EAACrK;UAAC,CAAC;QAAC,CAAC,MAAKA,CAAC,GAACN,CAAC,EAAC4C,CAAC,GAACpF,CAAC,GAACwC,CAAC,EAACqE,CAAC,GAAC3G,CAAC,GAACsC,CAAC,EAACuE,CAAC,GAACzE,CAAC,EAAC4E,CAAC,GAAC5E,CAAC,EAACjB,CAAC,CAAC/D,CAAC,CAAC,CAACqF,CAAC,CAAC,GAAC;UAAC1E,IAAI,EAAC,CAAC,YAAY,CAAC;UAACoN,KAAK,EAAClL,CAAC;UAACuM,OAAO,EAAC,CAAC,CAAC;UAACC,OAAO,EAAC,CAAC,CAAC;UAACC,KAAK,EAAC5M,CAAC;UAAC6M,KAAK,EAAC3M,CAAC;UAAC4M,SAAS,EAAC3O,CAAC;UAAC4O,UAAU,EAAC5O,CAAC;UAAC8L,CAAC,EAAC7E,CAAC;UAAC2B,CAAC,EAACF,CAAC;UAAC2E,KAAK,EAACzE,CAAC;UAAC0E,MAAM,EAACvE,CAAC;UAAC8F,UAAU,EAAClK,CAAC;UAACmK,YAAY,EAACnK,CAAC;UAACoK,aAAa,EAACpK,CAAC;UAACqK,WAAW,EAACrK;QAAC,CAAC;MAAC;IAAC;EAAA,CAAC,CAAC,IAAI,CAAC,EAAC,UAASvE,CAAC,EAAC;IAAC,IAAYK,CAAC,GAA8EL,CAAC,CAAxFQ,OAAO;MAAeN,CAAC,GAAgEF,CAAC,CAA9ES,WAAW;MAAgCmB,CAAC,GAAiC5B,CAAC,CAAhE8I,4BAA4B;MAAgCjH,CAAC,GAAE7B,CAAC,CAAjCgJ,4BAA4B;MAAMjH,CAAC,GAAC7B,CAAC,GAAC,CAAC;IAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC2K,OAAO,CAAE,UAAA7K,CAAC,EAAE;MAAC,IAAIE,CAAC,GAACG,CAAC,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC;QAAC6C,CAAC,GAACxC,CAAC,CAACL,CAAC,CAAC,CAAC,CAAC,GAAC+B,CAAC,CAAC,CAAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;QAAC8C,CAAC,GAACzC,CAAC,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC,GAAC+B,CAAC,CAAC;MAACe,CAAC,CAACpD,IAAI,CAAC0B,IAAI,CAAC,eAAe,CAAC,EAACyB,CAAC,CAACnD,IAAI,CAAC0B,IAAI,CAAC,eAAe,CAAC,EAAClB,CAAC,CAACR,IAAI,CAAC0B,IAAI,CAAC,eAAe,CAAC,EAAClB,CAAC,CAAC4M,KAAK,GAAC,CAAC,IAAE9M,CAAC,CAAC,CAAC,CAAC,GAAC6B,CAAC,GAACD,CAAC,EAACiB,CAAC,CAACiK,KAAK,GAAC,CAAC,IAAE9M,CAAC,CAAC,CAAC,CAAC,GAAC6B,CAAC,GAACD,CAAC,EAACkB,CAAC,CAACgK,KAAK,GAAC,CAAC,IAAE9M,CAAC,CAAC,CAAC,CAAC,GAAC6B,CAAC,GAACD,CAAC;IAAC,CAAC,CAAE;EAAC,CAAC,CAAC,IAAI,CAAC,EAAC,UAAS5B,CAAC,EAAC;IAAC,IAAYK,CAAC,GAAiCL,CAAC,CAA3CQ,OAAO;MAAeN,CAAC,GAAmBF,CAAC,CAAjCS,WAAW;MAAkBmB,CAAC,GAAE5B,CAAC,CAAnBkJ,cAAc;IAAM,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC2B,OAAO,CAAE,UAAA7K,CAAC,EAAE;MAAC,IAAI6B,CAAC,GAACxB,CAAC,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC;QAAC+B,CAAC,GAAC1B,CAAC,CAACH,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC;QAAC6C,CAAC,GAACxC,CAAC,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAACE,CAAC,GAACF,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC;MAAC6C,CAAC,CAACnD,IAAI,CAAC0B,IAAI,CAAC,WAAW,CAAC,EAACW,CAAC,CAACrC,IAAI,CAAC0B,IAAI,CAAC,WAAW,CAAC,EAACS,CAAC,CAACnC,IAAI,CAAC0B,IAAI,CAAC,WAAW,CAAC,EAACS,CAAC,CAACiL,KAAK,GAAClL,CAAC,EAACG,CAAC,CAAC+K,KAAK,GAAClL,CAAC,EAACiB,CAAC,CAACiK,KAAK,GAAClL,CAAC;IAAC,CAAC,CAAE;EAAC,CAAC,CAAC,IAAI,CAAC,EAAC,UAAS5B,CAAC,EAAC;IAAC,IAAeK,CAAC,GAA8KL,CAAC,CAA3LM,UAAU;MAAWJ,CAAC,GAAoKF,CAAC,CAA9KQ,OAAO;MAAeoB,CAAC,GAAsJ5B,CAAC,CAApKS,WAAW;MAAmBoB,CAAC,GAAoI7B,CAAC,CAAtJ4H,eAAe;MAAmB7F,CAAC,GAAkH/B,CAAC,CAApImH,eAAe;MAAiCtE,CAAC,GAAkF7C,CAAC,CAAlHqJ,6BAA6B;MAAiCvG,CAAC,GAAkD9C,CAAC,CAAlFoJ,6BAA6B;MAAyB7F,CAAC,GAA0BvD,CAAC,CAAlDwJ,qBAAqB;MAAyB5J,CAAC,GAAEI,CAAC,CAA1BsJ,qBAAqB;IAAM,IAAMvF,CAAC,GAAC,CAAC,EAAE,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC,CAAC1D,CAAC,GAAC,CAAC,CAAC;IAAC,IAAG0D,CAAC,EAAC;MAAC,IAAM/D,GAAC,GAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;QAACK,EAAC,GAAC0D,CAAC,CAAChD,MAAM;MAAC,KAAI,IAAIkD,EAAC,GAAC,CAAC,EAACA,EAAC,GAAC5D,EAAC,EAAC4D,EAAC,EAAE;QAAA,2BAASpF,EAAC;UAAY,WAAa;cAAC6M,CAAC,EAAC3H,CAAC,CAACE,EAAC,CAAC;cAACuE,CAAC,EAACzE,CAAC,CAAClF,EAAC;YAAC,CAAC;YAAtBwB,CAAC,QAAHqL,CAAC;YAAKvH,CAAC,QAAHqE,CAAC;UAAoBnI,CAAC,GAAC,CAAC,IAAE8D,CAAC,GAAC,CAAC,IAAE9D,CAAC,GAACuB,CAAC,GAAC,CAAC,GAAC,CAAC,IAAEuC,CAAC,GAAC,CAAC,IAAEA,CAAC,GAACvC,CAAC,GAAC,CAAC,GAAC,CAAC,IAAEvB,CAAC,GAAC,CAAC,IAAEL,GAAC,CAAC6K,OAAO,CAAE,UAAA7K,CAAC,EAAE;YAAC,IAAI4B,CAAC,GAAC1B,CAAC,CAACG,CAAC,GAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAACmE,CAAC,GAACnE,CAAC,CAAC,CAAC,CAAC,CAAC;YAAC4B,CAAC,CAAClC,IAAI,CAAC0B,IAAI,CAAC,gBAAgB,CAAC,EAACQ,CAAC,CAAClC,IAAI,CAACmP,QAAQ,CAAC,QAAQ,CAAC,GAAC,CAAC,IAAE7O,CAAC,CAAC,CAAC,CAAC,GAAC4B,CAAC,CAACkL,KAAK,GAACjK,CAAC,IAAEhB,CAAC,GAAC0B,CAAC,GAACV,CAAC,GAACjB,CAAC,CAACkL,KAAK,GAACjK,CAAC,IAAEhB,CAAC,IAAEiB,CAAC,IAAEf,CAAC,GAACnC,CAAC,GAACkD,CAAC,GAAClB,CAAC,CAACkL,KAAK,GAAC,CAAC,IAAE9M,CAAC,CAAC,CAAC,CAAC,GAAC6C,CAAC,GAACC,CAAC;UAAC,CAAC,CAAE;QAAC;QAA/P,KAAI,IAAIjE,EAAC,GAAC,CAAC,EAACA,EAAC,GAACwB,EAAC,EAACxB,EAAC,EAAE,EAAC;UAAA,MAAZA,EAAC;QAAsP;MAAC;IAAA;EAAC,CAAC,CAAC,IAAI,CAAC,EAAC,UAASmB,CAAC,EAAC;IAAC,IAAYK,CAAC,GAAgEL,CAAC,CAA1EQ,OAAO;MAAeN,CAAC,GAAkDF,CAAC,CAAhES,WAAW;MAAyBmB,CAAC,GAA0B5B,CAAC,CAAlDwJ,qBAAqB;MAAyB3H,CAAC,GAAE7B,CAAC,CAA1BsJ,qBAAqB;MAAMvH,CAAC,GAAC7B,CAAC,GAAC,EAAE;IAAC,KAAI,IAAIF,GAAC,GAAC,CAAC,EAACA,GAAC,GAAC+B,CAAC,EAAC/B,GAAC,EAAE,EAAC;MAAC,IAAI6C,CAAC,GAACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACL,GAAC,CAAC;QAAC8C,CAAC,GAACzC,CAAC,CAAC,CAAC,GAACL,GAAC,CAAC,CAAC,CAAC,CAAC;MAAC6C,CAAC,CAACnD,IAAI,CAAC0B,IAAI,CAAC,QAAQ,CAAC,EAAC0B,CAAC,CAACpD,IAAI,CAAC0B,IAAI,CAAC,QAAQ,CAAC,EAACyB,CAAC,CAACiK,KAAK,GAAC,CAAC,GAAC9M,GAAC,GAAC,CAAC,GAAC4B,CAAC,GAACC,CAAC,EAACiB,CAAC,CAACgK,KAAK,GAAC,CAAC,GAAC9M,GAAC,GAAC,CAAC,GAAC4B,CAAC,GAACC,CAAC;IAAC;EAAC,CAAC,CAAC,IAAI,CAAC,EAAC,UAAS7B,CAAC,EAAC;IAAC,IAAYK,CAAC,GAAiCL,CAAC,CAA3CQ,OAAO;MAAeN,CAAC,GAAmBF,CAAC,CAAjCS,WAAW;MAAkBmB,CAAC,GAAE5B,CAAC,CAAnB8J,cAAc;IAAM,IAAIjI,CAAC,GAACxB,CAAC,CAACH,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC2B,CAAC,CAACnC,IAAI,CAAC0B,IAAI,CAAC,WAAW,CAAC,EAACS,CAAC,CAACiL,KAAK,GAAClL,CAAC;EAAC,CAAC,CAAC,IAAI,CAAC,EAAC,UAAS5B,CAAC,EAAC;IAAC,IAAeK,CAAC,GAAkFL,CAAC,CAA/FM,UAAU;MAAWJ,CAAC,GAAwEF,CAAC,CAAlFQ,OAAO;MAAeoB,CAAC,GAA0D5B,CAAC,CAAxES,WAAW;MAA6BoB,CAAC,GAA8B7B,CAAC,CAA1D0J,yBAAyB;MAA6B3H,CAAC,GAAE/B,CAAC,CAA9B4J,yBAAyB;IAAM,IAAGvJ,CAAC,GAAC,CAAC,EAAC,OAAOH,CAAC;IAAC,IAAM2C,CAAC,GAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,oBAAoB,CAAC;IAAC,IAAIC,CAAC,GAACD,CAAC,CAACxC,CAAC,CAAC,GAACwC,CAAC,CAACxC,CAAC,CAAC;MAACkD,CAAC,GAAC,CAAC3B,CAAC,GAAC,EAAE,EAACA,CAAC,GAAC,EAAE,EAACA,CAAC,GAAC,CAAC,CAAC;IAAC,CAAC,CAAC,CAAC,EAAC2B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAACsH,OAAO,CAAE,UAAC7K,CAAC,EAACK,CAAC,EAAG;MAAC,IAAIuB,CAAC,GAAC1B,CAAC,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC;MAAC4B,CAAC,CAAClC,IAAI,CAAC0B,IAAI,CAAC,YAAY,CAAC,EAACQ,CAAC,CAACkL,KAAK,GAAC,GAAG,IAAEhK,CAAC,CAACzC,CAAC,CAAC,GAAC0B,CAAC,GAACF,CAAC;IAAC,CAAC,CAAE;EAAC,CAAC,CAAC,IAAI,CAAC,EAAC,IAAI,CAACuI,OAAO,GAAC,CAAC,CAAC,EAAC,IAAI,CAACJ,WAAW,GAAC,EAAE;AAAC,CAAC,EAACnD,CAAC,CAAChG,SAAS,CAACiO,cAAc,GAAC,YAAU;EAAC,IAAG,IAAI,CAAC9E,WAAW,IAAE,IAAI,CAACA,WAAW,CAACjJ,MAAM,GAAC,CAAC,EAAC,OAAO,IAAI,CAACiJ,WAAW;EAAK,IAAAhK,CAAC,GAAC,IAAI,CAACgK,WAAW,GAAC,EAAE;IAAU3J,CAAC,GAAkgB,IAAI,CAA/gBG,OAAO;IAAeN,CAAC,GAAof,IAAI,CAArgBO,WAAW;IAAemB,CAAC,GAAse,IAAI,CAAvfoF,WAAW;IAAanF,CAAC,GAA0d,IAAI,CAAzeqF,SAAS;IAAsBnF,CAAC,GAAqc,IAAI,CAA7dqF,kBAAkB;IAAoBvE,CAAC,GAAkb,IAAI,CAAxc0E,gBAAgB;IAAoBzE,CAAC,GAA+Z,IAAI,CAArb0E,gBAAgB;IAAwBjE,CAAC,GAAwY,IAAI,CAAla8D,oBAAoB;IAAyBzH,CAAC,GAAgX,IAAI,CAA3Y0H,qBAAqB;IAAwBvD,CAAC,GAAyV,IAAI,CAAnX0D,oBAAoB;IAA+BxD,CAAC,GAA2T,IAAI,CAA5VyD,2BAA2B;IAAsB7I,CAAC,GAAsS,IAAI,CAA9TgJ,kBAAkB;IAAoB1D,CAAC,GAAmR,IAAI,CAAzS6D,gBAAgB;IAAoBjJ,CAAC,GAAgQ,IAAI,CAAtRkJ,gBAAgB;IAAwB7D,CAAC,GAAyO,IAAI,CAAnQ0D,oBAAoB;IAAyBrG,CAAC,GAAiN,IAAI,CAA5OsG,qBAAqB;IAA0BpG,CAAC,GAAwL,IAAI,CAApNuG,sBAAsB;IAAkC3D,CAAC,GAAuJ,IAAI,CAA3L4D,8BAA8B;IAA+BtB,CAAC,GAAyH,IAAI,CAA1JuB,2BAA2B;IAAgCE,CAAC,GAA0F,IAAI,CAA5HD,4BAA4B;IAAgCG,CAAC,GAA2D,IAAI,CAA7FD,4BAA4B;IAA6BI,CAAC,GAA+B,IAAI,CAA9DF,yBAAyB;IAA8BI,CAAC,GAAE,IAAI,CAAlCH,0BAA0B;EAAS7G,CAAC,IAAE7B,CAAC,CAACoB,IAAI,CAAC;IAAC2N,IAAI,EAAC,MAAM;IAACrP,IAAI,EAAC,MAAM;IAACoN,KAAK,EAACjL,CAAC;IAAC6J,CAAC,EAAC,CAAC;IAAClD,CAAC,EAAC,CAAC;IAACyE,KAAK,EAACrL,CAAC;IAACsL,MAAM,EAACtL;EAAC,CAAC,CAAC,EAACG,CAAC,IAAE/B,CAAC,CAACoB,IAAI,CAAC;IAAC2N,IAAI,EAAC,iBAAiB;IAACrP,IAAI,EAAC,OAAO;IAACsP,QAAQ,EAACjN,CAAC;IAACkN,WAAW,EAAC,oBAAoB;IAACvD,CAAC,EAAC7I,CAAC;IAAC2F,CAAC,EAAC1F,CAAC;IAACmK,KAAK,EAAC1J,CAAC;IAAC2J,MAAM,EAACtN,CAAC;IAACuN,KAAK,EAACpJ,CAAC;IAACqJ,YAAY,EAACnJ;EAAC,CAAC,CAAC;EAAC,KAAI,IAAI8E,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC7I,CAAC,EAAC6I,CAAC,EAAE;IAAC,KAAI,IAAIE,CAAC,GAAC,CAAC,EAACA,CAAC,GAAC/I,CAAC,EAAC+I,CAAC,EAAE,EAAC;MAAC,IAAIE,CAAC,GAAC9I,CAAC,CAAC0I,CAAC,CAAC,CAACE,CAAC,CAAC;MAACE,CAAC,CAACiF,OAAO,KAAGjF,CAAC,CAACzJ,IAAI,CAACmP,QAAQ,CAAC,YAAY,CAAC,GAAC7O,CAAC,CAACoB,IAAI,CAAC;QAAC2N,IAAI,EAAC,YAAY;QAACrP,IAAI,EAAC,MAAM;QAACoN,KAAK,EAAC3D,CAAC,CAAC2D,KAAK;QAACuB,KAAK,EAAClF,CAAC,CAACkF,KAAK;QAACC,KAAK,EAACnF,CAAC,CAACmF,KAAK;QAACC,SAAS,EAACpF,CAAC,CAACoF,SAAS;QAACC,UAAU,EAACrF,CAAC,CAACqF,UAAU;QAAC9C,CAAC,EAACvC,CAAC,CAACuC,CAAC;QAAClD,CAAC,EAACW,CAAC,CAACX,CAAC;QAACyE,KAAK,EAAC9D,CAAC,CAAC8D,KAAK;QAACC,MAAM,EAAC/D,CAAC,CAAC+D,MAAM;QAACuB,UAAU,EAACtF,CAAC,CAACsF,UAAU;QAACC,YAAY,EAACvF,CAAC,CAACuF,YAAY;QAACC,aAAa,EAACxF,CAAC,CAACwF,aAAa;QAACC,WAAW,EAACzF,CAAC,CAACyF,WAAW;QAACM,QAAQ,EAACnG,CAAC;QAACoG,QAAQ,EAAClG;MAAC,CAAC,CAAC,GAACjJ,CAAC,CAACoB,IAAI,CAAC;QAAC2N,IAAI,EAAC,YAAY;QAACrP,IAAI,EAAC,MAAM;QAACoN,KAAK,EAAC3D,CAAC,CAAC2D,KAAK;QAACuB,KAAK,EAAClF,CAAC,CAACkF,KAAK;QAACC,KAAK,EAACnF,CAAC,CAACmF,KAAK;QAACC,SAAS,EAACpF,CAAC,CAACoF,SAAS;QAACC,UAAU,EAACrF,CAAC,CAACqF,UAAU;QAAC9C,CAAC,EAACvC,CAAC,CAACuC,CAAC;QAAClD,CAAC,EAACW,CAAC,CAACX,CAAC;QAACyE,KAAK,EAAC9D,CAAC,CAAC8D,KAAK;QAACC,MAAM,EAAC/D,CAAC,CAAC+D,MAAM;QAACuB,UAAU,EAACtF,CAAC,CAACsF,UAAU;QAACC,YAAY,EAACvF,CAAC,CAACuF,YAAY;QAACC,aAAa,EAACxF,CAAC,CAACwF,aAAa;QAACC,WAAW,EAACzF,CAAC,CAACyF,WAAW;QAACM,QAAQ,EAACnG,CAAC;QAACoG,QAAQ,EAAClG;MAAC,CAAC,CAAC,EAACE,CAAC,CAACiF,OAAO,GAAC,CAAC,CAAC,CAAC;IAAC;EAAC;EAAA,OAAOvP,CAAC,IAAEmB,CAAC,CAACoB,IAAI,CAAC;IAAC2N,IAAI,EAAC,iBAAiB;IAACrP,IAAI,EAAC,OAAO;IAACsP,QAAQ,EAACnQ,CAAC;IAACoQ,WAAW,EAAC,oBAAoB;IAACvD,CAAC,EAACvH,CAAC;IAACqE,CAAC,EAACzJ,CAAC;IAACkO,KAAK,EAAC7I,CAAC;IAAC8I,MAAM,EAACzL,CAAC;IAAC6L,OAAO,EAAC3L,CAAC;IAACwF,eAAe,EAAC5C,CAAC;IAAC6I,YAAY,EAACvG,CAAC;IAACN,aAAa,EAAC+B,CAAC;IAAC9B,aAAa,EAACgC,CAAC;IAAC/B,UAAU,EAACkC,CAAC;IAACjC,WAAW,EAACmC;EAAC,CAAC,CAAC,EAAC7I,CAAC;AAAA,CAAC,EAAC6G,CAAC,CAAChG,SAAS,CAACsN,OAAO,GAAC,UAASnO,CAAC,EAACK,CAAC,EAAC;EAAC,IAAIH,CAAC,GAAC,IAAI,CAACO,WAAW;EAAC,OAAO,EAAE,CAAC,GAACT,CAAC,IAAE,CAAC,GAACK,CAAC,IAAEL,CAAC,IAAEE,CAAC,IAAEG,CAAC,IAAEH,CAAC,CAAC,IAAE,IAAI,CAACM,OAAO,CAACR,CAAC,CAAC,CAACK,CAAC,CAAC,CAAC8N,OAAO;AAAA,CAAC,EAACtH,CAAC,CAAChG,SAAS,CAACuO,UAAU,GAAC,YAAU;EAAA;EAAC,IAAYpP,CAAC,GAAoJ,IAAI,CAAjKoK,OAAO;IAAiB/J,CAAC,GAAoI,IAAI,CAAvJ4J,aAAa;IAAkB/J,CAAC,GAAmH,IAAI,CAAvI6G,cAAc;IAAenF,CAAC,GAAqG,IAAI,CAAtHoF,WAAW;IAAmBnF,CAAC,GAAmF,IAAI,CAAxG+F,eAAe;IAAqB7F,CAAC,GAA+D,IAAI,CAAtF6G,iBAAiB;IAAmB/F,CAAC,GAA6C,IAAI,CAAlEsE,eAAe;IAAqBrE,CAAC,GAAyB,IAAI,CAAhD6E,iBAAiB;IAAepE,CAAC,GAAW,IAAI,CAA5B4G,WAAW;IAAUvK,CAAC,GAAE,IAAI,CAAdqH,MAAM;EAAS,IAAG,CAACjH,CAAC,EAAC,OAAOuK,OAAO,CAACC,KAAK,CAAC,kDAAkD,CAAC,EAACW,OAAO,CAACkE,MAAM,CAAC,IAAIxI,CAAC,CAACvF,KAAK,CAAC,uCAAuC,CAAC,CAAC;EAAC,IAAIyC,CAAC,GAAC,IAAI,CAAC+K,cAAc,EAAE;IAAC7K,CAAC;MAAA,mFAAC,iBAAMjE,CAAC,EAACE,CAAC;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAQG,CAAC,CAACuG,SAAS,CAAC,CAAC,EAAC,CAAC,EAAChF,CAAC,EAACA,CAAC,CAAC,EAACvB,CAAC,CAACsG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAAS9E,CAAC,GAAC,CAAC;cAAA;gBAAA,MAACA,CAAC,GAACkC,CAAC,CAAChD,MAAM;kBAAA;kBAAA;gBAAA;gBAAUgB,CAAC,GAACgC,CAAC,CAAClC,CAAC,CAAC;gBAAA,eAAQxB,CAAC,CAACiP,IAAI,EAAE,EAACvN,CAAC,CAACrC,IAAI;gBAAA,gCAAM,MAAM,uBAAwE,MAAM,wBAA4F,OAAO;gBAAA;cAAA;gBAAhLW,CAAC,CAACqF,YAAY,CAAC3D,CAAC,CAAC+K,KAAK,CAAC,EAACzM,CAAC,CAACkP,QAAQ,CAACxN,CAAC,CAAC2J,CAAC,EAAC3J,CAAC,CAACyG,CAAC,EAACzG,CAAC,CAACkL,KAAK,EAAClL,CAAC,CAACmL,MAAM,CAAC;gBAAC;cAAA;gBAAqBrK,CAAC,GAACd,CAAC,CAAC2J,CAAC,EAAC5I,CAAC,GAACf,CAAC,CAACyG,CAAC,EAAC5I,CAAC,GAACmC,CAAC,CAACkL,KAAK,EAAChJ,CAAC,GAAClC,CAAC,CAACmL,MAAM;gBAAC7M,CAAC,CAACqF,YAAY,CAAC3D,CAAC,CAAC+K,KAAK,CAAC,EAACzM,CAAC,CAACkP,QAAQ,CAAC1M,CAAC,EAACC,CAAC,EAAClD,CAAC,EAACqE,CAAC,CAAC;gBAAC;cAAA;gBAAA,MAAqB,iBAAiB,KAAGlC,CAAC,CAACgN,IAAI;kBAAA;kBAAA;gBAAA;gBAAElM,CAAC,GAACtD,IAAI,CAACC,KAAK,CAACuC,CAAC,CAAC2J,CAAC,CAAC,EAAC5I,CAAC,GAACvD,IAAI,CAACC,KAAK,CAACuC,CAAC,CAACyG,CAAC,CAAC,EAAC5I,CAAC,GAACL,IAAI,CAACC,KAAK,CAACuC,CAAC,CAACkL,KAAK,CAAC,EAAChJ,CAAC,GAAC1E,IAAI,CAACC,KAAK,CAACuC,CAAC,CAACmL,MAAM,CAAC;gBAACtN,CAAC,GAAC,CAAC,IAAEuE,CAAC,GAAC5E,IAAI,CAACC,KAAK,CAACuC,CAAC,CAACqL,YAAY,CAAC,CAAC,KAAGjJ,CAAC,GAACvE,CAAC,GAAC,CAAC,CAAC,EAACqE,CAAC,GAAC,CAAC,GAACE,CAAC,KAAGA,CAAC,GAACF,CAAC,GAAC,CAAC,CAAC,EAAC5D,CAAC,CAAC6F,cAAc,CAACnE,CAAC,CAACoL,KAAK,CAAC,EAAChJ,CAAC,GAAC,CAAC,KAAG9D,CAAC,CAACmP,SAAS,EAAE,EAACnP,CAAC,CAAC2C,MAAM,CAACH,CAAC,GAACsB,CAAC,EAACrB,CAAC,CAAC,EAACzC,CAAC,CAACoP,KAAK,CAAC5M,CAAC,GAACjD,CAAC,EAACkD,CAAC,EAACD,CAAC,GAACjD,CAAC,EAACkD,CAAC,GAACmB,CAAC,EAACE,CAAC,CAAC,EAAC9D,CAAC,CAACoP,KAAK,CAAC5M,CAAC,GAACjD,CAAC,EAACkD,CAAC,GAACmB,CAAC,EAACpB,CAAC,EAACC,CAAC,GAACmB,CAAC,EAACE,CAAC,CAAC,EAAC9D,CAAC,CAACoP,KAAK,CAAC5M,CAAC,EAACC,CAAC,GAACmB,CAAC,EAACpB,CAAC,EAACC,CAAC,EAACqB,CAAC,CAAC,EAAC9D,CAAC,CAACoP,KAAK,CAAC5M,CAAC,EAACC,CAAC,EAACD,CAAC,GAACjD,CAAC,EAACkD,CAAC,EAACqB,CAAC,CAAC,EAAC9D,CAAC,CAACqP,SAAS,EAAE,EAACrP,CAAC,CAAC+F,cAAc,CAAC,eAAe,CAAC,EAAC/F,CAAC,CAACsP,MAAM,EAAE,EAACtP,CAAC,CAACuP,IAAI,EAAE,CAAC;gBAAC;gBAAA;gBAAA,OAAgB,MAAI,CAAC1F,SAAS,CAACnI,CAAC,CAACiN,QAAQ,CAAC;cAAA;gBAAlCnQ,CAAC;gBAAkCwB,CAAC,CAACwP,SAAS,CAAChR,CAAC,EAACgE,CAAC,EAACC,CAAC,EAAClD,CAAC,EAACqE,CAAC,CAAC;gBAAC;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,MAAgBsG,OAAO,CAACC,KAAK,sBAAezI,CAAC,CAACkN,WAAW,eAAY,EAAC,IAAIpI,CAAC,CAACvF,KAAK,WAAIS,CAAC,CAACkN,WAAW,eAAY;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAAU,iBAAiB,KAAGlN,CAAC,CAACgN,IAAI;kBAAA;kBAAA;gBAAA;gBAAElM,CAAC,GAACtD,IAAI,CAACC,KAAK,CAACuC,CAAC,CAAC2J,CAAC,CAAC,EAAC5I,CAAC,GAACvD,IAAI,CAACC,KAAK,CAACuC,CAAC,CAACyG,CAAC,CAAC,EAAC5I,CAAC,GAACL,IAAI,CAACC,KAAK,CAACuC,CAAC,CAACkL,KAAK,CAAC,EAAChJ,CAAC,GAAC1E,IAAI,CAACC,KAAK,CAACuC,CAAC,CAACmL,MAAM,CAAC;gBAAOnO,CAAC,GAACQ,IAAI,CAACC,KAAK,CAACuC,CAAC,CAACuL,OAAO,CAAC;gBAAC1N,CAAC,GAAC,CAAC,IAAEuE,CAAC,GAAC5E,IAAI,CAACC,KAAK,CAACuC,CAAC,CAACqL,YAAY,CAAC,CAAC,KAAGjJ,CAAC,GAACvE,CAAC,GAAC,CAAC,CAAC,EAACqE,CAAC,GAAC,CAAC,GAACE,CAAC,KAAGA,CAAC,GAACF,CAAC,GAAC,CAAC,CAAC;gBAAKG,CAAC,GAACvB,CAAC,GAAC9D,CAAC,EAAC0C,CAAC,GAACqB,CAAC,GAAC/D,CAAC,EAAC4C,CAAC,GAAC/B,CAAC,GAAC,CAAC,GAACb,CAAC,EAACwF,CAAC,GAACN,CAAC,GAAC,CAAC,GAAClF,CAAC,EAACuJ,CAAC,GAAC/I,IAAI,CAACC,KAAK,CAACmC,CAAC,GAAC/B,CAAC,GAACuE,CAAC,CAAC;gBAACxC,CAAC,GAAC,CAAC,GAAC2G,CAAC,KAAGA,CAAC,GAAC3G,CAAC,GAAC,CAAC,CAAC,EAAC4C,CAAC,GAAC,CAAC,GAAC+D,CAAC,KAAGA,CAAC,GAAC/D,CAAC,GAAC,CAAC,CAAC,EAAClE,CAAC,CAACiP,IAAI,EAAE,EAACjP,CAAC,CAACiG,SAAS,CAACvE,CAAC,CAACwE,aAAa,EAACxE,CAAC,CAACyE,aAAa,EAACzE,CAAC,CAAC0E,UAAU,EAAC1E,CAAC,CAAC2E,WAAW,CAAC,EAAC4B,CAAC,GAAC,CAAC,IAAEjI,CAAC,CAACmP,SAAS,EAAE,EAACnP,CAAC,CAAC2C,MAAM,CAACoB,CAAC,GAACkE,CAAC,EAAC7G,CAAC,CAAC,EAACpB,CAAC,CAACoP,KAAK,CAACrL,CAAC,GAACzC,CAAC,EAACF,CAAC,EAAC2C,CAAC,GAACzC,CAAC,EAACF,CAAC,GAAC8C,CAAC,EAAC+D,CAAC,CAAC,EAACjI,CAAC,CAACoP,KAAK,CAACrL,CAAC,GAACzC,CAAC,EAACF,CAAC,GAAC8C,CAAC,EAACH,CAAC,EAAC3C,CAAC,GAAC8C,CAAC,EAAC+D,CAAC,CAAC,EAACjI,CAAC,CAACoP,KAAK,CAACrL,CAAC,EAAC3C,CAAC,GAAC8C,CAAC,EAACH,CAAC,EAAC3C,CAAC,EAAC6G,CAAC,CAAC,EAACjI,CAAC,CAACoP,KAAK,CAACrL,CAAC,EAAC3C,CAAC,EAAC2C,CAAC,GAACzC,CAAC,EAACF,CAAC,EAAC6G,CAAC,CAAC,EAACjI,CAAC,CAACqP,SAAS,EAAE,EAACrP,CAAC,CAACqF,YAAY,CAAC3D,CAAC,CAACoF,eAAe,CAAC,EAAC9G,CAAC,CAACyP,IAAI,EAAE,KAAGzP,CAAC,CAACqF,YAAY,CAAC3D,CAAC,CAACoF,eAAe,CAAC,EAAC9G,CAAC,CAACkP,QAAQ,CAACnL,CAAC,EAAC3C,CAAC,EAACE,CAAC,EAAC4C,CAAC,CAAC,CAAC,EAAClE,CAAC,CAAC0P,OAAO,EAAE,EAAC1P,CAAC,CAACiP,IAAI,EAAE,EAAChH,CAAC,GAAC,CAAC,IAAEjI,CAAC,CAACmP,SAAS,EAAE,EAACnP,CAAC,CAAC2C,MAAM,CAACoB,CAAC,GAACkE,CAAC,EAAC7G,CAAC,CAAC,EAACpB,CAAC,CAACoP,KAAK,CAACrL,CAAC,GAACzC,CAAC,EAACF,CAAC,EAAC2C,CAAC,GAACzC,CAAC,EAACF,CAAC,GAAC8C,CAAC,EAAC+D,CAAC,CAAC,EAACjI,CAAC,CAACoP,KAAK,CAACrL,CAAC,GAACzC,CAAC,EAACF,CAAC,GAAC8C,CAAC,EAACH,CAAC,EAAC3C,CAAC,GAAC8C,CAAC,EAAC+D,CAAC,CAAC,EAACjI,CAAC,CAACoP,KAAK,CAACrL,CAAC,EAAC3C,CAAC,GAAC8C,CAAC,EAACH,CAAC,EAAC3C,CAAC,EAAC6G,CAAC,CAAC,EAACjI,CAAC,CAACoP,KAAK,CAACrL,CAAC,EAAC3C,CAAC,EAAC2C,CAAC,GAACzC,CAAC,EAACF,CAAC,EAAC6G,CAAC,CAAC,EAACjI,CAAC,CAACqP,SAAS,EAAE,EAACrP,CAAC,CAACqF,YAAY,CAAC3G,CAAC,GAAC,CAAC,GAACgD,CAAC,CAACoF,eAAe,GAAC,eAAe,CAAC,EAAC9G,CAAC,CAACyP,IAAI,EAAE,KAAGzP,CAAC,CAACqF,YAAY,CAAC3G,CAAC,GAAC,CAAC,GAACgD,CAAC,CAACoF,eAAe,GAAC,eAAe,CAAC,EAAC9G,CAAC,CAACkP,QAAQ,CAACnL,CAAC,EAAC3C,CAAC,EAACE,CAAC,EAAC4C,CAAC,CAAC,CAAC,EAAClE,CAAC,CAAC0P,OAAO,EAAE,EAAC5L,CAAC,GAAC,CAAC,KAAG9D,CAAC,CAACmP,SAAS,EAAE,EAACnP,CAAC,CAAC2C,MAAM,CAACH,CAAC,GAACsB,CAAC,EAACrB,CAAC,CAAC,EAACzC,CAAC,CAACoP,KAAK,CAAC5M,CAAC,GAACjD,CAAC,EAACkD,CAAC,EAACD,CAAC,GAACjD,CAAC,EAACkD,CAAC,GAACmB,CAAC,EAACE,CAAC,CAAC,EAAC9D,CAAC,CAACoP,KAAK,CAAC5M,CAAC,GAACjD,CAAC,EAACkD,CAAC,GAACmB,CAAC,EAACpB,CAAC,EAACC,CAAC,GAACmB,CAAC,EAACE,CAAC,CAAC,EAAC9D,CAAC,CAACoP,KAAK,CAAC5M,CAAC,EAACC,CAAC,GAACmB,CAAC,EAACpB,CAAC,EAACC,CAAC,EAACqB,CAAC,CAAC,EAAC9D,CAAC,CAACoP,KAAK,CAAC5M,CAAC,EAACC,CAAC,EAACD,CAAC,GAACjD,CAAC,EAACkD,CAAC,EAACqB,CAAC,CAAC,EAAC9D,CAAC,CAACqP,SAAS,EAAE,EAACrP,CAAC,CAAC+F,cAAc,CAAC,eAAe,CAAC,EAAC/F,CAAC,CAACsP,MAAM,EAAE,EAACtP,CAAC,CAACuP,IAAI,EAAE,CAAC;gBAAC;gBAAA;gBAAA,OAAY,MAAI,CAAC1F,SAAS,CAACnI,CAAC,CAACiN,QAAQ,CAAC;cAAA;gBAAlCnQ,CAAC;gBAAkCwB,CAAC,CAACwP,SAAS,CAAChR,CAAC,EAACgE,CAAC,EAACC,CAAC,EAAClD,CAAC,EAACqE,CAAC,CAAC;gBAAC;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,MAAgBsG,OAAO,CAACC,KAAK,sBAAezI,CAAC,CAACkN,WAAW,eAAY,EAAC,IAAIpI,CAAC,CAACvF,KAAK,WAAIS,CAAC,CAACkN,WAAW,eAAY;cAAA;gBAAG1L,CAAC,IAAElD,CAAC,CAACsG,IAAI,CAAC,CAAC,CAAC,CAAC,EAACtG,CAAC,CAAC0P,OAAO,EAAE;cAAC;gBAA1kElO,CAAC,EAAE;gBAAA;gBAAA;cAAA;gBAAwkExB,CAAC,CAACsG,IAAI,CAAC,CAAC,CAAC,CAAC,EAACqJ,UAAU,CAAChQ,CAAC,EAAC,GAAG,CAAC;gBAAC;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,IAAe,uBAAa6G,CAAC,CAACvF,KAAK;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAUpB,CAAC,aAAG;cAAC;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CAAE;MAAA;QAAA;MAAA;IAAA;EAAC,OAAO,IAAIiL,OAAO,CAAE,UAACnL,CAAC,EAACK,CAAC,EAAG;IAAC4D,CAAC,CAACjE,CAAC,EAACK,CAAC,CAAC;EAAC,CAAC,CAAE;AAAA,CAAC,EAACwG,CAAC,CAAChG,SAAS,CAAC8F,IAAI,GAAC,YAAU;EAAC,OAAO,IAAI,CAACyI,UAAU,EAAE;AAAA,CAAC,EAACvI,CAAC,CAAChG,SAAS,CAACoP,QAAQ,GAAC,UAASjQ,CAAC,EAAC;EAACA,CAAC,IAAEA,CAAC,CAAC6G,CAAC,EAAC,IAAI,EAAC,CAAC,CAAC,CAAC;AAAC,CAAC,C", "file": "pages/me_all/common/vendor.js", "sourcesContent": ["/**\r\n * function: 60秒内（刚刚），60秒至60分钟（**分钟前），1小时至24小时（**小时前），1天至15天（**天前），其他为正常日期显示\r\n * @number   時間戳\r\n */\r\nfunction formatMsgTime(number) {\r\n  var dateTime = new Date(number); // 将传进来的字符串或者毫秒转为标准时间\r\n  var Y = dateTime.getFullYear(); // 年\r\n  var M = dateTime.getMonth() + 1; // 月\r\n  var D = dateTime.getDate(); // 日\r\n  var h = dateTime.getHours(); // 时\r\n  var m = dateTime.getMinutes(); // 分\r\n  var millisecond = dateTime.getTime(); // 将当前编辑的时间转换为毫秒\r\n  var now = new Date(); // 获取本机当前的时间\r\n  var nowNew = now.getTime(); // 将本机的时间转换为毫秒\r\n  var milliseconds = 0;\r\n  var numberStr;\r\n  milliseconds = nowNew - millisecond;\r\n  if (milliseconds <= 1000 * 60 * 1) { // 小于一分钟展示为刚刚\r\n    numberStr = '刚刚'\r\n  } else if (1000 * 60 * 1 < milliseconds && milliseconds <= 1000 * 60 * 60) { // 大于一分钟小于一小时展示为分钟\r\n    numberStr = Math.round((milliseconds / (1000 * 60))) + '分钟前'\r\n  } else if (1000 * 60 * 60 * 1 < milliseconds && milliseconds <= 1000 * 60 * 60 * 24) { // 大于一小时小于一天展示为小时\r\n    numberStr = Math.round(milliseconds / (1000 * 60 * 60)) + '小时前'\r\n  } else if (1000 * 60 * 60 * 24 < milliseconds && milliseconds <= 1000 * 60 * 60 * 24 * 15) { // 大于一天小于十五天展示位天\r\n    numberStr = Math.round(milliseconds / (1000 * 60 * 60 * 24)) + '天前'\r\n  } else if (milliseconds > 1000 * 60 * 60 * 24 * 15 && Y === now.getFullYear()) {\r\n    numberStr = M + '-' + D + ' ' + h + ':' + m\r\n  } else {\r\n    numberStr = Y + '-' + M + '-' + D + ' ' + h + ':' + m\r\n  }\r\n  return numberStr\r\n}\r\n \r\n/**\r\n * function: 時間戳轉日期\r\n * @number   時間戳\r\n * @type     格式（1為年-月-日 時-分-秒，2為年-月-日）\r\n */\r\nfunction toDate(number, type) {\r\n  var date = new Date(number);\r\n  var Y = date.getFullYear();\r\n  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);\r\n  var D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();\r\n  var h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();\r\n  var m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();\r\n  var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();\r\n  if (type == '1') {\r\n    return Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s;\r\n  } else if (type == '2') {\r\n    return Y + '-' + M + '-' + D;\r\n  }\r\n}\r\n \r\nmodule.exports = {\r\n  toDate: toDate,\r\n  formatMsgTime: formatMsgTime\r\n}\r\n", "//---------------------------------------------------------------------\r\n// uQRCode二维码生成插件 v4.0.6\r\n// \r\n// uQRCode是一款基于Javascript环境开发的二维码生成插件，适用所有Javascript运行环境的前端应用和Node.js。\r\n// \r\n// Copyright (c) Sansnn uQRCode All rights reserved.\r\n// \r\n// Licensed under the Apache License, Version 2.0.\r\n//   http://www.apache.org/licenses/LICENSE-2.0\r\n// \r\n// github地址：\r\n//   https://github.com/Sansnn/uQRCode\r\n// \r\n// npm地址：\r\n//   https://www.npmjs.com/package/uqrcodejs\r\n// \r\n// uni-app插件市场地址：\r\n//   https://ext.dcloud.net.cn/plugin?id=1287\r\n// \r\n// 复制使用请保留本段注释，感谢支持开源！\r\n// \r\n//---------------------------------------------------------------------\r\n\r\n//---------------------------------------------------------------------\r\n// 当前文件格式为 es，将 bundle 保留为 ES 模块文件，适用于其他打包工具以及支持 <script type=module> 标签的浏览器（别名: esm，module）\r\n// 如需在其他环境使用，请获取环境对应的格式文件\r\n// 格式说明：\r\n// amd - 异步模块定义，适用于 RequireJS 等模块加载器\r\n// cjs - CommonJS，适用于 Node 环境和其他打包工具（别名：commonjs）\r\n// es - 将 bundle 保留为 ES 模块文件，适用于其他打包工具以及支持 <script type=module> 标签的浏览器（别名: esm，module）\r\n// umd - 通用模块定义，生成的包同时支持 amd、cjs 和 iife 三种格式\r\n//---------------------------------------------------------------------\r\n\r\nfunction o(o){this.mode=r.MODE_8BIT_BYTE,this.data=o;}function e(o,e){this.typeNumber=o,this.errorCorrectLevel=e,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=new Array;}o.prototype={getLength:function(o){return this.data.length},write:function(o){for(var e=0;e<this.data.length;e++)o.put(this.data.charCodeAt(e),8);}},e.prototype={addData:function(e){var r=new o(e);this.dataList.push(r),this.dataCache=null;},isDark:function(o,e){if(o<0||this.moduleCount<=o||e<0||this.moduleCount<=e)throw new Error(o+\",\"+e);return this.modules[o][e]},getModuleCount:function(){return this.moduleCount},make:function(){if(this.typeNumber<1){var o=1;for(o=1;o<40;o++){for(var e=v.getRSBlocks(o,this.errorCorrectLevel),r=new p,t=0,i=0;i<e.length;i++)t+=e[i].dataCount;for(i=0;i<this.dataList.length;i++){var n=this.dataList[i];r.put(n.mode,4),r.put(n.getLength(),h.getLengthInBits(n.mode,o)),n.write(r);}if(r.getLengthInBits()<=8*t)break}this.typeNumber=o;}this.makeImpl(!1,this.getBestMaskPattern());},makeImpl:function(o,r){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var t=0;t<this.moduleCount;t++){this.modules[t]=new Array(this.moduleCount);for(var i=0;i<this.moduleCount;i++)this.modules[t][i]=null;}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(o,r),this.typeNumber>=7&&this.setupTypeNumber(o),null==this.dataCache&&(this.dataCache=e.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,r);},setupPositionProbePattern:function(o,e){for(var r=-1;r<=7;r++)if(!(o+r<=-1||this.moduleCount<=o+r))for(var t=-1;t<=7;t++)e+t<=-1||this.moduleCount<=e+t||(this.modules[o+r][e+t]=0<=r&&r<=6&&(0==t||6==t)||0<=t&&t<=6&&(0==r||6==r)||2<=r&&r<=4&&2<=t&&t<=4);},getBestMaskPattern:function(){for(var o=0,e=0,r=0;r<8;r++){this.makeImpl(!0,r);var t=h.getLostPoint(this);(0==r||o>t)&&(o=t,e=r);}return e},createMovieClip:function(o,e,r){var t=o.createEmptyMovieClip(e,r);this.make();for(var i=0;i<this.modules.length;i++)for(var n=1*i,a=0;a<this.modules[i].length;a++){var d=1*a;this.modules[i][a]&&(t.beginFill(0,100),t.moveTo(d,n),t.lineTo(d+1,n),t.lineTo(d+1,n+1),t.lineTo(d,n+1),t.endFill());}return t},setupTimingPattern:function(){for(var o=8;o<this.moduleCount-8;o++)null==this.modules[o][6]&&(this.modules[o][6]=o%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0);},setupPositionAdjustPattern:function(){for(var o=h.getPatternPosition(this.typeNumber),e=0;e<o.length;e++)for(var r=0;r<o.length;r++){var t=o[e],i=o[r];if(null==this.modules[t][i])for(var n=-2;n<=2;n++)for(var a=-2;a<=2;a++)this.modules[t+n][i+a]=-2==n||2==n||-2==a||2==a||0==n&&0==a;}},setupTypeNumber:function(o){for(var e=h.getBCHTypeNumber(this.typeNumber),r=0;r<18;r++){var t=!o&&1==(e>>r&1);this.modules[Math.floor(r/3)][r%3+this.moduleCount-8-3]=t;}for(r=0;r<18;r++){t=!o&&1==(e>>r&1);this.modules[r%3+this.moduleCount-8-3][Math.floor(r/3)]=t;}},setupTypeInfo:function(o,e){for(var r=this.errorCorrectLevel<<3|e,t=h.getBCHTypeInfo(r),i=0;i<15;i++){var n=!o&&1==(t>>i&1);i<6?this.modules[i][8]=n:i<8?this.modules[i+1][8]=n:this.modules[this.moduleCount-15+i][8]=n;}for(i=0;i<15;i++){n=!o&&1==(t>>i&1);i<8?this.modules[8][this.moduleCount-i-1]=n:i<9?this.modules[8][15-i-1+1]=n:this.modules[8][15-i-1]=n;}this.modules[this.moduleCount-8][8]=!o;},mapData:function(o,e){for(var r=-1,t=this.moduleCount-1,i=7,n=0,a=this.moduleCount-1;a>0;a-=2)for(6==a&&a--;;){for(var d=0;d<2;d++)if(null==this.modules[t][a-d]){var u=!1;n<o.length&&(u=1==(o[n]>>>i&1)),h.getMask(e,t,a-d)&&(u=!u),this.modules[t][a-d]=u,-1==--i&&(n++,i=7);}if((t+=r)<0||this.moduleCount<=t){t-=r,r=-r;break}}}},e.PAD0=236,e.PAD1=17,e.createData=function(o,r,t){for(var i=v.getRSBlocks(o,r),n=new p,a=0;a<t.length;a++){var d=t[a];n.put(d.mode,4),n.put(d.getLength(),h.getLengthInBits(d.mode,o)),d.write(n);}var u=0;for(a=0;a<i.length;a++)u+=i[a].dataCount;if(n.getLengthInBits()>8*u)throw new Error(\"code length overflow. (\"+n.getLengthInBits()+\">\"+8*u+\")\");for(n.getLengthInBits()+4<=8*u&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(!1);for(;!(n.getLengthInBits()>=8*u||(n.put(e.PAD0,8),n.getLengthInBits()>=8*u));)n.put(e.PAD1,8);return e.createBytes(n,i)},e.createBytes=function(o,e){for(var r=0,t=0,i=0,n=new Array(e.length),a=new Array(e.length),d=0;d<e.length;d++){var u=e[d].dataCount,s=e[d].totalCount-u;t=Math.max(t,u),i=Math.max(i,s),n[d]=new Array(u);for(var g=0;g<n[d].length;g++)n[d][g]=255&o.buffer[g+r];r+=u;var l=h.getErrorCorrectPolynomial(s),c=new f(n[d],l.getLength()-1).mod(l);a[d]=new Array(l.getLength()-1);for(g=0;g<a[d].length;g++){var m=g+c.getLength()-a[d].length;a[d][g]=m>=0?c.get(m):0;}}var v=0;for(g=0;g<e.length;g++)v+=e[g].totalCount;var p=new Array(v),C=0;for(g=0;g<t;g++)for(d=0;d<e.length;d++)g<n[d].length&&(p[C++]=n[d][g]);for(g=0;g<i;g++)for(d=0;d<e.length;d++)g<a[d].length&&(p[C++]=a[d][g]);return p};for(var r={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},t={L:1,M:0,Q:3,H:2},i=0,n=1,a=2,d=3,u=4,s=5,g=6,l=7,h={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(o){for(var e=o<<10;h.getBCHDigit(e)-h.getBCHDigit(h.G15)>=0;)e^=h.G15<<h.getBCHDigit(e)-h.getBCHDigit(h.G15);return (o<<10|e)^h.G15_MASK},getBCHTypeNumber:function(o){for(var e=o<<12;h.getBCHDigit(e)-h.getBCHDigit(h.G18)>=0;)e^=h.G18<<h.getBCHDigit(e)-h.getBCHDigit(h.G18);return o<<12|e},getBCHDigit:function(o){for(var e=0;0!=o;)e++,o>>>=1;return e},getPatternPosition:function(o){return h.PATTERN_POSITION_TABLE[o-1]},getMask:function(o,e,r){switch(o){case i:return (e+r)%2==0;case n:return e%2==0;case a:return r%3==0;case d:return (e+r)%3==0;case u:return (Math.floor(e/2)+Math.floor(r/3))%2==0;case s:return e*r%2+e*r%3==0;case g:return (e*r%2+e*r%3)%2==0;case l:return (e*r%3+(e+r)%2)%2==0;default:throw new Error(\"bad maskPattern:\"+o)}},getErrorCorrectPolynomial:function(o){for(var e=new f([1],0),r=0;r<o;r++)e=e.multiply(new f([1,c.gexp(r)],0));return e},getLengthInBits:function(o,e){if(1<=e&&e<10)switch(o){case r.MODE_NUMBER:return 10;case r.MODE_ALPHA_NUM:return 9;case r.MODE_8BIT_BYTE:case r.MODE_KANJI:return 8;default:throw new Error(\"mode:\"+o)}else if(e<27)switch(o){case r.MODE_NUMBER:return 12;case r.MODE_ALPHA_NUM:return 11;case r.MODE_8BIT_BYTE:return 16;case r.MODE_KANJI:return 10;default:throw new Error(\"mode:\"+o)}else {if(!(e<41))throw new Error(\"type:\"+e);switch(o){case r.MODE_NUMBER:return 14;case r.MODE_ALPHA_NUM:return 13;case r.MODE_8BIT_BYTE:return 16;case r.MODE_KANJI:return 12;default:throw new Error(\"mode:\"+o)}}},getLostPoint:function(o){for(var e=o.getModuleCount(),r=0,t=0;t<e;t++)for(var i=0;i<e;i++){for(var n=0,a=o.isDark(t,i),d=-1;d<=1;d++)if(!(t+d<0||e<=t+d))for(var u=-1;u<=1;u++)i+u<0||e<=i+u||0==d&&0==u||a==o.isDark(t+d,i+u)&&n++;n>5&&(r+=3+n-5);}for(t=0;t<e-1;t++)for(i=0;i<e-1;i++){var s=0;o.isDark(t,i)&&s++,o.isDark(t+1,i)&&s++,o.isDark(t,i+1)&&s++,o.isDark(t+1,i+1)&&s++,0!=s&&4!=s||(r+=3);}for(t=0;t<e;t++)for(i=0;i<e-6;i++)o.isDark(t,i)&&!o.isDark(t,i+1)&&o.isDark(t,i+2)&&o.isDark(t,i+3)&&o.isDark(t,i+4)&&!o.isDark(t,i+5)&&o.isDark(t,i+6)&&(r+=40);for(i=0;i<e;i++)for(t=0;t<e-6;t++)o.isDark(t,i)&&!o.isDark(t+1,i)&&o.isDark(t+2,i)&&o.isDark(t+3,i)&&o.isDark(t+4,i)&&!o.isDark(t+5,i)&&o.isDark(t+6,i)&&(r+=40);var g=0;for(i=0;i<e;i++)for(t=0;t<e;t++)o.isDark(t,i)&&g++;return r+=10*(Math.abs(100*g/e/e-50)/5)}},c={glog:function(o){if(o<1)throw new Error(\"glog(\"+o+\")\");return c.LOG_TABLE[o]},gexp:function(o){for(;o<0;)o+=255;for(;o>=256;)o-=255;return c.EXP_TABLE[o]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},m=0;m<8;m++)c.EXP_TABLE[m]=1<<m;for(m=8;m<256;m++)c.EXP_TABLE[m]=c.EXP_TABLE[m-4]^c.EXP_TABLE[m-5]^c.EXP_TABLE[m-6]^c.EXP_TABLE[m-8];for(m=0;m<255;m++)c.LOG_TABLE[c.EXP_TABLE[m]]=m;function f(o,e){if(null==o.length)throw new Error(o.length+\"/\"+e);for(var r=0;r<o.length&&0==o[r];)r++;this.num=new Array(o.length-r+e);for(var t=0;t<o.length-r;t++)this.num[t]=o[t+r];}function v(o,e){this.totalCount=o,this.dataCount=e;}function p(){this.buffer=new Array,this.length=0;}function C(o){return o.setFillStyle=o.setFillStyle||function(e){o.fillStyle=e;},o.setFontSize=o.setFontSize||function(e){o.font=`${e}px`;},o.setTextAlign=o.setTextAlign||function(e){o.textAlign=e;},o.setTextBaseline=o.setTextBaseline||function(e){o.textBaseline=e;},o.setGlobalAlpha=o.setGlobalAlpha||function(e){o.globalAlpha=e;},o.setStrokeStyle=o.setStrokeStyle||function(e){o.strokeStyle=e;},o.setShadow=o.setShadow||function(e,r,t,i){o.shadowOffsetX=e,o.shadowOffsetY=r,o.shadowBlur=t,o.shadowColor=i;},o.draw=o.draw||function(o,e){e&&e();},o.clearRect=o.clearRect||function(e,r,t,i){o.draw(!1);},o}function b(o,e){var r=this.data=\"\",t=this.size=200;this.useDynamicSize=!1,this.dynamicSize=t;var i=this.typeNumber=-1;this.errorCorrectLevel=b.errorCorrectLevel.H;var n=this.margin=0;this.areaColor=\"#FFFFFF\",this.backgroundColor=\"rgba(255,255,255,0)\",this.backgroundImageSrc=void 0;var a=this.backgroundImageWidth=void 0,d=this.backgroundImageHeight=void 0,u=this.backgroundImageX=void 0,s=this.backgroundImageY=void 0;this.backgroundImageAlpha=1,this.backgroundImageBorderRadius=0;var g=this.backgroundPadding=0;this.foregroundColor=\"#000000\",this.foregroundImageSrc=void 0;var l=this.foregroundImageWidth=void 0,h=this.foregroundImageHeight=void 0,c=this.foregroundImageX=void 0,m=this.foregroundImageY=void 0,f=this.foregroundImagePadding=0;this.foregroundImageBackgroundColor=\"#FFFFFF\";var v=this.foregroundImageBorderRadius=0,p=this.foregroundImageShadowOffsetX=0,k=this.foregroundImageShadowOffsetY=0,y=this.foregroundImageShadowBlur=0;this.foregroundImageShadowColor=\"#808080\";var w=this.foregroundPadding=0,I=this.positionProbeBackgroundColor=void 0,B=this.positionProbeForegroundColor=void 0,S=this.separatorColor=void 0,P=this.positionAdjustBackgroundColor=void 0,L=this.positionAdjustForegroundColor=void 0,D=this.timingBackgroundColor=void 0,A=this.timingForegroundColor=void 0,E=this.typeNumberBackgroundColor=void 0,T=this.typeNumberForegroundColor=void 0,N=this.darkBlockColor=void 0;this.base=void 0,this.modules=[],this.moduleCount=0,this.drawModules=[];var M=this.canvasContext=void 0;this.loadImage,this.drawReserve=!1,this.isMaked=!1,Object.defineProperties(this,{data:{get(){if(\"\"===r||void 0===r)throw console.error(\"[uQRCode]: data must be set!\"),new b.Error(\"data must be set!\");return r},set(o){r=String(o);}},size:{get:()=>t,set(o){t=Number(o);}},typeNumber:{get:()=>i,set(o){i=Number(o);}},margin:{get:()=>n,set(o){n=Number(o);}},backgroundImageWidth:{get(){return void 0===a?this.dynamicSize:this.useDynamicSize?this.dynamicSize/this.size*a:a},set(o){a=Number(o);}},backgroundImageHeight:{get(){return void 0===d?this.dynamicSize:this.useDynamicSize?this.dynamicSize/this.size*d:d},set(o){d=Number(o);}},backgroundImageX:{get(){return void 0===u?0:this.useDynamicSize?this.dynamicSize/this.size*u:u},set(o){u=Number(o);}},backgroundImageY:{get(){return void 0===s?0:this.useDynamicSize?this.dynamicSize/this.size*s:s},set(o){s=Number(o);}},backgroundPadding:{get:()=>g,set(o){g=o>1?1:o<0?0:o;}},foregroundImageWidth:{get(){return void 0===l?(this.dynamicSize-2*this.margin)/4:this.useDynamicSize?this.dynamicSize/this.size*l:l},set(o){l=Number(o);}},foregroundImageHeight:{get(){return void 0===h?(this.dynamicSize-2*this.margin)/4:this.useDynamicSize?this.dynamicSize/this.size*h:h},set(o){h=Number(o);}},foregroundImageX:{get(){return void 0===c?this.dynamicSize/2-this.foregroundImageWidth/2:this.useDynamicSize?this.dynamicSize/this.size*c:c},set(o){c=Number(o);}},foregroundImageY:{get(){return void 0===m?this.dynamicSize/2-this.foregroundImageHeight/2:this.useDynamicSize?this.dynamicSize/this.size*m:m},set(o){m=Number(o);}},foregroundImagePadding:{get(){return this.useDynamicSize?this.dynamicSize/this.size*f:f},set(o){f=Number(o);}},foregroundImageBorderRadius:{get(){return this.useDynamicSize?this.dynamicSize/this.size*v:v},set(o){v=Number(o);}},foregroundImageShadowOffsetX:{get(){return this.useDynamicSize?this.dynamicSize/this.size*p:p},set(o){p=Number(o);}},foregroundImageShadowOffsetY:{get(){return this.useDynamicSize?this.dynamicSize/this.size*k:k},set(o){k=Number(o);}},foregroundImageShadowBlur:{get(){return this.useDynamicSize?this.dynamicSize/this.size*y:y},set(o){y=Number(o);}},foregroundPadding:{get:()=>w,set(o){w=o>1?1:o<0?0:o;}},positionProbeBackgroundColor:{get(){return I||this.backgroundColor},set(o){I=o;}},positionProbeForegroundColor:{get(){return B||this.foregroundColor},set(o){B=o;}},separatorColor:{get(){return S||this.backgroundColor},set(o){S=o;}},positionAdjustBackgroundColor:{get(){return P||this.backgroundColor},set(o){P=o;}},positionAdjustForegroundColor:{get(){return L||this.foregroundColor},set(o){L=o;}},timingBackgroundColor:{get(){return D||this.backgroundColor},set(o){D=o;}},timingForegroundColor:{get(){return A||this.foregroundColor},set(o){A=o;}},typeNumberBackgroundColor:{get(){return E||this.backgroundColor},set(o){E=o;}},typeNumberForegroundColor:{get(){return T||this.foregroundColor},set(o){T=o;}},darkBlockColor:{get(){return N||this.foregroundColor},set(o){N=o;}},canvasContext:{get(){if(void 0===M)throw console.error(\"[uQRCode]: use drawCanvas, you need to set the canvasContext!\"),new b.Error(\"use drawCanvas, you need to set the canvasContext!\");return M},set(o){M=C(o);}}}),b.plugins.forEach((o=>o(b,this,!1))),o&&this.setOptions(o),e&&(this.canvasContext=C(e));}f.prototype={get:function(o){return this.num[o]},getLength:function(){return this.num.length},multiply:function(o){for(var e=new Array(this.getLength()+o.getLength()-1),r=0;r<this.getLength();r++)for(var t=0;t<o.getLength();t++)e[r+t]^=c.gexp(c.glog(this.get(r))+c.glog(o.get(t)));return new f(e,0)},mod:function(o){if(this.getLength()-o.getLength()<0)return this;for(var e=c.glog(this.get(0))-c.glog(o.get(0)),r=new Array(this.getLength()),t=0;t<this.getLength();t++)r[t]=this.get(t);for(t=0;t<o.getLength();t++)r[t]^=c.gexp(c.glog(o.get(t))+e);return new f(r,0).mod(o)}},v.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],v.getRSBlocks=function(o,e){var r=v.getRsBlockTable(o,e);if(null==r)throw new Error(\"bad rs block @ typeNumber:\"+o+\"/errorCorrectLevel:\"+e);for(var t=r.length/3,i=new Array,n=0;n<t;n++)for(var a=r[3*n+0],d=r[3*n+1],u=r[3*n+2],s=0;s<a;s++)i.push(new v(d,u));return i},v.getRsBlockTable=function(o,e){switch(e){case t.L:return v.RS_BLOCK_TABLE[4*(o-1)+0];case t.M:return v.RS_BLOCK_TABLE[4*(o-1)+1];case t.Q:return v.RS_BLOCK_TABLE[4*(o-1)+2];case t.H:return v.RS_BLOCK_TABLE[4*(o-1)+3];default:return}},p.prototype={get:function(o){var e=Math.floor(o/8);return 1==(this.buffer[e]>>>7-o%8&1)},put:function(o,e){for(var r=0;r<e;r++)this.putBit(1==(o>>>e-r-1&1));},getLengthInBits:function(){return this.length},putBit:function(o){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),o&&(this.buffer[e]|=128>>>this.length%8),this.length++;}},e.errorCorrectLevel=t,b.errorCorrectLevel=e.errorCorrectLevel,b.Error=function(o){this.errMsg=\"[uQRCode]: \"+o;},b.plugins=[],b.use=function(o){\"function\"==typeof o&&b.plugins.push(o);},b.prototype.loadImage=function(o){return Promise.resolve(o)},b.prototype.setOptions=function(o){var e,r,t,i,n,a,d,u,s,g,l,h,c,m,f,v,p,C,b,k,y,w,I,B,S,P,L,D,A,E,T,N,M,z,R,_,O,F,x,H,X,Y,j,W,G,K,Q,U,$,J,q,V,Z,oo,eo,ro;o&&(Object.keys(o).forEach((e=>{this[e]=o[e];})),function(o={},e={},r=!1){let t;t=r?o:{...o};for(let o in e){var i=e[o];null!=i&&(i.constructor==Object?t[o]=this.deepReplace(t[o],i):i.constructor!=String||i?t[o]=i:t[o]=t[o]);}}(this,{data:o.data||o.text,size:o.size,useDynamicSize:o.useDynamicSize,typeNumber:o.typeNumber,errorCorrectLevel:o.errorCorrectLevel,margin:o.margin,areaColor:o.areaColor,backgroundColor:o.backgroundColor||(null===(e=o.background)||void 0===e?void 0:e.color),backgroundImageSrc:o.backgroundImageSrc||(null===(r=o.background)||void 0===r||null===(t=r.image)||void 0===t?void 0:t.src),backgroundImageWidth:o.backgroundImageWidth||(null===(i=o.background)||void 0===i||null===(n=i.image)||void 0===n?void 0:n.width),backgroundImageHeight:o.backgroundImageHeight||(null===(a=o.background)||void 0===a||null===(d=a.image)||void 0===d?void 0:d.height),backgroundImageX:o.backgroundImageX||(null===(u=o.background)||void 0===u||null===(s=u.image)||void 0===s?void 0:s.x),backgroundImageY:o.backgroundImageY||(null===(g=o.background)||void 0===g||null===(l=g.image)||void 0===l?void 0:l.y),backgroundImageAlpha:o.backgroundImageAlpha||(null===(h=o.background)||void 0===h||null===(c=h.image)||void 0===c?void 0:c.alpha),backgroundImageBorderRadius:o.backgroundImageBorderRadius||(null===(m=o.background)||void 0===m||null===(f=m.image)||void 0===f?void 0:f.borderRadius),backgroundPadding:o.backgroundPadding,foregroundColor:o.foregroundColor||(null===(v=o.foreground)||void 0===v?void 0:v.color),foregroundImageSrc:o.foregroundImageSrc||(null===(p=o.foreground)||void 0===p||null===(C=p.image)||void 0===C?void 0:C.src),foregroundImageWidth:o.foregroundImageWidth||(null===(b=o.foreground)||void 0===b||null===(k=b.image)||void 0===k?void 0:k.width),foregroundImageHeight:o.foregroundImageHeight||(null===(y=o.foreground)||void 0===y||null===(w=y.image)||void 0===w?void 0:w.height),foregroundImageX:o.foregroundImageX||(null===(I=o.foreground)||void 0===I||null===(B=I.image)||void 0===B?void 0:B.x),foregroundImageY:o.foregroundImageY||(null===(S=o.foreground)||void 0===S||null===(P=S.image)||void 0===P?void 0:P.y),foregroundImagePadding:o.foregroundImagePadding||(null===(L=o.foreground)||void 0===L||null===(D=L.image)||void 0===D?void 0:D.padding),foregroundImageBackgroundColor:o.foregroundImageBackgroundColor||(null===(A=o.foreground)||void 0===A||null===(E=A.image)||void 0===E?void 0:E.backgroundColor),foregroundImageBorderRadius:o.foregroundImageBorderRadius||(null===(T=o.foreground)||void 0===T||null===(N=T.image)||void 0===N?void 0:N.borderRadius),foregroundImageShadowOffsetX:o.foregroundImageShadowOffsetX||(null===(M=o.foreground)||void 0===M||null===(z=M.image)||void 0===z?void 0:z.shadowOffsetX),foregroundImageShadowOffsetY:o.foregroundImageShadowOffsetY||(null===(R=o.foreground)||void 0===R||null===(_=R.image)||void 0===_?void 0:_.shadowOffsetY),foregroundImageShadowBlur:o.foregroundImageShadowBlur||(null===(O=o.foreground)||void 0===O||null===(F=O.image)||void 0===F?void 0:F.shadowBlur),foregroundImageShadowColor:o.foregroundImageShadowColor||(null===(x=o.foreground)||void 0===x||null===(H=x.image)||void 0===H?void 0:H.shadowColor),foregroundPadding:o.foregroundPadding,positionProbeBackgroundColor:o.positionProbeBackgroundColor||(null===(X=o.positionProbe)||void 0===X?void 0:X.backgroundColor)||(null===(Y=o.positionDetection)||void 0===Y?void 0:Y.backgroundColor),positionProbeForegroundColor:o.positionProbeForegroundColor||(null===(j=o.positionProbe)||void 0===j?void 0:j.foregroundColor)||(null===(W=o.positionDetection)||void 0===W?void 0:W.foregroundColor),separatorColor:o.separatorColor||(null===(G=o.separator)||void 0===G?void 0:G.color),positionAdjustBackgroundColor:o.positionAdjustBackgroundColor||(null===(K=o.positionAdjust)||void 0===K?void 0:K.backgroundColor)||(null===(Q=o.alignment)||void 0===Q?void 0:Q.backgroundColor),positionAdjustForegroundColor:o.positionAdjustForegroundColor||(null===(U=o.positionAdjust)||void 0===U?void 0:U.foregroundColor)||(null===($=o.alignment)||void 0===$?void 0:$.foregroundColor),timingBackgroundColor:o.timingBackgroundColor||(null===(J=o.timing)||void 0===J?void 0:J.backgroundColor),timingForegroundColor:o.timingForegroundColor||(null===(q=o.timing)||void 0===q?void 0:q.foregroundColor),typeNumberBackgroundColor:o.typeNumberBackgroundColor||(null===(V=o.typeNumber)||void 0===V?void 0:V.backgroundColor)||(null===(Z=o.versionInformation)||void 0===Z?void 0:Z.backgroundColor),typeNumberForegroundColor:o.typeNumberForegroundColor||(null===(oo=o.typeNumber)||void 0===oo?void 0:oo.foregroundColor)||(null===(eo=o.versionInformation)||void 0===eo?void 0:eo.foregroundColor),darkBlockColor:o.darkBlockColor||(null===(ro=o.darkBlock)||void 0===ro?void 0:ro.color)},!0));},b.prototype.make=function(){let{foregroundColor:o,backgroundColor:r,typeNumber:t,errorCorrectLevel:i,data:n,size:a,margin:d,useDynamicSize:u}=this;if(o===r)throw console.error(\"[uQRCode]: foregroundColor and backgroundColor cannot be the same!\"),new b.Error(\"foregroundColor and backgroundColor cannot be the same!\");var s=new e(t,i);s.addData(function(o){o=o.toString();for(var e,r=\"\",t=0;t<o.length;t++)(e=o.charCodeAt(t))>=1&&e<=127?r+=o.charAt(t):e>2047?(r+=String.fromCharCode(224|e>>12&15),r+=String.fromCharCode(128|e>>6&63),r+=String.fromCharCode(128|e>>0&63)):(r+=String.fromCharCode(192|e>>6&31),r+=String.fromCharCode(128|e>>0&63));return r}(n)),s.make(),this.base=s,this.typeNumber=s.typeNumber,this.modules=s.modules,this.moduleCount=s.moduleCount,this.dynamicSize=u?Math.ceil((a-2*d)/s.moduleCount)*s.moduleCount+2*d:a,function(o){let{dynamicSize:e,margin:r,backgroundColor:t,backgroundPadding:i,foregroundColor:n,foregroundPadding:a,modules:d,moduleCount:u}=o,s=(e-2*r)/u,g=s,l=0;i>0&&(l=g*i/2,g-=2*l);let h=s,c=0;a>0&&(c=h*a/2,h-=2*c);for(var m=0;m<u;m++)for(var f=0;f<u;f++){var v=f*s+r,p=m*s+r;if(d[m][f]){var C=c,b=v+c,k=p+c,y=h,w=h;d[m][f]={type:[\"foreground\"],color:n,isBlack:!0,isDrawn:!1,destX:v,destY:p,destWidth:s,destHeight:s,x:b,y:k,width:y,height:w,paddingTop:C,paddingRight:C,paddingBottom:C,paddingLeft:C};}else C=l,b=v+l,k=p+l,y=g,w=g,d[m][f]={type:[\"background\"],color:t,isBlack:!1,isDrawn:!1,destX:v,destY:p,destWidth:s,destHeight:s,x:b,y:k,width:y,height:w,paddingTop:C,paddingRight:C,paddingBottom:C,paddingLeft:C};}}(this),function(o){let{modules:e,moduleCount:r,positionProbeBackgroundColor:t,positionProbeForegroundColor:i}=o,n=r-7;[[0,0,1],[1,0,1],[2,0,1],[3,0,1],[4,0,1],[5,0,1],[6,0,1],[0,1,1],[1,1,0],[2,1,0],[3,1,0],[4,1,0],[5,1,0],[6,1,1],[0,2,1],[1,2,0],[2,2,1],[3,2,1],[4,2,1],[5,2,0],[6,2,1],[0,3,1],[1,3,0],[2,3,1],[3,3,1],[4,3,1],[5,3,0],[6,3,1],[0,4,1],[1,4,0],[2,4,1],[3,4,1],[4,4,1],[5,4,0],[6,4,1],[0,5,1],[1,5,0],[2,5,0],[3,5,0],[4,5,0],[5,5,0],[6,5,1],[0,6,1],[1,6,1],[2,6,1],[3,6,1],[4,6,1],[5,6,1],[6,6,1]].forEach((o=>{var r=e[o[0]][o[1]],a=e[o[0]+n][o[1]],d=e[o[0]][o[1]+n];d.type.push(\"positionProbe\"),a.type.push(\"positionProbe\"),r.type.push(\"positionProbe\"),r.color=1==o[2]?i:t,a.color=1==o[2]?i:t,d.color=1==o[2]?i:t;}));}(this),function(o){let{modules:e,moduleCount:r,separatorColor:t}=o;[[7,0],[7,1],[7,2],[7,3],[7,4],[7,5],[7,6],[7,7],[0,7],[1,7],[2,7],[3,7],[4,7],[5,7],[6,7]].forEach((o=>{var i=e[o[0]][o[1]],n=e[r-o[0]-1][o[1]],a=e[o[0]][r-o[1]-1];a.type.push(\"separator\"),n.type.push(\"separator\"),i.type.push(\"separator\"),i.color=t,n.color=t,a.color=t;}));}(this),function(o){let{typeNumber:e,modules:r,moduleCount:t,foregroundColor:i,backgroundColor:n,positionAdjustForegroundColor:a,positionAdjustBackgroundColor:d,timingForegroundColor:u,timingBackgroundColor:s}=o;const g=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]][e-1];if(g){const o=[[-2,-2,1],[-1,-2,1],[0,-2,1],[1,-2,1],[2,-2,1],[-2,-1,1],[-1,-1,0],[0,-1,0],[1,-1,0],[2,-1,1],[-2,0,1],[-1,0,0],[0,0,1],[1,0,0],[2,0,1],[-2,1,1],[-1,1,0],[0,1,0],[1,1,0],[2,1,1],[-2,2,1],[-1,2,1],[0,2,1],[1,2,1],[2,2,1]],e=g.length;for(let l=0;l<e;l++)for(let h=0;h<e;h++){let{x:e,y:c}={x:g[l],y:g[h]};e<9&&c<9||e>t-9-1&&c<9||c>t-9-1&&e<9||o.forEach((o=>{var t=r[e+o[0]][c+o[1]];t.type.push(\"positionAdjust\"),t.type.includes(\"timing\")?1==o[2]?t.color=a==i?u:a:t.color=a==i&&d==n?s:d:t.color=1==o[2]?a:d;}));}}}(this),function(o){let{modules:e,moduleCount:r,timingForegroundColor:t,timingBackgroundColor:i}=o,n=r-16;for(let o=0;o<n;o++){var a=e[6][8+o],d=e[8+o][6];a.type.push(\"timing\"),d.type.push(\"timing\"),a.color=1&o^1?t:i,d.color=1&o^1?t:i;}}(this),function(o){let{modules:e,moduleCount:r,darkBlockColor:t}=o;var i=e[r-7-1][8];i.type.push(\"darkBlock\"),i.color=t;}(this),function(o){let{typeNumber:e,modules:r,moduleCount:t,typeNumberBackgroundColor:i,typeNumberForegroundColor:n}=o;if(e<7)return r;const a=[0,0,0,0,0,0,0,\"000111110010010100\",\"001000010110111100\",\"001001101010011001\",\"001010010011010011\",\"001011101111110110\",\"001100011101100010\",\"001101100001000111\",\"001110011000001101\",\"001111100100101000\",\"010000101101111000\",\"010001010001011101\",\"010010101000010111\",\"010011010100110010\",\"010100100110100110\",\"010101011010000011\",\"010110100011001001\",\"010111011111101100\",\"011000111011000100\",\"011001000111100001\",\"011010111110101011\",\"011011000010001110\",\"011100110000011010\",\"011101001100111111\",\"011110110101110101\",\"011111001001010000\",\"100000100111010101\",\"100001011011110000\",\"100010100010111010\",\"100011011110011111\",\"100100101100001011\",\"100101010000101110\",\"100110101001100100\",\"100111010101000001\",\"101000110001101001\"];let d=a[e]+a[e],u=[t-11,t-10,t-9];[[5,u[2]],[5,u[1]],[5,u[0]],[4,u[2]],[4,u[1]],[4,u[0]],[3,u[2]],[3,u[1]],[3,u[0]],[2,u[2]],[2,u[1]],[2,u[0]],[1,u[2]],[1,u[1]],[1,u[0]],[0,u[2]],[0,u[1]],[0,u[0]],[u[2],5],[u[1],5],[u[0],5],[u[2],4],[u[1],4],[u[0],4],[u[2],3],[u[1],3],[u[0],3],[u[2],2],[u[1],2],[u[0],2],[u[2],1],[u[1],1],[u[0],1],[u[2],0],[u[1],0],[u[0],0]].forEach(((o,e)=>{var t=r[o[0]][o[1]];t.type.push(\"typeNumber\"),t.color=\"1\"==d[e]?n:i;}));}(this),this.isMaked=!0,this.drawModules=[];},b.prototype.getDrawModules=function(){if(this.drawModules&&this.drawModules.length>0)return this.drawModules;let o=this.drawModules=[],{modules:e,moduleCount:r,dynamicSize:t,areaColor:i,backgroundImageSrc:n,backgroundImageX:a,backgroundImageY:d,backgroundImageWidth:u,backgroundImageHeight:s,backgroundImageAlpha:g,backgroundImageBorderRadius:l,foregroundImageSrc:h,foregroundImageX:c,foregroundImageY:m,foregroundImageWidth:f,foregroundImageHeight:v,foregroundImagePadding:p,foregroundImageBackgroundColor:C,foregroundImageBorderRadius:b,foregroundImageShadowOffsetX:k,foregroundImageShadowOffsetY:y,foregroundImageShadowBlur:w,foregroundImageShadowColor:I}=this;i&&o.push({name:\"area\",type:\"area\",color:i,x:0,y:0,width:t,height:t}),n&&o.push({name:\"backgroundImage\",type:\"image\",imageSrc:n,mappingName:\"backgroundImageSrc\",x:a,y:d,width:u,height:s,alpha:g,borderRadius:l});for(var B=0;B<r;B++)for(var S=0;S<r;S++){var P=e[B][S];P.isDrawn||(P.type.includes(\"foreground\")?o.push({name:\"foreground\",type:\"tile\",color:P.color,destX:P.destX,destY:P.destY,destWidth:P.destWidth,destHeight:P.destHeight,x:P.x,y:P.y,width:P.width,height:P.height,paddingTop:P.paddingTop,paddingRight:P.paddingRight,paddingBottom:P.paddingBottom,paddingLeft:P.paddingLeft,rowIndex:B,colIndex:S}):o.push({name:\"background\",type:\"tile\",color:P.color,destX:P.destX,destY:P.destY,destWidth:P.destWidth,destHeight:P.destHeight,x:P.x,y:P.y,width:P.width,height:P.height,paddingTop:P.paddingTop,paddingRight:P.paddingRight,paddingBottom:P.paddingBottom,paddingLeft:P.paddingLeft,rowIndex:B,colIndex:S}),P.isDrawn=!0);}return h&&o.push({name:\"foregroundImage\",type:\"image\",imageSrc:h,mappingName:\"foregroundImageSrc\",x:c,y:m,width:f,height:v,padding:p,backgroundColor:C,borderRadius:b,shadowOffsetX:k,shadowOffsetY:y,shadowBlur:w,shadowColor:I}),o},b.prototype.isBlack=function(o,e){var r=this.moduleCount;return !(0>o||0>e||o>=r||e>=r)&&this.modules[o][e].isBlack},b.prototype.drawCanvas=function(){let{isMaked:o,canvasContext:e,useDynamicSize:r,dynamicSize:t,foregroundColor:i,foregroundPadding:n,backgroundColor:a,backgroundPadding:d,drawReserve:u,margin:s}=this;if(!o)return console.error(\"[uQRCode]: please execute the make method first!\"),Promise.reject(new b.Error(\"please execute the make method first!\"));let g=this.getDrawModules(),l=async(o,r)=>{try{e.clearRect(0,0,t,t),e.draw(!1);for(var i=0;i<g.length;i++){var n=g[i];switch(e.save(),n.type){case\"area\":e.setFillStyle(n.color),e.fillRect(n.x,n.y,n.width,n.height);break;case\"tile\":var a=n.x,d=n.y,s=n.width,l=n.height;e.setFillStyle(n.color),e.fillRect(a,d,s,l);break;case\"image\":if(\"backgroundImage\"===n.name){a=Math.round(n.x),d=Math.round(n.y),s=Math.round(n.width),l=Math.round(n.height);s<2*(c=Math.round(n.borderRadius))&&(c=s/2),l<2*c&&(c=l/2),e.setGlobalAlpha(n.alpha),c>0&&(e.beginPath(),e.moveTo(a+c,d),e.arcTo(a+s,d,a+s,d+l,c),e.arcTo(a+s,d+l,a,d+l,c),e.arcTo(a,d+l,a,d,c),e.arcTo(a,d,a+s,d,c),e.closePath(),e.setStrokeStyle(\"rgba(0,0,0,0)\"),e.stroke(),e.clip());try{var h=await this.loadImage(n.imageSrc);e.drawImage(h,a,d,s,l);}catch(o){throw console.error(`[uQRCode]: ${n.mappingName} invalid!`),new b.Error(`${n.mappingName} invalid!`)}}else if(\"foregroundImage\"===n.name){a=Math.round(n.x),d=Math.round(n.y),s=Math.round(n.width),l=Math.round(n.height);var c,m=Math.round(n.padding);s<2*(c=Math.round(n.borderRadius))&&(c=s/2),l<2*c&&(c=l/2);var f=a-m,v=d-m,p=s+2*m,C=l+2*m,k=Math.round(p/s*c);p<2*k&&(k=p/2),C<2*k&&(k=C/2),e.save(),e.setShadow(n.shadowOffsetX,n.shadowOffsetY,n.shadowBlur,n.shadowColor),k>0?(e.beginPath(),e.moveTo(f+k,v),e.arcTo(f+p,v,f+p,v+C,k),e.arcTo(f+p,v+C,f,v+C,k),e.arcTo(f,v+C,f,v,k),e.arcTo(f,v,f+p,v,k),e.closePath(),e.setFillStyle(n.backgroundColor),e.fill()):(e.setFillStyle(n.backgroundColor),e.fillRect(f,v,p,C)),e.restore(),e.save(),k>0?(e.beginPath(),e.moveTo(f+k,v),e.arcTo(f+p,v,f+p,v+C,k),e.arcTo(f+p,v+C,f,v+C,k),e.arcTo(f,v+C,f,v,k),e.arcTo(f,v,f+p,v,k),e.closePath(),e.setFillStyle(m>0?n.backgroundColor:\"rgba(0,0,0,0)\"),e.fill()):(e.setFillStyle(m>0?n.backgroundColor:\"rgba(0,0,0,0)\"),e.fillRect(f,v,p,C)),e.restore(),c>0&&(e.beginPath(),e.moveTo(a+c,d),e.arcTo(a+s,d,a+s,d+l,c),e.arcTo(a+s,d+l,a,d+l,c),e.arcTo(a,d+l,a,d,c),e.arcTo(a,d,a+s,d,c),e.closePath(),e.setStrokeStyle(\"rgba(0,0,0,0)\"),e.stroke(),e.clip());try{h=await this.loadImage(n.imageSrc);e.drawImage(h,a,d,s,l);}catch(o){throw console.error(`[uQRCode]: ${n.mappingName} invalid!`),new b.Error(`${n.mappingName} invalid!`)}}}u&&e.draw(!0),e.restore();}e.draw(!0),setTimeout(o,150);}catch(o){if(!(o instanceof b.Error))throw o;r(o);}};return new Promise(((o,e)=>{l(o,e);}))},b.prototype.draw=function(){return this.drawCanvas()},b.prototype.register=function(o){o&&o(b,this,!0);};export{b as default};"], "sourceRoot": ""}