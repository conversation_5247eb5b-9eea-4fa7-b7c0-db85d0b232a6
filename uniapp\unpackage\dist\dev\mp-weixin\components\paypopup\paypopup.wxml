<view><u-popup vue-id="318c39fc-1" show="{{popShow}}" round="{{10}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="popBox"><view class="flexs flexc popBox_title"><view></view><view>请选择支付方式</view><view data-event-opts="{{[['tap',[['close1',['$event']]]]]}}" bindtap="__e"><u-icon vue-id="{{('318c39fc-2')+','+('318c39fc-1')}}" name="close" size="32rpx" bind:__l="__l"></u-icon></view></view><view class="popBox_content"><radio-group><block wx:for="{{pay_type}}" wx:for-item="item" wx:for-index="index" wx:key="*this"><label class="uni-list-cell uni-list-cell-pd"><block wx:if="{{item=='wechat'}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" class="flexc popBox_content_item" bindtap="__e"><view class="flexs" style="width:90%;"><view>微信</view><radio value="{{index}}" checked="{{index===current}}"></radio></view></view></block><block wx:if="{{item=='yue'&&isqb==1}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({index})}}" class="flexc popBox_content_item" bindtap="__e"><view class="flexs" style="width:90%;"><view>{{"钱包余额("+(userinfo.money||'0.00')+")"}}</view><radio value="{{index}}" checked="{{index===current}}"></radio></view></view></block></label></block></radio-group></view><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="payBtn" bindtap="__e">立即付款</view></view></u-popup></view>