{"version": 3, "sources": ["webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/login/login.vue?cc93", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/login/login.vue?bd2d", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/login/login.vue?d682", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/login/login.vue?8af1", "uni-app:///components/login/login.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/login/login.vue?7467", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/login/login.vue?bd20"], "names": ["props", "name", "data", "loginavatar", "usershow", "cell", "time", "code", "select", "getuserinfo", "logincode", "common", "login_name", "login_url", "mounted", "loginApi", "watch", "show", "methods", "checkApi", "phone", "smsCode", "errCode", "msg", "uni", "title", "icon", "timeevent", "clearTimeout", "sendApi", "selectevent", "loginClick", "close", "getphonenumber", "e", "getUserDetail", "getAsset", "setUserDetail", "res"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC2GnnB;AASA;AAMA;AAIA;AAEA;AAAA;AAAA,eAEA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACAC;MACA;IACA;IACA;IACA;EACA;;EACAC;IACAC;MACA,aAEA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAKA;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBANAnB;gBACAoB;gBACAC;gBAKA;kBACAC;kBACA;gBACA;kBACAA;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAF;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;MACA;MACA;QACA;QACA;UACA;UACAC;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA;kBACAT;gBACA;cAAA;gBAAA;gBALAlB;gBACAoB;gBACAC;gBAIA,mBAEA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAI;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;QACAP;UACAC;UACAC;QACA;MACA;IAGA;IACAM;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAIA;kBACA3B;gBACA;cAAA;gBAAA;gBAJAe;gBACApB;gBAIA;kBACAsB;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAFAb;gBACApB;gBAEA;kBACAsB;kBACA;kBACA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAA;gBAHAlC;gBACAoB;gBACAC;gBAEA;kBACAC;kBACA;gBACA;kBACAA;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAW;MACA,yDACAC,KACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7RA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/login/login.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=55f941d8&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/login/login.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=template&id=55f941d8&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uDivider: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-divider/u-divider\" */ \"uview-ui/components/u-divider/u-divider.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.cell.length\n  var g1 = _vm.code.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.usershow = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.usershow = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<!-- :closeable='true' -->\r\n\t\t<u-popup :show=\"show\" :round=\"20\" @close=\"close\" :safeAreaInsetTop='true'>\r\n\t\t\t<view class=\"login\">\r\n\t\t\t\t<view class=\"close\" @click=\"close\">\r\n\t\t\t\t\t<u-icon name='close' size='40'></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"hadr\">\r\n\t\t\t\t\t<view>手机号登录/注册</view>\r\n\t\t\t\t\t<view>欢迎登录</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"input\">\r\n\t\t\t\t\t<view class=\"input_1\">\r\n\t\t\t\t\t\t<view class=\"input_1_left\">\r\n\t\t\t\t\t\t\t手机号\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<input type=\"text\" v-model=\"cell\" maxlength=\"11\" placeholder=\"请输入手机号\">\r\n\t\t\t\t\t\t<view class=\"input_1_right\" style=\"\" v-if=\"cell.length!=11\">\r\n\t\t\t\t\t\t\t获取验证码\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"input_1_right\" v-else style=\"background: #05B6F6;\" @tap=\"timeevent\">\r\n\t\t\t\t\t\t\t<text v-if=\"time==60\">获取验证码</text>\r\n\t\t\t\t\t\t\t<text v-else>{{time}}s</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"input_2\">\r\n\t\t\t\t\t\t<view class=\"input_2_left\">\r\n\t\t\t\t\t\t\t验证码\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<input type=\"text\" v-model=\"code\" maxlength=\"4\" placeholder=\"请输入验证码\">\r\n\t\t\t\t\t\t<view class=\"input_2_right\" :style=\"{background: code.length==4?'#05B6F6':''}\" @tap=\"checkApi\">\r\n\t\t\t\t\t\t\t登录\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"wechat\">\r\n\t\t\t\t\t<view class=\"wechat_division\" v-if=\"common.is_shlogin!==1\">\r\n\t\t\t\t\t\t<u-divider text=\"手机号快捷登录\" textSize='24rpx'></u-divider>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<button v-if=\"select\" open-type=\"getPhoneNumber\" @getphonenumber=\"getphonenumber\">\r\n\t\t\t\t\t\t\t<view class=\"wechat_img\">\r\n\t\t\t\t\t\t\t\t<image src=\"@/static/phone.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<view v-else class=\"wechat_img\" @click=\"loginClick\">\r\n\t\t\t\t\t\t\t<image src=\"@/static/phone.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"agreement\">\r\n\t\t\t\t\t\t<label class=\"radio\" @tap=\"selectevent\">\r\n\t\t\t\t\t\t\t<radio value=\"r1\" style=\"transform: scale(0.7);\" color=\"#05B6F6\" :checked=\"select\" />\r\n\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<text>登录即代表同意</text>\r\n\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t@tap=\"routerTo('/pages/order_all/login_protocol/login_protocol?name='+'用户协议'+'&state='+0)\">《用户协议》,</text>\r\n\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t@tap=\"routerTo('/pages/order_all/login_protocol/login_protocol?name='+'隐私政策'+'&state='+0)\">《隐私政策》</text>\r\n\t\t\t\t\t\t\t<text>及</text>\r\n\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t@tap=\"routerTo('/pages/order_all/login_protocol/login_protocol?name='+'第三方SDK类服务商说明'+'&state='+0)\">《第三方SDK类服务商说明》</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\r\n\r\n\t\t<u-popup :closeOnClickOverlay='false' :show=\"usershow\" :round=\"10\" mode='center' @close=\"usershow=false\">\r\n\t\t\t<view class=\"user_box\">\r\n\t\t\t\t<view class=\"user_box_title\">\r\n\t\t\t\t\t<view class=\"\" style=\"margin-left: 30rpx;\"></view>\r\n\t\t\t\t\t<view class=\"\">登录后可享受更多功能</view>\r\n\t\t\t\t\t<view class=\"\" @click=\"usershow=false\">\r\n\t\t\t\t\t\t<u-icon name='close' size='40'></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button open-type='chooseAvatar' @chooseavatar=\"setimg\">\r\n\t\t\t\t\t<view class=\"user_avatar\">\r\n\t\t\t\t\t\t<image v-if=\"loginavatar\" :src=\"loginavatar\" mode=\"\"></image>\r\n\t\t\t\t\t\t<image v-else src=\"../../static/avatar.png\" mode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</button>\r\n\r\n\t\t\t\t<view class=\"user_ipt\">\r\n\t\t\t\t\t<view>昵称</view>\r\n\t\t\t\t\t<input @change='change' type=\"nickname\" v-model=\"login_name\" placeholder=\"请输入昵称\" />\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"user_tag\">\r\n\t\t\t\t\t99%+的用户使用选择微信头像和微信昵\r\n\t\t\t\t\t称，便于订单发货和售后沟通\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"user_btn\" @click=\"wxlogin()\">\r\n\t\t\t\t\t立即登录\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tsend,\r\n\t\tcheck,\r\n\t\tlogin,\r\n\t\twx_login,\r\n\t\tget_openid,\r\n\t\tgetInfo,\r\n\t\tgetWxPhone\r\n\t} from \"@/api/user.js\"\r\n\timport {\r\n\t\tsmsSend,\r\n\t\tsmsLogin,\r\n\t\tgetAssets\r\n\t} from \"@/api/comm.js\"\r\n\r\n\timport {\r\n\t\tuserInfo,\r\n\t\tcommon\r\n\t} from \"@/api/public.js\"\r\n\timport\r\n\tloginApi\r\n\tfrom '@/utils/wxApi.js'\r\n\r\n\texport default {\r\n\t\tprops: ['show'],\r\n\t\tname: \"login\",\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloginavatar: '',\r\n\t\t\t\tusershow: false,\r\n\t\t\t\tcell: '',\r\n\t\t\t\ttime: 60,\r\n\t\t\t\tcode: '',\r\n\t\t\t\t// 单选\r\n\t\t\t\tselect: false,\r\n\t\t\t\tgetuserinfo: {},\r\n\t\t\t\tlogincode: '',\r\n\t\t\t\tcommon: null,\r\n\t\t\t\tlogin_name: \"\",\r\n\t\t\t\tlogin_url: null\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tloginApi.loginApi().then(res => {\r\n\t\t\t\tthis.logincode = res.code\r\n\t\t\t})\r\n\t\t\tlet userinfo = uni.getStorageSync('user')\r\n\t\t\t// this.commonApi()\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tshow(newVal) {\r\n\t\t\t\tif (newVal) {\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync checkApi() {\r\n\t\t\t\tif (this.select) {\r\n\t\t\t\t\tlet {\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\terrCode,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = await smsLogin({\r\n\t\t\t\t\t\tphone: this.cell,\r\n\t\t\t\t\t\tsmsCode: this.code\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\t\tuni.setStorageSync('TOKEN', data)\r\n\t\t\t\t\t\tthis.getUserDetail()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请勾选用户协议',\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttimeevent() {\r\n\t\t\t\tthis.sendApi()\r\n\t\t\t\tlet timer = setInterval(() => {\r\n\t\t\t\t\tthis.time--\r\n\t\t\t\t\tif (!this.time) {\r\n\t\t\t\t\t\tthis.time = 60\r\n\t\t\t\t\t\tclearTimeout(timer)\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000);\r\n\t\t\t},\r\n\t\t\tasync sendApi() {\r\n\t\t\t\tlet {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await smsSend({\r\n\t\t\t\t\tphone: this.cell\r\n\t\t\t\t})\r\n\t\t\t\tif (errCode == 0) {\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tselectevent() {\r\n\t\t\t\tthis.select = this.select ? false : true\r\n\t\t\t},\r\n\t\t\tloginClick() {\r\n\t\t\t\tif (this.select) {\r\n\t\t\t\t\tthis.usershow = true\r\n\t\t\t\t\tthis.close()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请勾选用户协议',\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t},\r\n\t\t\tclose(data) {\r\n\t\t\t\tthis.$emit('closepage', data)\r\n\t\t\t},\r\n\t\t\tasync getphonenumber(e) {\r\n\t\t\t\tif (e.detail.errMsg.indexOf('ok') != -1) {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\terrCode,\r\n\t\t\t\t\t\tdata\r\n\t\t\t\t\t} = await getWxPhone({\r\n\t\t\t\t\t\tcode: e.detail.code\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\t\tuni.setStorageSync('TOKEN', data.token)\r\n\t\t\t\t\t\tthis.getUserDetail()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getUserDetail() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tdata\r\n\t\t\t\t} = await getInfo()\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tuni.setStorageSync('user', data)\r\n\t\t\t\t\tthis.setUserDetail(data)\r\n\t\t\t\t\tthis.getAsset()\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getAsset() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await getAssets()\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tuni.setStorageSync('ASSET', data)\r\n\t\t\t\t\tthis.close(data)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetUserDetail(res) {\r\n\t\t\t\tthis.$store.commit('user/setUserInfo', {\r\n\t\t\t\t\t...res,\r\n\t\t\t\t})\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t::v-deep.uicon-close {\r\n\t\tfont-size: 50rpx;\r\n\t}\r\n\r\n\t.user_box {\r\n\t\twidth: 600rpx;\r\n\t\theight: 700rpx;\r\n\t\ttext-align: center;\r\n\t\tpadding: 30rpx 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: space-between;\r\n\r\n\t\t.user_box_title {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tfont-size: 34rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\r\n\t\t.user_avatar {\r\n\t\t\timage {\r\n\t\t\t\twidth: 200rpx;\r\n\t\t\t\theight: 200rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.user_ipt {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #3d3d3d;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tborder-bottom: 1rpx #eee solid;\r\n\r\n\t\t\tinput {\r\n\t\t\t\twidth: 400rpx;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.user_tag {\r\n\t\t\tbackground-color: #f3f3ff;\r\n\t\t\tpadding: 24rpx 30rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #848484;\r\n\t\t\ttext-align: left;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t}\r\n\r\n\t\t.user_btn {\r\n\t\t\tbackground-color: #05B6F6;\r\n\t\t\theight: 80rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.login {\r\n\t\theight: 1000rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tz-index: 9999999;\r\n\r\n\t\t.close {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 30rpx;\r\n\t\t\tright: 30rpx;\r\n\t\t\tz-index: 99999991;\r\n\t\t}\r\n\r\n\t\t.hadr {\r\n\t\t\tpadding: 30rpx 40rpx;\r\n\r\n\t\t\tview:nth-child(1) {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #0D0D0D;\r\n\t\t\t}\r\n\r\n\t\t\tview:nth-child(2) {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #9A9A9A;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.input {\r\n\t\t\tpadding: 0 40rpx;\r\n\r\n\t\t\t.input_1 {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tborder-bottom: 1rpx solid #CCCCCC;\r\n\t\t\t\tpadding: 30rpx 0;\r\n\r\n\t\t\t\t.input_1_left {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcolor: #0D0D0D;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tinput {\r\n\t\t\t\t\twidth: 300rpx;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #9A9A9A;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.input_1_right {\r\n\t\t\t\t\twidth: 162rpx;\r\n\t\t\t\t\theight: 57rpx;\r\n\t\t\t\t\tbackground: #95DBF5;\r\n\t\t\t\t\tborder-radius: 29rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tline-height: 57rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.input_2 {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tborder-bottom: 1rpx solid #CCCCCC;\r\n\t\t\t\tpadding: 30rpx 0;\r\n\r\n\t\t\t\t.input_2_left {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcolor: #0D0D0D;\r\n\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\tinput {\r\n\t\t\t\t\twidth: 300rpx;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #9A9A9A;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.input_2_right {\r\n\t\t\t\t\twidth: 162rpx;\r\n\t\t\t\t\theight: 57rpx;\r\n\t\t\t\t\tbackground: #95DBF5;\r\n\t\t\t\t\tborder-radius: 29rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tline-height: 57rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.wechat {\r\n\t\t\t.wechat_division {\r\n\t\t\t\twidth: 80%;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tpadding: 40rpx 0;\r\n\t\t\t\tfont-size: 24rpx\r\n\t\t\t}\r\n\r\n\t\t\t.wechat_img {\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t\theight: 100rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tmargin-top: 40rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.agreement {\r\n\t\t\t\twidth: 523rpx;\r\n\t\t\t\theight: 66rpx;\r\n\t\t\t\tmargin: 30rpx auto;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #9A9A9A;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\ttext:nth-child(1) {}\r\n\r\n\t\t\t\ttext:nth-child(2) {\r\n\t\t\t\t\tcolor: #4270BA;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttext:nth-child(3) {\r\n\t\t\t\t\tcolor: #4270BA;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttext:nth-child(4) {}\r\n\r\n\t\t\t\ttext:nth-child(5) {\r\n\t\t\t\t\tcolor: #4270BA;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557570428\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}