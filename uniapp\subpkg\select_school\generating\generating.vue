<template>
	<view class="content">
		<height :hg='System_height'></height>
		<view class="content_header">
			<view class="nav-title" :style="{'top':System_height+'rpx'}">
				<text>AI考研择校报告</text>
			</view>
			<uni-icons type="left" size="24" color="#2D2D2D" class="back-left" :style="{'top':System_height+'rpx'}"
				@tap="back"></uni-icons>
		</view>
		
		<!-- 生成中状态 -->
		<view class="generating-section" v-if="isGenerating">
			<view class="ai-container">
				<view class="ai-images">
					<image src="/static/select_school/ai4-56586a.png" class="ai-bg-4" :class="{ 'rotating': isGenerating }"></image>
					<image src="/static/select_school/ai1-56586a.png" class="ai-bg-1" :class="{ 'rotating': isGenerating }"></image>
					<image src="/static/select_school/ai2-56586a.png" class="ai-bg-2" :class="{ 'rotating': isGenerating }"></image>
					<view class="ai-center">
						<image src="/static/select_school/ai-56586a.png" class="ai-main" :class="{ 'pulsing': isGenerating }"></image>
						<image src="/static/select_school/ai_text-56586a.png" class="ai-text"></image>
					</view>
				</view>
			</view>
			<view class="generating-text">
				<text>AI正在为您生成专属报告...</text>
			</view>
			<view class="progress-bar">
				<view class="progress-fill" :style="{ width: progress + '%' }"></view>
			</view>
			<view class="progress-text">{{ progress }}%</view>
		</view>
		
		<!-- 生成完成状态 -->
		<view class="success-section" v-if="!isGenerating">
			<view class="success-icon">
				<uni-icons type="checkmarkempty" size="60" color="#00C2A0"></uni-icons>
			</view>
			<view class="success-title">报告生成成功！</view>
			<view class="success-desc">您的AI考研择校报告已生成完成</view>
			
			<view class="report-preview">
				<view class="report-name">AI考研择校报告</view>
				<view class="report-time">生成时间：{{ currentTime }}</view>
				<view class="report-content">
					<text>报告包含专业分析、院校分析、学习计划等针对性综合分析</text>
				</view>
			</view>
			
			<view class="action-buttons">
				<view class="view-btn" @click="viewReport">
					<text>查看报告</text>
				</view>
				<view class="back-btn" @click="backToHome">
					<text>返回首页</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				isGenerating: true,
				progress: 0,
				currentTime: ''
			}
		},
		computed: {
			...mapState(['System_height'])
		},
		onLoad() {
			this.startGenerating()
			this.getCurrentTime()
		},
		methods: {
			back() {
				if (this.isGenerating) {
					uni.showModal({
						title: '提示',
						content: '报告正在生成中，确定要退出吗？',
						success: (res) => {
							if (res.confirm) {
								uni.navigateBack()
							}
						}
					})
				} else {
					uni.navigateBack()
				}
			},
			startGenerating() {
				// 模拟生成进度
				const timer = setInterval(() => {
					this.progress += Math.random() * 15
					if (this.progress >= 100) {
						this.progress = 100
						this.isGenerating = false
						clearInterval(timer)
					}
				}, 500)
			},
			getCurrentTime() {
				const now = new Date()
				const year = now.getFullYear()
				const month = now.getMonth() + 1
				const day = now.getDate()
				this.currentTime = `${year}年${month}月${day}日`
			},
			viewReport() {
				// 查看报告详情
				uni.showToast({
					title: '查看报告功能待开发',
					icon: 'none'
				})
			},
			backToHome() {
				// 返回考研择校首页
				uni.navigateTo({
					url: '/subpkg/select_school/index/index'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		min-height: 100vh;
		background: linear-gradient(180deg, #CBF2E0 0%, #F6F7FB 43.3%);
	}
	
	.content_header {
		position: relative;
		height: 140rpx;
		background: #00C2A0;
		
		.nav-title {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			z-index: 10;
			
			text {
				font-size: 34rpx;
				color: #2D2D2D;
				font-weight: 400;
			}
		}
		
		.back-left {
			position: absolute;
			left: 30rpx;
			z-index: 10;
		}
	}
	
	.generating-section {
		padding: 60rpx 30rpx;
		text-align: center;
		
		.ai-container {
			position: relative;
			height: 400rpx;
			margin-bottom: 60rpx;
			
			.ai-images {
				position: relative;
				height: 100%;
				
				.ai-bg-4 {
					position: absolute;
					top: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 300rpx;
					height: 300rpx;
					z-index: 1;
					
					&.rotating {
						animation: rotate 3s linear infinite;
					}
				}
				
				.ai-bg-1 {
					position: absolute;
					top: 20rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 280rpx;
					height: 280rpx;
					z-index: 2;
					
					&.rotating {
						animation: rotate 2s linear infinite reverse;
					}
				}
				
				.ai-bg-2 {
					position: absolute;
					top: 40rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 260rpx;
					height: 220rpx;
					z-index: 3;
					
					&.rotating {
						animation: rotate 4s linear infinite;
					}
				}
				
				.ai-center {
					position: absolute;
					top: 80rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 180rpx;
					height: 180rpx;
					z-index: 4;
					
					.ai-main {
						width: 100%;
						height: 100%;
						
						&.pulsing {
							animation: pulse 2s ease-in-out infinite;
						}
					}
					
					.ai-text {
						position: absolute;
						top: 50rpx;
						left: 25rpx;
						width: 130rpx;
						height: 88rpx;
					}
				}
			}
		}
		
		.generating-text {
			font-size: 32rpx;
			color: #2D2D2D;
			margin-bottom: 40rpx;
		}
		
		.progress-bar {
			width: 80%;
			height: 8rpx;
			background: #E0E0E0;
			border-radius: 4rpx;
			margin: 0 auto 20rpx;
			overflow: hidden;
			
			.progress-fill {
				height: 100%;
				background: linear-gradient(90deg, #26C8AC 0%, #19C990 100%);
				border-radius: 4rpx;
				transition: width 0.3s ease;
			}
		}
		
		.progress-text {
			font-size: 28rpx;
			color: #00C2A0;
			font-weight: 500;
		}
	}
	
	.success-section {
		padding: 60rpx 30rpx;
		text-align: center;
		
		.success-icon {
			width: 120rpx;
			height: 120rpx;
			background: #E8F5E8;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 auto 30rpx;
		}
		
		.success-title {
			font-size: 36rpx;
			color: #2D2D2D;
			font-weight: 500;
			margin-bottom: 20rpx;
		}
		
		.success-desc {
			font-size: 28rpx;
			color: #5A5A5A;
			margin-bottom: 60rpx;
		}
		
		.report-preview {
			background: #FFFFFF;
			border-radius: 16rpx;
			padding: 40rpx;
			margin-bottom: 60rpx;
			box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.1);
			
			.report-name {
				font-size: 32rpx;
				color: #00C2A0;
				font-weight: 500;
				margin-bottom: 20rpx;
			}
			
			.report-time {
				font-size: 24rpx;
				color: #818181;
				margin-bottom: 30rpx;
			}
			
			.report-content {
				font-size: 28rpx;
				color: #5A5A5A;
				line-height: 1.6;
			}
		}
		
		.action-buttons {
			display: flex;
			gap: 30rpx;
			
			.view-btn {
				flex: 1;
				height: 80rpx;
				background: linear-gradient(135deg, #26C8AC 0%, #19C990 100%);
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				
				text {
					font-size: 30rpx;
					color: #FFFFFF;
					font-weight: 400;
				}
			}
			
			.back-btn {
				flex: 1;
				height: 80rpx;
				background: #FFFFFF;
				border: 2rpx solid #00C2A0;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				
				text {
					font-size: 30rpx;
					color: #00C2A0;
					font-weight: 400;
				}
			}
		}
	}
	
	@keyframes rotate {
		from {
			transform: translateX(-50%) rotate(0deg);
		}
		to {
			transform: translateX(-50%) rotate(360deg);
		}
	}
	
	@keyframes pulse {
		0%, 100% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.1);
		}
	}
</style>
