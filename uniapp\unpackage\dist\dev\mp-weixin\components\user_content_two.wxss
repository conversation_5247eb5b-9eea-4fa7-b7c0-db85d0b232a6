@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-777afb4d {
  background: linear-gradient(180deg, rgba(203, 242, 224, 0.2) 0%, rgba(255, 255, 255, 0) 20%);
  border-radius: 14rpx;
  border: 2rpx dashed #1BB394;
  padding: 24rpx 28rpx;
}
@-webkit-keyframes icon-loading-data-v-777afb4d {
0% {
    -webkit-transform: rotate(0);
            transform: rotate(0);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes icon-loading-data-v-777afb4d {
0% {
    -webkit-transform: rotate(0);
            transform: rotate(0);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.container .loading.data-v-777afb4d {
  color: #909090;
}
.container .loading svg.data-v-777afb4d {
  will-change: transform;
  width: 1em;
  height: 1em;
  -webkit-animation: 0.6s linear infinite icon-loading-data-v-777afb4d;
          animation: 0.6s linear infinite icon-loading-data-v-777afb4d;
}
.container .again.data-v-777afb4d {
  display: inline-block;
  width: 100rpx;
  height: 35rpx;
  line-height: 35rpx;
  border-radius: 10rpx;
  text-align: center;
  font-size: 16rpx;
  color: #fff;
  background: #1bb394;
  cursor: pointer;
  margin-left: 10rpx;
}
.container .loading-spinner.data-v-777afb4d {
  width: 36px;
  height: 36px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: currentColor;
  -webkit-animation: spin-data-v-777afb4d 1s linear infinite;
          animation: spin-data-v-777afb4d 1s linear infinite;
}
@-webkit-keyframes spin-data-v-777afb4d {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-777afb4d {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.container .info.data-v-777afb4d {
  padding-top: 30rpx;
}
.container .info .info-title.data-v-777afb4d {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.container .info .info-title .logo.data-v-777afb4d {
  width: 32rpx;
  height: 24rpx;
  margin-right: 8rpx;
}
.container .info .info-title .title-text.data-v-777afb4d {
  font-weight: 800;
  font-size: 16rpx;
  color: #4C5370;
}
.container .info .info-content.data-v-777afb4d {
  margin-top: 20rpx;
}
.container .info .info-content .content-title.data-v-777afb4d {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.container .info .info-content .content-title .cricle.data-v-777afb4d {
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background-color: #AEE8CD;
  margin-right: 10rpx;
}
.container .info .info-content .content-title .content-title-text.data-v-777afb4d {
  font-weight: bold;
  font-size: 16rpx;
  color: #5A5A5A;
}
.container .info .info-content .subtitle-content.data-v-777afb4d {
  margin-top: 20rpx;
}
.container .info .info-content .subtitle-content-container.data-v-777afb4d {
  margin-top: 20rpx;
  border-radius: 9rpx;
  border: 1rpx solid #A0E4C4;
  padding: 28rpx;
}
.container .info .info-content .subtitle-content-container .subtitle-text.data-v-777afb4d {
  display: inline-block;
  padding: 0 6rpx;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 1rpx solid #FF9B3A;
  font-weight: 400;
  font-size: 16rpx;
  line-height: 32rpx;
  text-align: center;
  color: #FF9B3A;
}
.container .info .info-content .subtitle-content-container .subtitle-content-text.data-v-777afb4d {
  margin-top: 10rpx;
  font-weight: 400;
  font-size: 16rpx;
  color: #504E4E;
  line-height: 24rpx;
}
.container.data-v-777afb4d  .sec-title {
  border: 1rpx solid #ff9b3a;
  border-radius: 4rpx;
  font-size: 16rpx !important;
  padding: 6rpx;
  color: #ff9b3a;
  position: relative;
}
.container.data-v-777afb4d  .sec-p {
  width: 100%;
  border-radius: 8rpx;
  padding: 10rpx 15rpx;
  font-size: 16rpx;
  color: #504E4E;
  line-height: 24rpx;
  display: block;
}

