<template>
	<view class="container">
		<view class="content-container">
			<view class="coupon-container">
				<view class="coupon" v-for="(item,index) in coupon_list" :key="index">
					<view class="left">
						<text>{{item.couponValue}} <text style="font-size: 32rpx;">元</text></text>
						<text v-if="item.hasMinOrderAmount">满{{item.minOrderAmount}}可用</text>
						<text v-else>无门槛</text>
					</view>
					<view class="right">
						<view class="name">
							{{item.name}}
						</view>
						<view class="detail-info">
							<view class="detail-left">
								<view class="date" v-if="item.hasIssuePeriod">
									有效期：<text>{{item.issuePeriod[0]}}至{{item.issuePeriod[1]}}</text></view>
								<view class="bottom-rule">
									<view class="rule" @click="rule(index)">
										<text>使用规则</text>
										<u-icon name="arrow-right" color="#009c7b" size="24"></u-icon>
									</view>
									<view class="use-btn" @click="coupon_drawApi(item.id)">
										点击领取
									</view>
									<view class="rules" v-show="item.show" v-html="item.description">
									</view>

								</view>

							</view>
							<view class="detail-right">

							</view>

						</view>
					</view>

				</view>
			</view>
		</view>


	</view>
</template>

<script>
	import {
		coupon_couponList,
		coupon_draw,
		wxCouponList,
		receiveCoupon
	} from "@/api/comm.js"
	export default {
		data() {
			return {
				coupon_list: [],
			};
		},
		onLoad() {
			this.coupon_couponListApi()
		},
		methods: {
			async coupon_couponListApi() {
				let {
					errCode,
					data,
					msg
				} = await wxCouponList({
					page: 1,
					limit: 20,
				})
				if (errCode == 0) {

					this.coupon_list = data.data.map(item => {
						return {
							...item,
							show: 0
						}
					})
				}


			},
			rule(index) {
				if (this.coupon_list[index].show) {
					this.coupon_list[index].show = 0
				} else {
					this.coupon_list[index].show = 1
				}
			},
			async coupon_drawApi(id) {
				let {
					errCode,
					data,
					msg
				} = await receiveCoupon({
					couponId: id
				})
				if (errCode == 0) {
					this.coupon_couponListApi()
					uni.showToast({
						title: '领取成功',
						icon: "success"
					})
				} else {
					uni.showToast({
						title: data.msg,
						icon: "error"
					})
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		view {
			box-sizing: border-box;
		}

		.content-container {
			padding: 30rpx;

			.exchange {
				height: 84rpx;
				border-radius: 40rpx;
				background-color: #F6F7FB;
				display: flex;
				align-items: center;
				justify-content: center;
				padding-left: 240rpx;

				.input-code {
					flex: 3;

				}

				text {
					flex: 1;
					height: 100%;
					border-radius: 0 40rpx 40rpx 0;
					// background-color: $main-color;
					color: #FFF;
					line-height: 84rpx;
					text-align: center;
				}

			}

			.wait-for-use {
				height: 120rpx;
				border-radius: 40rpx;
				border: 1rpx solid #AFAFAF;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 28rpx;

				.tip {
					display: flex;
					align-items: center;
					justify-content: space-between;

					text {
						color: #5A5A5A;
						font-size: 28rpx;
						margin-left: 16rpx;
					}
				}

				.more {
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						color: #EE7878;
						font-size: 24rpx;
						margin-left: 10rpx;
					}
				}
			}

			.coupon-container {
				margin-top: 36rpx;

				.coupon {
					margin-bottom: 28rpx;
					height: 200rpx;
					background-size: contain;
					background-repeat: no-repeat;
					display: flex;
					align-items: center;
					justify-content: flex-start;

					.left {
						width: 192rpx;
						height: 200rpx;
						background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-left.png);
						background-repeat: no-repeat;
						background-size: 192rpx 200rpx;
						color: #fff;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;

						text {
							&:first-child {
								font-weight: bold;
								font-size: 40rpx;
							}

							&:last-child {
								color: #CDF3E7;
								font-size: 26rpx;
							}
						}
					}

					.right {
						background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-right.png);
						background-repeat: no-repeat;
						background-size: 500rpx 200rpx;
						width: 500rpx;
						height: 100%;
						padding: 15rpx 25rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.name {
							color: #4C5370;
							font-size: 30rpx;
							font-weight: bold;
						}

						.detail-info {


							.detail-left {
								.date {
									color: #AFAFAF;
									font-size: 24rpx;

									text {
										font-size: 20rpx;
									}
								}

								.bottom-rule {
									display: flex;
									align-items: center;
									position: relative;
									justify-content: space-between;

									.rule {
										// margin-top: 16rpx;
										display: flex;
										align-items: center;
										justify-content: flex-start;

										text {
											font-size: 24rpx;
											// color: $main-color;
										}
									}

									.use-btn {
										width: 132rpx;
										height: 54rpx;
										border-radius: 32rpx;
										border: 1rpx solid #01997A;
										color: #01997A;
										font-weight: 500;
										line-height: 54rpx;
										text-align: center;
										font-size: 26rpx;
									}
								}

							}

							// .detail-right {
							// 	height: 100%;
							// 	display: flex;
							// 	align-items: center;
							// 	justify-content: center;


							// }
						}
					}



					.rules {
						width: 299rpx;
						background: #FFFFFF;
						box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(0, 0, 0, 0.13);
						border-radius: 8rpx;
						position: absolute;
						top: 50rpx;
						left: -20rpx;
						padding: 20rpx 20rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #A0A0A0;
						z-index: 99999;
					}
				}
			}
		}
	}
</style>