<template>
	<view class="container">
		<view class="tabs">
			<u-tabs :list="list" lineWidth="50" lineHeight="4" lineColor="#01997A" :activeStyle="{
			           
						 color: '#01997A',
						
			        }" :inactiveStyle="{
			             color: '#777777',
						
			        }" itemStyle="width:26%; height: 100rpx;" @click="change">
			</u-tabs>
		</view>
		<view class="content-container">

			<view class="wait-for-use" v-if="currentIndex==0">
				<view class="tip">
					<u-icon name="coupon-fill" color="#009c7b" size="60"></u-icon>
					<text>领取更多优惠券</text>
				</view>
				<view class="more" @click='more'>
					<text>查看更多</text>
					<u-icon name="play-right-fill" color="#BCBBBB" size="18"></u-icon>
				</view>
			</view>
			<view style="margin-top: 150rpx;" v-if="coupon_list.length<1">
				<u-empty mode="data" :iconSize='150' :textSize='24' text='暂无此类优惠券' icon=""></u-empty>
			</view>
			<view class="coupon-container">
				<view class="coupon" v-for="(item,index) in coupon_list" :key="index">
					<view class="left">
						<text>{{item.couponValue}} <text style="font-size: 32rpx;">元</text></text>
						<text>满{{item.minOrderAmount}}可用</text>
					</view>
					<view class="right">
						<view class="name">
							{{item.name}}
						</view>
						<view class="date">
							过期时间：<text>{{item.expireTime}}</text></view>
						<view class="detail-info">
							<view class="detail-left">
								<view class="bottom-rule">
									<view class="rule" @click="rule(index)">
										<text>使用规则</text>
										<u-icon name="arrow-right" color="#009c7b" size="24"></u-icon>
									</view>
									<view class="use-btn" @click="coupon_drawApi()" v-if="currentIndex==0">
										去使用
									</view>
									<view class="use-btn" v-if="currentIndex==1">
										已使用
									</view>
									<view class="use-btns" v-if="currentIndex==2">
										已过期
									</view>
									<view class="rules" v-show="item.show" v-html="item.description">
									</view>

								</view>

							</view>
							<view class="detail-right">

							</view>

						</view>
					</view>

				</view>
			</view>
		</view>


	</view>
</template>

<script>
	import {
		guestCouponList
	} from "@/api/comm.js"
	export default {
		data() {
			return {
				currentIndex: 0,
				couponNum: "",
				list: [{
						name: '未使用'
					},
					{
						name: '已使用',
					},
					{
						name: '已过期'
					},
				],
				coupon_list: [],
				page: 1,
			};
		},
		onLoad() {
			this.coupon_couponListApi()
		},
		methods: {
			//查看更多优惠券
			more() {
				uni.navigateTo({
					url: '/subpkg/all_coupon/all_coupon'
				})
			},
			async coupon_couponListApi() {
				let {
					errCode,
					data,
					msg
				} = await guestCouponList({
					page: this.page,
					limit: 20,
					status: this.currentIndex
				})
				if (errCode == 0) {
					let arr = data.data.map(item => {
						return {
							...item,
							show: 0
						}
					})
					this.coupon_list = [...this.coupon_list, ...arr]
					console.log(this.coupon_list)
				}


			},
			change(item) {
				this.currentIndex = item.index
				this.coupon_list = []
				this.coupon_couponListApi()
			},
			// 去使用
			coupon_drawApi() {
				uni.switchTab({
					url: "/pages/order/order"
				})
			},
			rule(index) {
				if (this.coupon_list[index].show) {
					this.coupon_list[index].show = 0
				} else {
					this.coupon_list[index].show = 1
				}
			},
		},
		onReachBottom() {
			this.page++
			this.coupon_couponListApi()
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.tabs {
			height: 94rpx;
			border-bottom: 1rpx solid #D6D6D6;
		}

		::v-deep .u-tabs__wrapper__nav__line {
			width: 100rpx !important;
			height: 6rpx !important;
		}

		view {
			box-sizing: border-box;
		}

		.content-container {
			padding: 30rpx;

			.exchange {
				height: 84rpx;
				border-radius: 40rpx;
				background-color: #F6F7FB;
				display: flex;
				align-items: center;
				justify-content: center;
				padding-left: 20rpx;

				.input-code {
					flex: 4;

				}

				text {
					flex: 1.5;
					height: 100%;
					border-radius: 0 40rpx 40rpx 0;
					background-color: #00C2A0;
					color: #FFF;
					line-height: 84rpx;
					text-align: center;
				}

			}

			.wait-for-use {
				height: 120rpx;
				border-radius: 40rpx;
				border: 1rpx solid #AFAFAF;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 28rpx;

				.tip {
					display: flex;
					align-items: center;
					justify-content: space-between;

					text {
						color: #5A5A5A;
						font-size: 28rpx;
						margin-left: 16rpx;
					}
				}

				.more {
					display: flex;
					align-items: center;
					justify-content: center;

					text {
						color: #EE7878;
						font-size: 24rpx;
						margin-right: 10rpx;
					}
				}
			}

			.coupon-container {
				margin-top: 36rpx;

				.coupon {
					margin-bottom: 28rpx;
					height: 200rpx;
					background-size: contain;
					background-repeat: no-repeat;
					display: flex;
					align-items: center;
					justify-content: flex-start;

					.left {
						width: 192rpx;
						height: 200rpx;
						background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-left.png);
						background-repeat: no-repeat;
						background-size: 192rpx 200rpx;
						color: #fff;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;

						text {
							&:first-child {
								font-weight: bold;
								font-size: 40rpx;
							}

							&:last-child {
								color: #CDF3E7;
								font-size: 26rpx;
							}
						}
					}

					.right {
						background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-right.png);
						background-repeat: no-repeat;
						background-size: 500rpx 200rpx;
						width: 500rpx;
						height: 100%;
						padding: 15rpx 25rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.name {
							color: #4C5370;
							font-size: 30rpx;
							font-weight: bold;
						}

						.date {
							color: #AFAFAF;
							font-size: 24rpx;

							text {
								font-size: 20rpx;
							}
						}

						.detail-info {


							.detail-left {


								.bottom-rule {
									display: flex;
									align-items: center;
									position: relative;
									justify-content: space-between;

									.rule {
										// margin-top: 16rpx;
										display: flex;
										align-items: center;
										justify-content: flex-start;

										text {
											font-size: 24rpx;
											// color: $main-color;
										}
									}

									.use-btn {
										width: 132rpx;
										height: 54rpx;
										border-radius: 32rpx;
										border: 1rpx solid #01997A;
										color: #01997A;
										font-weight: 500;
										line-height: 54rpx;
										text-align: center;
										font-size: 26rpx;
									}

									.use-btns {
										width: 132rpx;
										height: 54rpx;
										border-radius: 32rpx;
										border: 1rpx solid #bcbbbb;
										background-color: #bcbbbb;
										color: #fff;
										font-weight: 500;
										line-height: 54rpx;
										text-align: center;
										font-size: 26rpx;
									}
								}

							}

							// .detail-right {
							// 	height: 100%;
							// 	display: flex;
							// 	align-items: center;
							// 	justify-content: center;


							// }
						}
					}



					.rules {
						width: 299rpx;
						background: #FFFFFF;
						box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(0, 0, 0, 0.13);
						border-radius: 8rpx;
						position: absolute;
						top: 50rpx;
						left: -20rpx;
						padding: 20rpx 20rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #A0A0A0;
						z-index: 99999;
					}
				}
			}
		}
	}
</style>