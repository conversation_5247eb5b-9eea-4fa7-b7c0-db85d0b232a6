<template>
	<view class="container">
		<view class="title">
			<image src="../../../static/clock.png" mode=""></image>
			<text>产品待核销</text>
		</view>
		<view class="desc">您的订单已生成，请尽快使用！</view>
		<view class="content">
			<view class="class-detail">
				<image :src="detail.productCover" mode=""></image>
				<view class="right">
					<view class="title">{{detail.productName}}</view>
					<view class="desc">好老师 好资料 好服务</view>
					<view class="money">￥{{detail.totalPrice}}</view>
				</view>
			</view>
			<!-- <image class="ma" src="../../../static/ma.png" mode=""></image> -->
			<canvas v-if="showCanvas" canvas-id="qrcode-canvas" id="qrcode-canvas"
				style="width: 300px; height: 300px; position: absolute; left: -9999px"></canvas>

			<!-- 显示的二维码图片 -->
			<image v-if="qrCodeUrl" :src="qrCodeUrl" mode="widthFix" class="ma" />
			<view class="num">券码{{detail.redeemCode}}</view>
			<view class="time" v-if="detail.validYear">{{detail.validYear}}到期</view>
		</view>
		<!-- <view class="order-detail">
			<view class="title">订单信息</view>
			<view class="detail">
				<view class="every">
					<text>订单编号</text>
					<text>{{detail.sn}}</text>
				</view>
				<view class="every">
					<text>下单时间</text>
					<text>{{detail.transTime}}</text>
				</view>
				
				<view class="every">
					<text>优惠券优惠</text>
					<text>{{detail.couponAmount}}</text>
				</view>
			</view>
			<view class="total">实际支付：<text>￥{{detail.checkoutPrice}}</text></view>
		</view> -->

	</view>
</template>

<script>
	// 引入uqrcode组件
	import UQRCode from '@/uni_modules/Sansnn-uQRCode/js_sdk/uqrcode/uqrcode.js'
	export default {
		data() {
			return {
				detail: {},
				display: false,
				verificationCode: '', // 后端返回的核销码
				expiryTime: '', // 核销码有效期
				qrCodeUrl: '', // 生成的二维码图片路径
				showCanvas: false,

			};
		},
		methods: {
			// / 创建二维码 (修复后的方法)
			createQRCode(content) {
				// 显示canvas（微信小程序需要）
				this.showCanvas = true

				// 等待DOM更新
				this.$nextTick(() => {
					try {
						// 创建UQRCode实例
						const qr = new UQRCode()

						// 设置二维码参数
						qr.data = content // 使用后端返回的核销码
						qr.size = 300 // 二维码大小
						qr.margin = 10 // 边距
						qr.foregroundColor = '#000000' // 前景色
						qr.backgroundColor = '#FFFFFF' // 背景色
						qr.errorCorrectLevel = UQRCode.errorCorrectLevel.H // 容错级别

						// 关键修复：先调用make()方法
						qr.make()

						// 获取canvas上下文
						const ctx = uni.createCanvasContext('qrcode-canvas', this)

						// 绘制二维码
						qr.canvasContext = ctx
						qr.drawCanvas()

						// 获取临时图片路径 (微信小程序特殊处理)
						setTimeout(() => {
							uni.canvasToTempFilePath({
								canvasId: 'qrcode-canvas',
								success: (res) => {
									this.qrCodeUrl = res.tempFilePath
									this.showCanvas = false // 生成后隐藏canvas
								},
								fail: (err) => {
									console.error('生成二维码失败', err)
									uni.showToast({
										title: '生成二维码失败',
										icon: 'none'
									})
								}
							}, this)
						}, 300)
					} catch (error) {
						console.error('生成二维码异常:', error)
						uni.showToast({
							title: '生成二维码异常',
							icon: 'none'
						})
					}
				})
			},
		},
		onLoad(options) {
			this.detail = JSON.parse(options.query.obj)
			let str = this.detail.orderId + '_' + this.detail.id + '_' + this.detail.redeemCode
			this.createQRCode(str)
		}

	}
</script>

<style lang="scss">
	.container {
		width: 750rpx;
		padding: 30rpx;
		box-sizing: border-box;
		background: linear-gradient(180deg, #00C2A0 0%, #00C2A0 16%, #F6F7FB 38%, #F6F7FB 100%);

		.title {
			display: flex;
			align-items: center;
			font-weight: bold;
			font-size: 34rpx;
			color: #FFFFFF;

			image {
				width: 52rpx;
				height: 52rpx;
				margin-right: 6rpx;
			}
		}

		.desc {
			font-size: 26rpx;
			color: #FFFFFF;
			margin-top: 8rpx;
			margin-bottom: 30rpx;
		}

		.content {
			width: 690rpx;
			height: 606rpx;
			background: #FFFFFF;
			border-radius: 20rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			box-sizing: border-box;

			.class-detail {
				display: flex;
				padding-bottom: 15rpx;
				border-bottom: 1rpx dashed #D1D1D1;

				image {
					width: 148rpx;
					height: 110rpx;
					margin-right: 10rpx;
				}

				.right {
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.title {
						font-weight: bold;
						font-size: 26rpx;
						color: #414141;
					}

					.desc {
						font-weight: 400;
						font-size: 18rpx;
						color: #777777;
					}

					.money {
						font-weight: bold;
						font-size: 26rpx;
						color: #343434;
					}
				}
			}

			.ma {
				width: 254rpx;
				height: 254rpx;
				margin: 40rpx auto;
				margin-left: 50%;
				transform: translateX(-50%);
			}

			.num {
				font-size: 26rpx;
				color: #343434;
			}

			.time {
				margin-top: 6rpx;
				font-size: 22rpx;
				color: #606060;
			}
		}

		.order-detail {
			width: 690rpx;
			background: #FFFFFF;
			padding: 25rpx 30rpx;
			box-sizing: border-box;
			border-radius: 20rpx;
			margin-bottom: 100rpx;

			.title {
				font-weight: bold;
				font-size: 28rpx;
				color: #201E2E;
				padding-bottom: 18rpx;
				border-bottom: 1rpx dashed #D1D1D1;
			}

			.detail {
				// height: 300rpx;
				padding: 30rpx 0;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				border-bottom: 1rpx dashed #D1D1D1;

				.every {
					width: 100%;
					font-weight: 500;
					font-size: 24rpx;
					color: #A2A2A2;
					margin-bottom: 20rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					text {
						&:last-child {
							font-weight: 500;
							font-size: 24rpx;
							color: #201E2E;
						}
					}
				}
			}

			.total {
				margin-top: 25rpx;
				color: #A4A4A4;
				font-size: 24rpx;
				display: flex;
				align-items: center;
				justify-content: flex-end;

				text {
					font-weight: 500;
					font-size: 34rpx;
					color: #E62E2E;
				}
			}
		}
	}
</style>