@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-7e26637b {
  box-sizing: border-box;
  min-height: 100vh;
  background-color: #fff;
}
.container .head.data-v-7e26637b {
  width: 100%;
  height: 500rpx;
  background-image: url("https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/login_header_img.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.container .head .back-icon.data-v-7e26637b {
  position: absolute;
  top: 30rpx;
  left: 30rpx;
}
.login-form.data-v-7e26637b {
  margin-top: -134rpx;
  padding: 0 28rpx;
  padding-top: 80rpx;
  border-top-left-radius: 36rpx;
  border-top-right-radius: 36rpx;
  background-color: #fff;
}
.form.data-v-7e26637b {
  display: flex;
  flex-direction: column;
}
.input-group.data-v-7e26637b {
  margin-bottom: 20px;
}
input.data-v-7e26637b {
  padding: 10px;
  font-size: 16px;
  border: none;
  border-bottom: 1px solid #ccc;
  outline: none;
}
.active-code.data-v-7e26637b {
  display: flex;
  flex-direction: column;
  padding-left: 20rpx;
}
.uni-input-placeholder.data-v-7e26637b {
  font-size: 28rpx;
  color: #A5A5A5;
}
.active-code-title.data-v-7e26637b {
  font-size: 28rpx;
  color: #A5A5A5;
}
.active-code-btn-container.data-v-7e26637b {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.active-code-btn-container .active-code-btn.data-v-7e26637b {
  width: 70rpx;
  height: 70rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 3rpx 6rpx 1rpx #D6D6D6, inset 0rpx 3rpx 6rpx 1rpx #C1F1DA;
  border-radius: 12rpx;
  border: 1rpx solid #26C8AC;
  font-size: 34rpx;
  color: #5A5A5A;
  text-align: center;
  line-height: 70rpx;
  /* 添加输入框样式 */
  padding: 0;
  margin: 0;
  outline: none;
}
input.data-v-7e26637b:focus {
  border-bottom: 1px solid #1BB394;
}
.code-group.data-v-7e26637b {
  margin-bottom: 20px;
  position: relative;
}
.code-input.data-v-7e26637b {
  flex: 1;
  /* 使输入框占据剩余空间 */
  padding: 10px;
  font-size: 16px;
  border: none;
  border-bottom: 1px solid #ccc;
  outline: none;
}
.code-input.data-v-7e26637b:focus {
  border-bottom: 1px solid #1BB394;
}
.code-btn.data-v-7e26637b {
  width: 160rpx;
  padding: 14rpx;
  background-color: #26C8AC;
  position: absolute;
  right: 10rpx;
  top: 50%;
  -webkit-transform: translateY(-65%);
          transform: translateY(-65%);
  color: white;
  font-size: 22rpx;
  margin-left: 10px;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
}
.code-btn.data-v-7e26637b:disabled {
  background-color: #ccc;
}
.login-btn.data-v-7e26637b {
  width: 80%;
  height: 45px;
  padding: 12px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30px;
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  color: white;
  cursor: pointer;
  margin: 120rpx auto;
}
.agreement.data-v-7e26637b {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  margin-top: 70rpx;
}
.radio-group.data-v-7e26637b {
  display: flex;
  align-items: center;
}
.radio-label.data-v-7e26637b {
  font-size: 14px;
  color: #333;
}
.agreement-text.data-v-7e26637b {
  color: red;
  cursor: pointer;
  margin-left: 5px;
}
.enter-pay.data-v-7e26637b {
  width: 750rpx;
  height: 126rpx;
  position: fixed;
  bottom: 0;
  padding: 0 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #F6F7FB;
}
.enter-pay .title.data-v-7e26637b {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #A5A5A5;
}
.enter-pay .title image.data-v-7e26637b {
  width: 70rpx;
  height: 70rpx;
  margin-right: 10rpx;
}
.enter-pay .btn.data-v-7e26637b {
  width: 145rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-weight: bold;
  font-size: 28rpx;
  color: #FFFFFF;
  background: #26C8AC;
  border-radius: 14rpx;
}

