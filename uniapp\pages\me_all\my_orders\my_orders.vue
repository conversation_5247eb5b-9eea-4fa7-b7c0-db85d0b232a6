<template>
	<view class="container">
		<view class="tabs">
			<text :class="activeIndex==item.status ? 'active' : ''" v-for="(item,index) in list" :key="index"
				@click="meunClick(item.status)">{{item.title}}</text>
		</view>
		<!-- <view class="searchBox">
			<input type="text" placeholder="搜索手机号" />
			<image src="../../../static/me/icon20.png" mode="" @click="open"></image>
		</view> -->
		<!-- <view class="list" @click="detail" v-for="item in allList">
			<view class="time-top">
				<view class="time">下单时间：2025.03.07 23:13</view>
				<view class="status">
					<view class="btn">扫码核销</view>
					<text>待核销</text>
				</view>
			</view>
			<view class="content">
				<view class="title">联报[25考研英语&数学]寒假线上集训营[25考研英语&数学]寒假线上集训营</view>
				<view class="detail">
					<text class="subject">考研英语</text>
					<text class="endTime">2024.02.04-2024.12.16</text>
					<text class="num">共54节</text>
				</view>
				<view class="bottom">
					<view class="teacher-list">
						<view class="teacher-info">
							<image class="avatar" src="@/static/wa.png"></image>
							<text>孙老师</text>
						</view>
						<view class="teacher-info">
							<image class="avatar" src="@/static/wa.png"></image>
							<text>孙老师</text>
						</view>
						<view class="teacher-info">
							<image class="avatar" src="@/static/wa.png"></image>
							<text>孙老师</text>
						</view>
					</view>
					<view class="money-right">
						<view class="money">
							￥3800.00
						</view>

					</view>

				</view>
			</view>

			<view class="phone-money">
				<view class="phone">下单姓名：183****2392</view>
				<view class="money">实际支付：<text>￥20000</text></view>
			</view>
		</view> -->
		<view class="list" @click="details(item.id)" v-for="item in allList" :key="item.id">
			<view class="time-top">
				<view class="time">下单时间：{{item.transTime}}</view>
				<view class="status">
					<view class="btn" v-if="item.status==='unredeemed'">扫码核销</view>
					<text>{{item.statusName}}</text>
				</view>
			</view>

			<view class="class-content">
				<view class="imageBox">
					<image v-for="(i,index) in item.orderProducts" :src="i.productCover" mode=""></image>
				</view>
				<view class="right">
					<view class="money">￥{{item.totalPrice}}</view>
					<view class="num">共{{item.totalNum}}件</view>
				</view>
			</view>
			<view class="phone-money">
				<view class="phone">下单手机号：{{item.studentPhone || ''}}</view>
				<view class="money">实际支付：<text>￥{{item.checkoutPrice}}</text></view>
			</view>
		</view>
		<!-- <view class="list">
			<view class="time-top">
				<view class="time">下单时间：2025.03.07 23:13</view>
				<view class="status">
					<view class="btn">扫码核销</view>
					<text>待核销</text>
				</view>
			</view>

			<view class="class-content">
				<image src="../../../static/head.png" mode=""></image>
				<view class="center">
					<view class="title">[26考研]政治牛蛙班</view>
					<view class="desc">好老师 好资料 好服务</view>
				</view>
				<view class="right">￥20000</view>
			</view>
			<view class="phone-money">
				<view class="phone">下单姓名：183****2392</view>
				<view class="money">实际支付：<text>￥20000</text></view>
			</view>
		</view> -->
		<uni-calendar ref="calendar" class="uni-calendar--hook" :clear-date="true" :date="info.date"
			:insert="info.insert" :lunar="info.lunar" :startDate="info.startDate" :endDate="info.endDate"
			@confirm="confirm" @close="close" />
	</view>
</template>

<script>
	import {
		mapState
	} from "vuex"
	import {
		orderList
	} from '@/api/comm.js'
	export default {
		data() {
			return {
				list: [{
					title: '全部',
					status: '',
				}, {
					title: '已支付',
					status: 'paid',
				}, {
					title: '待核销',
					status: 'unredeem',
				}, {
					title: '已退款',
					status: 'refuned',
				}],
				activeIndex: '',
				showCalendar: false,
				info: {
					lunar: true,
					range: true,
					insert: false,
					selected: []
				},
				allList: [],
				page: 1,
			};
		},
		onShow() {
			this.allList = []
			this.getList()

		},
		methods: {
			async getList() {
				const {
					data,
					errCode,
					msg
				} = await orderList({
					page: this.page,
					limit: 10,
					status: this.activeIndex
				})
				if (errCode == 0) {
					let arr = data.data.map(item => {
						let num = 0
						item.orderProducts.forEach(i => {
							num += i.quantity
						})
						return {
							...item,
							totalNum: num,
							studentPhone: item.studentPhone ? item.studentPhone.replace(/(\d{3})\d{4}(\d{4})/,
								'$1****$2') : ''
						}
					})
					this.allList = [...this.allList, ...arr]
				}
			},
			detail(id) {
				uni.navigateTo({
					url: `/pages/me_all/orderdetails/orderdetails?id=${id}`
				})
			},
			details(id) {

				uni.navigateTo({
					url: `/pages/me_all/order_detail/order_detail?id=${id}`
				})
			},
			meunClick(index) {
				this.activeIndex = index
				this.page = 1
				this.allList = []
				this.getList()
			},
			open() {
				this.$refs.calendar.open()
			},
			confirm(e) {
				console.log('confirm 返回:', e)
			},
			close() {

			}
		},
		onReachBottom() {
			this.page++
			this.getList()

		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background: #F6F7FB;
		min-height: 100vh;

		.tabs {
			height: 100rpx;
			background-color: #fff;
			width: 100%;
			font-weight: bold;
			font-size: 30rpx;
			color: #5A5A5A;
			display: flex;
			align-items: center;
			justify-content: space-around;
			border-bottom: 1rpx solid #f1f2f3;

			text {
				height: 100rpx;
				line-height: 100rpx;

			}

			.active {
				color: #00C2A0;
				border-bottom: 6rpx solid #00C2A0;
			}
		}

		.searchBox {
			padding: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			input {
				width: 612rpx;
				height: 74rpx;
				text-align: center;
				font-weight: bold;
				font-size: 26rpx;
				color: #989898;
				background: #FFFFFF;
				border-radius: 77rpx;
			}

			image {
				width: 60rpx;
				height: 60rpx;
			}
		}

		.list {
			width: 690rpx;
			// height: 448rpx;
			background: #FFFFFF;
			padding: 20rpx 0rpx;
			box-sizing: border-box;
			margin: 0 auto 25rpx;
			border-radius: 20rpx;

			.time-top {
				padding: 0 25rpx 20rpx;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1rpx solid #E5E5E5;

				.time {
					font-size: 24rpx;
					color: #A2A2A2;
				}

				.status {
					display: flex;
					align-items: center;

					.btn {
						width: 110rpx;
						height: 45rpx;
						line-height: 45rpx;
						text-align: center;
						font-size: 22rpx;
						color: #00C2A0;
						border-radius: 30rpx;
						margin-right: 10rpx;
						border: 1rpx solid #00C2A0;
					}

					text {
						font-size: 24rpx;
						color: #A2A2A2;
					}
				}
			}

			.content {
				padding: 20rpx 25rpx;
				box-sizing: border-box;
				border-bottom: 1rpx solid #E5E5E5;
				margin-bottom: 25rpx;

				.title {
					font-weight: bold;
					font-size: 26rpx;
					color: #060606;
					line-height: 1.4;
				}

				.detail {
					margin-top: 12rpx;
					display: flex;
					align-items: center;
					color: #A4A4A4;

					text {

						font-weight: 500;
						font-size: 24rpx;
						color: #A4A4A4;
					}

					.subject {
						font-weight: bold;
						font-size: 22rpx;
						color: #09CC8C;
						padding: 9rpx 7rpx;
						background: #EEFAF6;
						border-radius: 10rpx;
					}

					.endTime {
						margin: 0 12rpx;
						position: relative;

						&::after {
							content: '';
							position: absolute;
							width: 1rpx;
							height: 25rpx;
							top: 50%;
							transform: translateY(-50%);
							right: -12rpx;
							background-color: #A4A4A4;
						}
					}

					.num {
						margin-left: 12rpx;
					}
				}

				.bottom {
					margin-top: 20rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.teacher-list {
						padding-left: 10rpx;
						display: flex;
						align-items: center;
						justify-content: flex-start;
						flex-wrap: wrap;

						.teacher-info {
							margin-right: 15rpx;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							font-size: 22rpx;
							color: #818181;
							margin-bottom: 20rpx;

							&:last-child {
								margin-right: 0;
							}

							.avatar {
								width: 60rpx;
								height: 60rpx;
								border-radius: 100%;
								margin-bottom: 2rpx;
							}

						}
					}

					.money-right {
						display: flex;
						align-items: center;
						margin-right: 10rpx;

						.money {
							font-size: 30rpx;
							color: #4C5370;
							margin-right: 10rpx;

						}
					}

				}
			}

			.phone-money {
				padding: 0 25rpx;
				font-weight: 500;
				font-size: 24rpx;
				color: #A4A4A4;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.money {
					display: flex;
					align-items: center;

					text {
						font-weight: 500;
						font-size: 34rpx;
						color: #E62E2E;
					}
				}
			}

			.class-content {
				padding: 25rpx;
				box-sizing: border-box;
				border-bottom: 1rpx solid #E5E5E5;
				margin-bottom: 25rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.imageBox {
					flex: 3.4;
					display: flex;
					/* 启用Flex布局 */
					flex-wrap: nowrap;
					/* 禁止换行 */
					overflow-x: auto;

					image {
						flex: 0 0 auto;
						/* 禁止图片伸缩 */
						width: 156rpx;
						height: 116rpx;
						margin-right: 12rpx;

						&:last-child {
							margin-right: 0rpx;
						}
					}
				}


				.title {
					font-weight: bold;
					font-size: 28rpx;
					color: #414141;
				}

				.desc {
					font-weight: 400;
					font-size: 22rpx;
					color: #777777;
					margin-top: 10rpx;
				}

				.right {
					flex: 1;
					font-weight: 500;
					font-size: 30rpx;
					color: #4C5370;
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;

					.money {
						width: 100%;
						text-align: right;
					}

					.num {
						width: 100%;
						text-align: right;
						font-size: 20rpx;
					}
				}
			}
		}
	}
</style>