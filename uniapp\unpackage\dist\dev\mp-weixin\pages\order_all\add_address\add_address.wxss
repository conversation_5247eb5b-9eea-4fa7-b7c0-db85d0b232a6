
page {
	background: #F6F7FB;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-02831c15 {
  box-sizing: border-box;
  margin-top: 20rpx;
}
.container .content.data-v-02831c15 {
  width: 750rpx;
  padding: 50rpx 30rpx;
  height: 100vh;
  box-sizing: border-box;
  background-color: #fff;
}
.container .form.data-v-02831c15 {
  border-radius: 10rpx;
  padding-bottom: 20rpx;
  box-sizing: border-box;
}
.container .form-item-top.data-v-02831c15 {
  height: 230rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  border: 1rpx solid #26C8AC;
  margin-bottom: 30rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.container .form-item-top textarea.data-v-02831c15 {
  width: 100%;
  flex: 1;
  font-size: 30rpx;
  color: #989898;
}
.container .form-item-top .detailBtn.data-v-02831c15 {
  width: 150rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  font-weight: bold;
  font-size: 24rpx;
  color: #FFFFFF;
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  border-radius: 20rpx;
}
.container .form-item-box.data-v-02831c15 {
  display: flex;
  flex-direction: column;
  min-height: 106rpx;
  border-bottom: 1px solid #eee;
}
.container .form-item-box .form-item.data-v-02831c15 {
  min-height: 80rpx;
  border-bottom: none;
  margin-top: 20rpx;
}
.container .form-item-box .cursor.data-v-02831c15 {
  color: #f56c6c;
}
.container .form-item.data-v-02831c15 {
  display: flex;
  min-height: 95rpx;
  align-items: center;
  background-color: #fff;
  padding: 0 30rpx !important;
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  border: 1rpx solid #2FC293;
}
.container .form-item .right.data-v-02831c15 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .last-item.data-v-02831c15 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .label.data-v-02831c15 {
  min-width: 180rpx;
  font-size: 30rpx;
  color: #060606;
}
.container .label .red.data-v-02831c15 {
  color: #f56c6c;
}
.container .value.data-v-02831c15 {
  font-size: 30rpx;
  color: #777777;
  text-align: left;
  flex: 1;
  margin-right: 20rpx;
}
.container .value-input.data-v-02831c15 {
  flex: 1;
  padding: 10rpx;
  font-size: 30rpx;
  color: #777777;
  border: 0;
  border-radius: 8rpx;
  outline: none;
}
.container .value-input.data-v-02831c15:focus {
  border-color: #1BB394;
}
.container .form-item-step-three.data-v-02831c15 {
  height: 240rpx;
  background: #FFFFFF;
  border-radius: 17rpx;
  padding: 26rpx 30rpx;
  margin-bottom: 26rpx;
  display: flex;
  align-items: flex-start;
  border: 1rpx solid #2FC293;
}
.container .form-item-step-three .label.data-v-02831c15 {
  flex: 1;
  font-weight: bold;
  font-size: 30rpx;
  color: #504E4E;
}
.container .value-container.data-v-02831c15 {
  display: flex;
  align-items: center;
  width: 460rpx;
  margin-top: -10rpx;
}
.container .value-input.data-v-02831c15 {
  flex: 1;
  padding: 10rpx;
  font-size: 30rpx;
  color: #777777;
  border: 0;
  border-radius: 8rpx;
  outline: none;
}
.container .value-input.data-v-02831c15:focus {
  border-color: #1BB394;
}
.container.data-v-02831c15  .checkbox__inner {
  border-radius: 16rpx !important;
}
.container .toolbar.data-v-02831c15 {
  position: fixed;
  bottom: 50rpx;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
}
.container .toolbar .pay-btn.data-v-02831c15 {
  width: 510rpx;
  height: 92rpx;
  border-radius: 40rpx;
  line-height: 92rpx;
  text-align: center;
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  font-weight: bold;
  font-size: 30rpx;
  color: #FFFFFF;
  margin: 0 auto;
}

