@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-475f855d {
  padding-bottom: 20px;
}
.back.data-v-475f855d {
  position: absolute;
  top: 30rpx;
  left: 30rpx;
}
.header .header-img.data-v-475f855d {
  width: 100%;
  height: 1070rpx;
}
.header .teacher-info.data-v-475f855d {
  position: absolute;
  top: 396rpx;
  left: 520rpx;
  width: 200rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  color: #000;
}
.content.data-v-475f855d {
  padding-top: 28rpx;
  padding: 0 30rpx;
  /*爱好*/
  /* 升学规划*/
}
.content .base-info-item-container.data-v-475f855d {
  width: 100%;
  margin-top: 18rpx;
  font-size: 16rpx;
  display: flex;
  align-content: space-between;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.content .base-info-item-container .base-info-item.data-v-475f855d {
  width: 33%;
  display: flex;
  align-items: start !important;
  margin-right: 10rpx;
  justify-content: flex-start;
  /* 显示省略号 */
}
.content .base-info-item-container .base-info-item.data-v-475f855d  .uni-body {
  line-height: 0 !important;
}
.content .base-info-item-container .base-info-item .item-title.data-v-475f855d {
  font-weight: 400;
  font-size: 16rpx;
  color: #5A5A5A;
}
.content .base-info-item-container .base-info-item .item.data-v-475f855d {
  color: #5A5A5A;
}
.content .base-info-item-container .base-info-item.data-v-475f855d  .major {
  width: 80%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* 限制显示行数 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 25rpx !important;
  margin-top: 10rpx;
}
.content .base-info-item-container .base-info-item .wrapItem.data-v-475f855d {
  width: 80%;
  white-space: nowrap;
  overflow: hidden;
  /* 隐藏溢出内容 */
  text-overflow: ellipsis;
}
.content .base-info-item-container .width-25.data-v-475f855d {
  width: 28%;
}
.content .hobby.data-v-475f855d {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.content .hobby-item.data-v-475f855d {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border: 1rpx solid #A0E4C4;
  border-radius: 4rpx;
  padding: 8rpx 12rpx;
  margin-top: 18rpx;
}
.content .hobby-title.data-v-475f855d {
  font-weight: bold;
  font-size: 16rpx;
  color: #5A5A5A;
  width: 100rpx;
}
.content .hobby-info.data-v-475f855d {
  font-weight: 400;
  font-size: 16rpx;
  color: #5A5A5A;
  margin-left: 14rpx;
  width: 80%;
}
.content .exam-info.data-v-475f855d {
  margin-top: 20rpx;
}
.content .exam-info .table.data-v-475f855d {
  margin-top: 20rpx;
  height: 48rpx;
  border-radius: 8rpx;
  border: 1rpx solid #1BB394;
  font-size: 12rpx;
}
.content .exam-info .table .header.data-v-475f855d,
.content .exam-info .table .table-line.data-v-475f855d {
  height: 24rpx;
  border-bottom: 1rpx solid #1BB394;
  display: flex;
  align-items: flex-start;
  align-items: center;
}
.content .exam-info .table .header .title.data-v-475f855d,
.content .exam-info .table .header .table-line-item.data-v-475f855d,
.content .exam-info .table .table-line .title.data-v-475f855d,
.content .exam-info .table .table-line .table-line-item.data-v-475f855d {
  height: 24rpx;
  line-height: 24rpx;
  flex: 1;
  border-right: 1rpx solid #1BB394;
  text-align: center;
}
.content .exam-info .table .header .title.data-v-475f855d:last-child,
.content .exam-info .table .header .table-line-item.data-v-475f855d:last-child,
.content .exam-info .table .table-line .title.data-v-475f855d:last-child,
.content .exam-info .table .table-line .table-line-item.data-v-475f855d:last-child {
  border: 0;
}
.content .exam-info .table .table-line.data-v-475f855d {
  border: 0;
}
.content .university-info.data-v-475f855d {
  margin-top: 32rpx;
}
.content .university-info .university-tag.data-v-475f855d {
  margin-top: 18rpx;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.content .university-info .university-tag .logo.data-v-475f855d {
  width: 70rpx;
  height: 70rpx;
  margin-right: 24rpx;
}
.content .university-info .university-tag .tag.data-v-475f855d {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.content .university-info .university-tag .tag .name.data-v-475f855d {
  font-weight: bold;
  font-size: 20rpx;
  color: #5A5A5A;
  width: 100%;
  text-align: left;
}
.content .university-info .university-tag .tag .tag-list.data-v-475f855d {
  margin-top: 10rpx;
  width: 100%;
  display: flex;
  align-items: center;
}
.content .university-info .university-tag .tag .tag-list .tag-list-item.data-v-475f855d {
  width: 68rpx;
  height: 30rpx;
  background: #FFB975;
  border-radius: 6rpx;
  font-weight: 400;
  font-size: 14rpx;
  color: #FFFFFF;
  line-height: 30rpx;
  text-align: center;
  margin-right: 15rpx;
}
.content .plan.data-v-475f855d,
.content .ability.data-v-475f855d,
.content .university-plan.data-v-475f855d {
  margin-top: 32rpx;
}
.no-report.data-v-475f855d {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.no-report .no-data-img.data-v-475f855d {
  width: 360rpx;
  margin-bottom: 40rpx;
}
.no-report .no-data-tip.data-v-475f855d {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  gap: 44rpx;
}
.no-report .no-data-text.data-v-475f855d {
  font-size: 32rpx;
  color: #999;
  font-weight: 500;
}
/* 添加头部导航栏样式 */
.header.data-v-475f855d {
  background-color: #fff;
}
.header .nav-bar.data-v-475f855d {
  width: 100vw;
  height: 88rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  background-color: #1BB394;
  color: #fff;
}
.header .nav-bar .back-icon.data-v-475f855d {
  position: absolute;
  left: 30rpx;
}
.header .nav-bar .title.data-v-475f855d {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
}
.headers.data-v-475f855d {
  position: fixed;
  background-color: transparent !important;
}
.headers .nav-bar.data-v-475f855d {
  width: 100vw;
  height: 88rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  background-color: transparent !important;
  color: #888;
}
uni-button.data-v-475f855d:after {
  content: none !important;
  border: none !important;
}
.transparent-button.data-v-475f855d {
  position: absolute;
  top: 30rpx;
  left: 30rpx;
  z-index: 99999;
  padding: 0;
  margin: 0;
  background: transparent;
  border: none;
  outline: none;
}

