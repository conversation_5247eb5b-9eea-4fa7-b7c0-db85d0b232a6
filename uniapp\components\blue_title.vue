<template>
	<view class="container">
		<text>{{title}}</text>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			};
		},
		props: {
			title: {
				type: String,
				default: '标题'
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-image: url('@/static/blue-bg.png');
		width: 100%;
		height: 36rpx;
		background-size: 100% 100%;
		padding-left: 48rpx;
		font-weight: bold;
		font-size: $primary-font-size;
		line-height: 36rpx;
		text-align: left;
		color: #FFFFFF;
		background-repeat: no-repeat;
	}
</style>