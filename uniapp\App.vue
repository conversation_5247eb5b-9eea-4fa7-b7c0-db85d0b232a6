<script>
	import {
		auto_login,
		get_openid
	} from '@/api/user.js'
	import {
		userInfo,
		common
	} from "@/api/public.js"
	import {
		getAssets
	} from "@/api/comm.js"
	export default {
		onLaunch() {
			if (uni.getStorageSync('TOKEN')) return
			this.getUser()
		},
		onLoad() {

		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			async getUser() {
				//获取用户信息
				const loginInfo = await uni.login()
				let {
					errCode,
					msg,
					data
				} = await auto_login({
					code: loginInfo.code
				})

				if (errCode == 0) {
					uni.setStorageSync('TOKEN', data.token)

				}
			}

		},

	}
</script>

<style lang="scss">
	@import "uview-ui/index.scss";

	@import url("utils/default.scss");

	::v-deep .u-picker__view__column__item {
		padding-top: 10rpx;
		box-sizing: border-box;
	}

	image {
		width: 100%;
		height: 100%;
	}

	button::after {
		border: none;
	}

	button {
		background-color: transparent;
		padding-left: 0;
		padding-right: 0;
		line-height: inherit;
	}

	button {
		border-radius: 0;
	}
</style>