<view class="content data-v-57280228"><view class="header data-v-57280228"><view class="img_box data-v-57280228"><view class="img_box data-v-57280228"><u-swiper vue-id="8dd740cc-1" list="{{banner}}" indicator="{{true}}" keyName="image" indicatorMode="dot" height="455rpx" data-event-opts="{{[['^click',[['hadeswp']]]]}}" bind:click="__e" class="data-v-57280228" bind:__l="__l"></u-swiper></view><view class="data-v-57280228"></view><view class="user data-v-57280228"><view class="user_head_portrait data-v-57280228"><block wx:if="{{userall.avatar}}"><image src="{{userall.avatar}}" mode class="data-v-57280228"></image></block><block wx:else><image src="../../static/wa.png" mode class="data-v-57280228"></image></block></view><view data-event-opts="{{[['tap',[['routergo']]]]}}" class="user_2 data-v-57280228" bindtap="__e"><view class="user_2_1 data-v-57280228">{{''+(userall.nickname||'请登录')+''}}</view><view class="user_2_2 data-v-57280228">蝌蚪币:<text class="num data-v-57280228">{{''+(asset.point||0)+''}}</text><text class="left data-v-57280228">优惠券:<text class="num data-v-57280228">{{asset.couponCnt||0}}</text></text></view></view><view data-event-opts="{{[['tap',[['routergo',['/pages/me_all/codeqr/codeqr']]]]]}}" class="user_3 data-v-57280228" bindtap="__e"><view class="user_code data-v-57280228"><image src="/static/Project_drawing 23.png" mode class="data-v-57280228"></image></view><text class="data-v-57280228">会员码</text></view></view></view><view class="grid data-v-57280228"><block wx:for="{{gridList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['toTarget',['$0'],[[['gridList','id',item.id]]]]]]]}}" class="grid-item data-v-57280228" bindtap="__e"><image mode="widthFix" src="{{item.image}}" class="data-v-57280228"></image><text class="data-v-57280228">{{item.name}}</text></view></block></view></view><view class="scrollBox data-v-57280228"><scroll-view class="menu data-v-57280228" scroll-x="{{true}}"><block wx:for="{{menuItems}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['setActiveIndex',[index,'$0'],[[['menuItems','id',item.id,'id']]]]]]]}}" class="{{['data-v-57280228','menu-item',activeIndex===index?'active':'']}}" bindtap="__e">{{''+item.name+''}}</view></block></scroll-view></view><view class="productList data-v-57280228"><block wx:if="{{$root.g0>0}}"><view class="product data-v-57280228"><image class="da data-v-57280228" src="{{activity[0].image}}" alt></image><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['routergo',['/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id,1]]]]]}}" class="productItem data-v-57280228" bindtap="__e"><block wx:if="{{index%2!=0}}"><image class="head data-v-57280228" src="{{item.course_cover}}" alt></image><view class="bottom data-v-57280228"><view class="title data-v-57280228">{{item.name}}</view><view class="desc data-v-57280228">{{item.handout_type_name+" "+item.teaching_mode_name}}</view><view class="money data-v-57280228"><text class="data-v-57280228">{{"￥"+item.checkout_price}}</text><block wx:if="{{!userall.user}}"><view class="add data-v-57280228">添加</view></block></view></view></block></view></block></view></block><block wx:if="{{$root.g1>0}}"><view class="product data-v-57280228"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['routergo',['/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id,1]]]]]}}" class="productItem data-v-57280228" bindtap="__e"><block wx:if="{{index%2==0}}"><image class="head data-v-57280228" src="{{item.course_cover}}" alt></image><view class="bottom data-v-57280228"><view class="title data-v-57280228">{{item.name}}</view><view class="desc data-v-57280228">{{item.handout_type_name+" "+item.teaching_mode_name}}</view><view class="money data-v-57280228"><text class="data-v-57280228">{{"￥"+item.checkout_price}}</text><block wx:if="{{!userall.user}}"><view class="add data-v-57280228">添加</view></block></view></view></block></view></block></view></block></view><block wx:if="{{!$root.g2}}"><view class="noenList data-v-57280228">暂无产品内容</view></block><block wx:if="{{$root.g3>0}}"><uni-load-more vue-id="8dd740cc-2" contentText="{{contentText}}" status="{{loadMoreStatus}}" data-event-opts="{{[['^clickLoadMore',[['getMore']]]]}}" bind:clickLoadMore="__e" class="data-v-57280228" bind:__l="__l"></uni-load-more></block><login vue-id="8dd740cc-3" show="{{enter}}" data-event-opts="{{[['^closepage',[['closepage']]]]}}" bind:closepage="__e" class="data-v-57280228" bind:__l="__l"></login></view>