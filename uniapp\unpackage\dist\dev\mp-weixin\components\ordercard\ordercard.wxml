<view><block><block wx:for="{{content}}" wx:for-item="item" wx:for-index="index" wx:key="*this"><view class="box"><view class="box_left"><view data-event-opts="{{[['tap',[['routergo',['/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id]]]]]}}" class="box_left_img" bindtap="__e"><image src="{{item.course_cover}}" mode="aspectFill"></image></view></view><view class="box_right"><view><text data-event-opts="{{[['tap',[['routergo',['/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id]]]]]}}" class="box_right_title" bindtap="__e">{{''+item.name}}</text><view class="box_right_sell">好老师 好资料 好服务</view></view><view class="box_right_price"><view class="box_right_price_left">{{'¥'+item.checkout_price+''}}</view><block wx:if="{{!user.user}}"><view data-event-opts="{{[['tap',[['add_joinCar',['$0'],[[['content','',index]]]]]]]}}" class="box_right_price_right_1" bindtap="__e"><text>添加</text></view></block></view></view></view></block></block><block wx:if="{{enter}}"><login bind:loadpage="__e" bind:closepage="__e" vue-id="7d849678-1" data-event-opts="{{[['^loadpage',[['unloadpage']]],['^closepage',[['closepage']]]]}}" bind:__l="__l"></login></block></view>