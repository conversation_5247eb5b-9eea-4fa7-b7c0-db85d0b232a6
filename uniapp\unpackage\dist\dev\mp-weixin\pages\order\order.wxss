
page {
	background: #FFFFFF;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 定位失败悬浮提示样式 */
.location-error-toast.data-v-127632e4 {
  position: fixed;
  top: 200rpx;
  width: 690rpx;
  left: 30rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 15rpx 20rpx;
  box-sizing: border-box;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  z-index: 9999;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  -webkit-animation: fadeIn-data-v-127632e4 0.3s ease-in-out;
          animation: fadeIn-data-v-127632e4 0.3s ease-in-out;
}
@-webkit-keyframes fadeIn-data-v-127632e4 {
from {
    opacity: 0;
    top: 0;
}
to {
    opacity: 1;
    top: 20rpx;
}
}
@keyframes fadeIn-data-v-127632e4 {
from {
    opacity: 0;
    top: 0;
}
to {
    opacity: 1;
    top: 20rpx;
}
}
.error-text.data-v-127632e4 {
  font-size: 28rpx;
  margin-right: 15rpx;
}
.retry-btn.data-v-127632e4 {
  background-color: #00C2A0;
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
  margin: 0;
}
.movableArea.data-v-127632e4 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.movableView.data-v-127632e4 {
  pointer-events: auto;
}
.scrollBox.data-v-127632e4 {
  padding: 0rpx 30rpx;
  box-sizing: border-box;
  height: 80rpx;
  border-bottom: 1rpx solid #F4F4F4;
}
.scrollBox .menu.data-v-127632e4 {
  display: flex;
  align-items: center;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.scrollBox .menu.data-v-127632e4::-webkit-scrollbar {
  display: none;
}
.scrollBox .menu-item.data-v-127632e4 {
  display: inline-block;
  height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  color: #414141;
  margin-right: 30rpx;
  transition: all 0.3s ease;
}
.scrollBox .menu-item.data-v-127632e4:last-child {
  margin-right: 0;
}
.scrollBox .active.data-v-127632e4 {
  color: #00C2A0;
  font-size: 30rpx;
  font-weight: bold;
  position: relative;
}
.scrollBox .active.data-v-127632e4::after {
  content: '';
  position: absolute;
  z-index: 99;
  bottom: 20rpx;
  left: 0;
  width: 100%;
  height: 6rpx;
  border-radius: 50rpx;
  background-color: #00C2A0;
}
.search.data-v-127632e4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.search .search_left.data-v-127632e4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #05B6F6;
  padding-left: 30rpx;
}
.search .search_left image.data-v-127632e4 {
  width: 206rpx;
  height: 58rpx;
}
.search .search_right.data-v-127632e4 {
  width: 62rpx;
  height: 62rpx;
  padding: 0 10rpx;
}
.hade_location.data-v-127632e4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0 30rpx;
}
.hade_location .tag.data-v-127632e4 {
  height: 39rpx;
  border-radius: 4rpx;
  border: 1rpx solid #05B6F6;
  font-size: 24rpx;
  font-weight: 400;
  color: #05B6F6;
  text-align: center;
  box-sizing: border-box;
  padding: 0 10rpx;
  margin-right: 10rpx;
  line-height: 37rpx;
}
.hade_location .hade_location_left.data-v-127632e4 {
  margin-left: 20rpx;
}
.hade_location .hade_location_left .hade_location_left_top.data-v-127632e4 {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #313131;
}
.hade_location .hade_location_left .hade_location_left_top text.data-v-127632e4 {
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.hade_location .hade_location_left .hade_location_left_top .schoolName.data-v-127632e4 {
  height: 45rpx;
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 22rpx;
  color: #BE5C2D;
  padding: 5rpx 10rpx 5rpx 5rpx;
  background: #FFF9E2;
  border-radius: 12rpx;
}
.hade_location .hade_location_left .hade_location_left_top .schoolName image.data-v-127632e4 {
  width: 36rpx;
  height: 30rpx;
  margin-right: 5rpx;
}
.hade_location .hade_location_left .hade_location_left_down.data-v-127632e4 {
  font-size: 24rpx;
  font-weight: 400;
  color: #666A6B;
  margin-top: 5rpx;
}
.hade_location .hade_location_right.data-v-127632e4 {
  height: 63rpx;
  background: #F4F4F4;
  border-radius: 32rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.hade_location .hade_location_right .hade_location_right_content.data-v-127632e4 {
  width: 90rpx;
  height: 63rpx;
  text-align: center;
  line-height: 63rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #949494;
}
.hade_location .hade_location_right .hade_location_right_content_activate.data-v-127632e4 {
  background-color: #05B6F6;
  color: #FDFEFF;
  border-radius: 32rpx;
}
.felx.data-v-127632e4 {
  display: flex;
  justify-content: space-between;
}
.felx .felx_right_box.data-v-127632e4 {
  width: 571rpx;
  height: 67vh;
}
.felx .felx_right_box .right_title.data-v-127632e4 {
  padding: 20rpx 20rpx 0 20rpx;
  display: flex;
  flex-wrap: wrap;
}
.felx .felx_right_box .right_title .title.data-v-127632e4 {
  width: 162rpx;
  height: 62rpx;
  line-height: 62rpx;
  text-align: center;
  font-size: 26rpx;
  color: #414141;
  background: #F6F7FB;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.felx .felx_right_box .right_title .title.data-v-127632e4:nth-child(3n+3) {
  margin-right: 0;
}
.felx .felx_right_box .right_title .title_active.data-v-127632e4 {
  font-weight: bold;
  color: #00C2A0;
  background: #DCF6ED;
}
.felx .felx_right_box .felx_right_box_conten.data-v-127632e4 {
  width: 522rpx;
  margin: 0 auto;
}
.felx .felx_right_box .felx_right_box_conten .felx_right_box_conten_img.data-v-127632e4 {
  width: 525rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.felx .felx_right_box .felx_right_box_conten .felx_right_box_conten_title.data-v-127632e4 {
  font-size: 26rpx;
  font-weight: 600;
  color: #353535;
  padding: 30rpx 0 30rpx 10rpx;
}
.specification.data-v-127632e4 {
  width: 688rpx;
  background: #FFFFFF;
  border-radius: 12rpx;
  position: relative;
  z-index: 10074;
}
.specification .specification_title.data-v-127632e4 {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #353535;
  margin: 0 auto;
}
.specification .specification_title_1.data-v-127632e4 {
  width: 95%;
  height: 504rpx;
  margin: 0 auto;
}
.specification .specification_title_1 .specification_title_1_title.data-v-127632e4 {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #676767;
  display: inline-block;
  padding: 20rpx 0;
}
.specification .specification_title_1 .specification_title_1_content.data-v-127632e4 {
  display: flex;
  overflow-x: auto;
  display: -webkit-box;
  -webkit-overflow-scrolling: touch;
}
.specification .specification_title_1 .specification_title_1_content .specification_title_1_content_flex_activate.data-v-127632e4 {
  background: #F4FCFF !important;
  border: 1rpx solid #00B8FB !important;
  color: #00B8FB !important;
}
.specification .specification_title_1 .specification_title_1_content .font_sizi_1.data-v-127632e4 {
  color: #00B8FB;
}
.specification .specification_title_1 .specification_title_1_content .font_sizi_2.data-v-127632e4 {
  border-left: 1rpx solid #00B8FB;
}
.specification .specification_title_1 .specification_title_1_content .specification_title_1_content_flex.data-v-127632e4 {
  height: 63rpx;
  background: #FFFFFF;
  border-radius: 14rpx;
  border: 1rpx solid #F1F1F1;
  margin-right: 20rpx;
  text-align: center;
  line-height: 63rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #343434;
  padding: 0 40rpx;
}
.specification .close.data-v-127632e4 {
  position: absolute;
  bottom: -150rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.selected.data-v-127632e4 {
  width: 688rpx;
  padding: 20rpx 0;
  background: #F5F5F5;
  margin-top: 60rpx;
}
.selected text.data-v-127632e4 {
  font-size: 24rpx;
  font-weight: 400;
  color: #363636;
}
.selected text.data-v-127632e4:nth-child(1) {
  font-size: 24rpx;
  font-weight: 400;
  color: #676767;
  padding: 0 20rpx;
  margin-left: 10rpx;
}
.sublist.data-v-127632e4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
}
.sublist .sublist_left text.data-v-127632e4:nth-child(1) {
  font-size: 28rpx;
  font-weight: 600;
  color: #363636;
}
.sublist .sublist_left text.data-v-127632e4:nth-child(2) {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF4000;
}
.sublist .sublist_left text:nth-child(2) text.data-v-127632e4:nth-child(1) {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF4000;
  font-size: 24rpx;
}
.sublist .sublist_right.data-v-127632e4 {
  width: 234rpx;
  height: 62rpx;
  background: #02B6FD;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #F3FCFF;
  line-height: 62rpx;
  text-align: center;
}
.coupn .coupn_icon.data-v-127632e4 {
  width: 24rpx;
  height: 24rpx;
  margin-left: 100rpx;
  margin-bottom: 10rpx;
  opacity: 0.5;
}
.coupn .coupn_img.data-v-127632e4 {
  width: 130rpx;
  height: 121rpx;
}
.coupn .coupn_title.data-v-127632e4 {
  width: 120rpx;
  padding: 5rpx 10rpx;
  background: #FEDD5B;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  position: relative;
  bottom: 30rpx;
}
.coupn .coupn_title .coupn_title_text.data-v-127632e4 {
  font-size: 22rpx;
  font-weight: 400;
  color: #631407;
  margin-left: 10rpx;
}

