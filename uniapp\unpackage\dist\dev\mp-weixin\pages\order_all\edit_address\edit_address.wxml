<view class="container data-v-2b1f1b07"><view class="content data-v-2b1f1b07"><view class="form data-v-2b1f1b07"><view class="form-item data-v-2b1f1b07"><text class="label data-v-2b1f1b07"><label class="red _span data-v-2b1f1b07">*</label>收货人</text><input class="value data-v-2b1f1b07" style="text-align:left;" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',['trim']],['stuInfo']]]],['blur',[['$forceUpdate']]]]}}" value="{{stuInfo.name}}" bindinput="__e" bindblur="__e"/></view><view class="form-item data-v-2b1f1b07"><text class="label data-v-2b1f1b07"><label class="red _span data-v-2b1f1b07">*</label>联系电话</text><input class="value data-v-2b1f1b07" style="text-align:left;" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',['trim']],['stuInfo']]]],['blur',[['$forceUpdate']]]]}}" value="{{stuInfo.phone}}" bindinput="__e" bindblur="__e"/></view><view class="form-item data-v-2b1f1b07"><text class="label data-v-2b1f1b07"><label class="red _span data-v-2b1f1b07">*</label>所在地区</text><view class="right selectCity data-v-2b1f1b07"><select-city vue-id="38c4c6df-1" type="{{type}}" districtCode="{{stuInfo.pcaCode}}" data-event-opts="{{[['^confirm',[['handleConfirm']]]]}}" bind:confirm="__e" class="data-v-2b1f1b07" bind:__l="__l"></select-city><uni-icons class="icon-right data-v-2b1f1b07" vue-id="38c4c6df-2" type="right" size="20" color="#C3C3C3" bind:__l="__l"></uni-icons></view></view><view class="form-item-step-three data-v-2b1f1b07"><text class="label data-v-2b1f1b07"><label class="red _span data-v-2b1f1b07">*</label>详细地址：</text><view class="value-container data-v-2b1f1b07"><textarea class="value-input data-v-2b1f1b07" maxlength="100" placeholder="请输入街道门牌信息" data-event-opts="{{[['input',[['__set_model',['$0','detailPlace','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.detailPlace}}" bindinput="__e"></textarea></view></view><view class="form-item last-item data-v-2b1f1b07"><text class="data-v-2b1f1b07">设为默认地址</text><u-switch bind:input="__e" vue-id="38c4c6df-3" activeColor="#22c8a3" size="40" value="{{stuInfo.isDefault}}" data-event-opts="{{[['^input',[['__set_model',['$0','isDefault','$event',[]],['stuInfo']]]]]}}" class="data-v-2b1f1b07" bind:__l="__l"></u-switch></view></view><view class="toolbar data-v-2b1f1b07"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="pay-btn data-v-2b1f1b07" bindtap="__e">提交</view></view></view></view>