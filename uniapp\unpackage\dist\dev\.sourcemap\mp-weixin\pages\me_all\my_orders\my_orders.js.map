{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/my_orders/my_orders.vue?e067", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/my_orders/my_orders.vue?8cbe", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/my_orders/my_orders.vue?5329", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/my_orders/my_orders.vue?e450", "uni-app:///pages/me_all/my_orders/my_orders.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/my_orders/my_orders.vue?69e7", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/my_orders/my_orders.vue?3d2f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "list", "title", "status", "activeIndex", "showCalendar", "info", "lunar", "range", "insert", "selected", "allList", "page", "onShow", "methods", "getList", "limit", "errCode", "msg", "arr", "item", "num", "totalNum", "studentPhone", "detail", "uni", "url", "details", "meunClick", "open", "confirm", "console", "close", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC2GvnB;AAGA;AAEA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EAEA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA;kBACAH;kBACAI;kBACAb;gBACA;cAAA;gBAAA;gBAPAH;gBACAiB;gBACAC;gBAMA;kBACAC;oBACA;oBACAC;sBACAC;oBACA;oBACA,uCACAD;sBACAE;sBACAC,mFACA;oBAAA;kBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MAEAF;QACAC;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAC;IACA;IACAC,yBAEA;EACA;EACAC;IACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC7MA;AAAA;AAAA;AAAA;AAAsqC,CAAgB,4oCAAG,EAAC,C;;;;;;;;;;;ACA1rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/me_all/my_orders/my_orders.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/me_all/my_orders/my_orders.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my_orders.vue?vue&type=template&id=57961e85&scoped=true&\"\nvar renderjs\nimport script from \"./my_orders.vue?vue&type=script&lang=js&\"\nexport * from \"./my_orders.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my_orders.vue?vue&type=style&index=0&id=57961e85&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57961e85\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/me_all/my_orders/my_orders.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my_orders.vue?vue&type=template&id=57961e85&scoped=true&\"", "var components\ntry {\n  components = {\n    uniCalendar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-calendar/uni-calendar\" */ \"@dcloudio/uni-ui/lib/uni-calendar/uni-calendar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my_orders.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my_orders.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"tabs\">\r\n\t\t\t<text :class=\"activeIndex==item.status ? 'active' : ''\" v-for=\"(item,index) in list\" :key=\"index\"\r\n\t\t\t\t@click=\"meunClick(item.status)\">{{item.title}}</text>\r\n\t\t</view>\r\n\t\t<!-- <view class=\"searchBox\">\r\n\t\t\t<input type=\"text\" placeholder=\"搜索手机号\" />\r\n\t\t\t<image src=\"../../../static/me/icon20.png\" mode=\"\" @click=\"open\"></image>\r\n\t\t</view> -->\r\n\t\t<!-- <view class=\"list\" @click=\"detail\" v-for=\"item in allList\">\r\n\t\t\t<view class=\"time-top\">\r\n\t\t\t\t<view class=\"time\">下单时间：2025.03.07 23:13</view>\r\n\t\t\t\t<view class=\"status\">\r\n\t\t\t\t\t<view class=\"btn\">扫码核销</view>\r\n\t\t\t\t\t<text>待核销</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view class=\"title\">联报[25考研英语&数学]寒假线上集训营[25考研英语&数学]寒假线上集训营</view>\r\n\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t<text class=\"subject\">考研英语</text>\r\n\t\t\t\t\t<text class=\"endTime\">2024.02.04-2024.12.16</text>\r\n\t\t\t\t\t<text class=\"num\">共54节</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bottom\">\r\n\t\t\t\t\t<view class=\"teacher-list\">\r\n\t\t\t\t\t\t<view class=\"teacher-info\">\r\n\t\t\t\t\t\t\t<image class=\"avatar\" src=\"@/static/wa.png\"></image>\r\n\t\t\t\t\t\t\t<text>孙老师</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"teacher-info\">\r\n\t\t\t\t\t\t\t<image class=\"avatar\" src=\"@/static/wa.png\"></image>\r\n\t\t\t\t\t\t\t<text>孙老师</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"teacher-info\">\r\n\t\t\t\t\t\t\t<image class=\"avatar\" src=\"@/static/wa.png\"></image>\r\n\t\t\t\t\t\t\t<text>孙老师</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"money-right\">\r\n\t\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t\t￥3800.00\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"phone-money\">\r\n\t\t\t\t<view class=\"phone\">下单姓名：183****2392</view>\r\n\t\t\t\t<view class=\"money\">实际支付：<text>￥20000</text></view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t<view class=\"list\" @click=\"details(item.id)\" v-for=\"item in allList\" :key=\"item.id\">\r\n\t\t\t<view class=\"time-top\">\r\n\t\t\t\t<view class=\"time\">下单时间：{{item.transTime}}</view>\r\n\t\t\t\t<view class=\"status\">\r\n\t\t\t\t\t<view class=\"btn\" v-if=\"item.status==='unredeemed'\">扫码核销</view>\r\n\t\t\t\t\t<text>{{item.statusName}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"class-content\">\r\n\t\t\t\t<view class=\"imageBox\">\r\n\t\t\t\t\t<image v-for=\"(i,index) in item.orderProducts\" :src=\"i.productCover\" mode=\"\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t<view class=\"money\">￥{{item.totalPrice}}</view>\r\n\t\t\t\t\t<view class=\"num\">共{{item.totalNum}}件</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"phone-money\">\r\n\t\t\t\t<view class=\"phone\">下单手机号：{{item.studentPhone || ''}}</view>\r\n\t\t\t\t<view class=\"money\">实际支付：<text>￥{{item.checkoutPrice}}</text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- <view class=\"list\">\r\n\t\t\t<view class=\"time-top\">\r\n\t\t\t\t<view class=\"time\">下单时间：2025.03.07 23:13</view>\r\n\t\t\t\t<view class=\"status\">\r\n\t\t\t\t\t<view class=\"btn\">扫码核销</view>\r\n\t\t\t\t\t<text>待核销</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"class-content\">\r\n\t\t\t\t<image src=\"../../../static/head.png\" mode=\"\"></image>\r\n\t\t\t\t<view class=\"center\">\r\n\t\t\t\t\t<view class=\"title\">[26考研]政治牛蛙班</view>\r\n\t\t\t\t\t<view class=\"desc\">好老师 好资料 好服务</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"right\">￥20000</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"phone-money\">\r\n\t\t\t\t<view class=\"phone\">下单姓名：183****2392</view>\r\n\t\t\t\t<view class=\"money\">实际支付：<text>￥20000</text></view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t<uni-calendar ref=\"calendar\" class=\"uni-calendar--hook\" :clear-date=\"true\" :date=\"info.date\"\r\n\t\t\t:insert=\"info.insert\" :lunar=\"info.lunar\" :startDate=\"info.startDate\" :endDate=\"info.endDate\"\r\n\t\t\t@confirm=\"confirm\" @close=\"close\" />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState\r\n\t} from \"vuex\"\r\n\timport {\r\n\t\torderList\r\n\t} from '@/api/comm.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlist: [{\r\n\t\t\t\t\ttitle: '全部',\r\n\t\t\t\t\tstatus: '',\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '已支付',\r\n\t\t\t\t\tstatus: 'paid',\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '待核销',\r\n\t\t\t\t\tstatus: 'unredeem',\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttitle: '已退款',\r\n\t\t\t\t\tstatus: 'refuned',\r\n\t\t\t\t}],\r\n\t\t\t\tactiveIndex: '',\r\n\t\t\t\tshowCalendar: false,\r\n\t\t\t\tinfo: {\r\n\t\t\t\t\tlunar: true,\r\n\t\t\t\t\trange: true,\r\n\t\t\t\t\tinsert: false,\r\n\t\t\t\t\tselected: []\r\n\t\t\t\t},\r\n\t\t\t\tallList: [],\r\n\t\t\t\tpage: 1,\r\n\t\t\t};\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.allList = []\r\n\t\t\tthis.getList()\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync getList() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await orderList({\r\n\t\t\t\t\tpage: this.page,\r\n\t\t\t\t\tlimit: 10,\r\n\t\t\t\t\tstatus: this.activeIndex\r\n\t\t\t\t})\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tlet arr = data.data.map(item => {\r\n\t\t\t\t\t\tlet num = 0\r\n\t\t\t\t\t\titem.orderProducts.forEach(i => {\r\n\t\t\t\t\t\t\tnum += i.quantity\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\ttotalNum: num,\r\n\t\t\t\t\t\t\tstudentPhone: item.studentPhone ? item.studentPhone.replace(/(\\d{3})\\d{4}(\\d{4})/,\r\n\t\t\t\t\t\t\t\t'$1****$2') : ''\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.allList = [...this.allList, ...arr]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdetail(id) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/me_all/orderdetails/orderdetails?id=${id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tdetails(id) {\r\n\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/me_all/order_detail/order_detail?id=${id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tmeunClick(index) {\r\n\t\t\t\tthis.activeIndex = index\r\n\t\t\t\tthis.page = 1\r\n\t\t\t\tthis.allList = []\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\topen() {\r\n\t\t\t\tthis.$refs.calendar.open()\r\n\t\t\t},\r\n\t\t\tconfirm(e) {\r\n\t\t\t\tconsole.log('confirm 返回:', e)\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tthis.page++\r\n\t\t\tthis.getList()\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t\tbackground: #F6F7FB;\r\n\t\tmin-height: 100vh;\r\n\r\n\t\t.tabs {\r\n\t\t\theight: 100rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\twidth: 100%;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #5A5A5A;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\tborder-bottom: 1rpx solid #f1f2f3;\r\n\r\n\t\t\ttext {\r\n\t\t\t\theight: 100rpx;\r\n\t\t\t\tline-height: 100rpx;\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.active {\r\n\t\t\t\tcolor: #00C2A0;\r\n\t\t\t\tborder-bottom: 6rpx solid #00C2A0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.searchBox {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\r\n\t\t\tinput {\r\n\t\t\t\twidth: 612rpx;\r\n\t\t\t\theight: 74rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #989898;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 77rpx;\r\n\t\t\t}\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.list {\r\n\t\t\twidth: 690rpx;\r\n\t\t\t// height: 448rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tpadding: 20rpx 0rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tmargin: 0 auto 25rpx;\r\n\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t.time-top {\r\n\t\t\t\tpadding: 0 25rpx 20rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tborder-bottom: 1rpx solid #E5E5E5;\r\n\r\n\t\t\t\t.time {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #A2A2A2;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.status {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t.btn {\r\n\t\t\t\t\t\twidth: 110rpx;\r\n\t\t\t\t\t\theight: 45rpx;\r\n\t\t\t\t\t\tline-height: 45rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #00C2A0;\r\n\t\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\tborder: 1rpx solid #00C2A0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #A2A2A2;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.content {\r\n\t\t\t\tpadding: 20rpx 25rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tborder-bottom: 1rpx solid #E5E5E5;\r\n\t\t\t\tmargin-bottom: 25rpx;\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #060606;\r\n\t\t\t\t\tline-height: 1.4;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.detail {\r\n\t\t\t\t\tmargin-top: 12rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tcolor: #A4A4A4;\r\n\r\n\t\t\t\t\ttext {\r\n\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #A4A4A4;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.subject {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #09CC8C;\r\n\t\t\t\t\t\tpadding: 9rpx 7rpx;\r\n\t\t\t\t\t\tbackground: #EEFAF6;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.endTime {\r\n\t\t\t\t\t\tmargin: 0 12rpx;\r\n\t\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\twidth: 1rpx;\r\n\t\t\t\t\t\t\theight: 25rpx;\r\n\t\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\t\t\tright: -12rpx;\r\n\t\t\t\t\t\t\tbackground-color: #A4A4A4;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.num {\r\n\t\t\t\t\t\tmargin-left: 12rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bottom {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.teacher-list {\r\n\t\t\t\t\t\tpadding-left: 10rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\t\t\tflex-wrap: wrap;\r\n\r\n\t\t\t\t\t\t.teacher-info {\r\n\t\t\t\t\t\t\tmargin-right: 15rpx;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\tcolor: #818181;\r\n\t\t\t\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.avatar {\r\n\t\t\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\t\t\t\tborder-radius: 100%;\r\n\t\t\t\t\t\t\t\tmargin-bottom: 2rpx;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.money-right {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\r\n\t\t\t\t\t\t.money {\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: #4C5370;\r\n\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.phone-money {\r\n\t\t\t\tpadding: 0 25rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #A4A4A4;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.money {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\tcolor: #E62E2E;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.class-content {\r\n\t\t\t\tpadding: 25rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tborder-bottom: 1rpx solid #E5E5E5;\r\n\t\t\t\tmargin-bottom: 25rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.imageBox {\r\n\t\t\t\t\tflex: 3.4;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t/* 启用Flex布局 */\r\n\t\t\t\t\tflex-wrap: nowrap;\r\n\t\t\t\t\t/* 禁止换行 */\r\n\t\t\t\t\toverflow-x: auto;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\tflex: 0 0 auto;\r\n\t\t\t\t\t\t/* 禁止图片伸缩 */\r\n\t\t\t\t\t\twidth: 156rpx;\r\n\t\t\t\t\t\theight: 116rpx;\r\n\t\t\t\t\t\tmargin-right: 12rpx;\r\n\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tmargin-right: 0rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #414141;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.desc {\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tcolor: #777777;\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.right {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #4C5370;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tjustify-content: space-evenly;\r\n\r\n\t\t\t\t\t.money {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.num {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my_orders.vue?vue&type=style&index=0&id=57961e85&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my_orders.vue?vue&type=style&index=0&id=57961e85&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557561979\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}