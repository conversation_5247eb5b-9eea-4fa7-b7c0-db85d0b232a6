<view data-event-opts="{{[['tap',[['wrapperClickHandler',['$event']]]]]}}" class="{{['u-radio','data-v-643b3322','u-radio-label--'+parentData.iconPlacement,parentData.borderBottom&&parentData.placement==='column'&&'u-border-bottom']}}" style="{{$root.s0}}" catchtap="__e"><view data-event-opts="{{[['tap',[['iconClickHandler',['$event']]]]]}}" class="{{['u-radio__icon-wrap','data-v-643b3322',iconClasses]}}" style="{{$root.s1}}" catchtap="__e"><block wx:if="{{$slots.icon}}"><slot name="icon"></slot></block><block wx:else><u-icon class="u-radio__icon-wrap__icon data-v-643b3322" vue-id="058ad445-1" name="checkbox-mark" size="{{elIconSize}}" color="{{elIconColor}}" bind:__l="__l"></u-icon></block></view><text data-event-opts="{{[['tap',[['labelClickHandler',['$event']]]]]}}" class="u-radio__text data-v-643b3322" style="{{'color:'+(elDisabled?elInactiveColor:elLabelColor)+';'+('font-size:'+(elLabelSize)+';')+('line-height:'+(elLabelSize)+';')}}" catchtap="__e">{{label}}</text></view>