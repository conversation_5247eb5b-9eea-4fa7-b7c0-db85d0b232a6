<template>
	<!-- 商品卡牌 -->
	<view>
		<block>
			<view class="box" v-for="(item,index) in content" :key="item">
				<view class="box_left">
					<view class="box_left_img"
						@tap="routergo('/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id)">
						<image :src="item.course_cover" mode="aspectFill"></image>
						<!-- <view class="box_left_img_text" v-if="item.tags"> {{item.tags}}</view> -->
					</view>
				</view>
				<view class="box_right">
					<view class="">
						<text class="box_right_title"
							@tap="routergo('/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id)">
							{{item.name}}</text>
						<!-- <view class="box_right_ranking" v-if="item.xl_desc">
						好老师 好资料 好服务
					</view> -->
						<view class="box_right_sell">
							好老师 好资料 好服务
						</view>
					</view>
					<view class="box_right_price">
						<view class="box_right_price_left">
							¥{{item.checkout_price}}
						</view>
						<!-- <view v-if="item.sku_count>0" class="box_right_price_right" @tap="stop(item.id,item.type)">
							<text> 选规格</text>
						</view> -->
						<view @click="add_joinCar(item)" class="box_right_price_right_1" v-if="!user.user">
							<text>添加</text>
						</view>

					</view>
				</view>
			</view>
		</block>
		<login v-if="enter" @loadpage="unloadpage" @closepage='closepage'></login>
	</view>
</template>

<script>
	import {
		order_joinCar,
	} from "@/api/comm.js"
	export default {
		name: "ordercard",
		props: {
			content: {
				type: Array,
				default: [],
				required: true
			},
			store_id: {
				type: String,
				default: ' ',
				required: true
			},
			activate_data: {
				type: Number,
				default: '',
				required: true
			},
		},
		data() {
			return {
				enter: '',
				cartItems: [], // 存储购物车商品信息
				user: uni.getStorageSync('user') || {}
			};
		},
		onShow() {
			// 页面显示时更新购物车信息
			this.updateCartInfo();
		},
		methods: {
			updateCartInfo() {
				const cartData = uni.getStorageSync('cartItems');
				if (cartData) {
					this.cartItems = cartData;
				} else {
					this.cartItems = [];
				}
			},

			// 添加购物车
			async add_joinCar(val) {
				if (!uni.getStorageSync('TOKEN')) {
					this.$emit('login')
					return
				}
				this.updateCartInfo()
				const existingItemIndex = this.cartItems.findIndex(cartItem => cartItem.id === val.id);

				if (existingItemIndex !== -1) {
					// 商品已存在，增加数量
					this.cartItems[existingItemIndex].quantity += this.cartItems[existingItemIndex].min_quantity
				} else {
					// 商品不存在，添加新商品
					const newItem = {
						...val,
						quantity: val.min_quantity
					};
					this.cartItems.push(newItem);
					// this.cartCount++;
				}
				uni.showToast({
					title: '加入成功',
					icon: "success"
				})
				// 存储到本地
				uni.setStorageSync('cartItems', this.cartItems);
				console.log(this.cartItems)
				this.$emit('addCar')
			},

			routergo(url) {
				uni.navigateTo({
					url: url
				})
			},
			stop(e, type) {
				let obj = {
					e,
					type
				}
				this.$emit('specification', obj)
			},
			//未登录关闭弹出层需要关掉组件
			closepage() {
				this.enter = false
			},
		}
	}
</script>

<style lang="scss">
	.box {
		padding: 30rpx 0;
		display: flex;
		border-bottom: 1rpx solid #E6E6E6;

		.box_left {
			.box_left_img {
				width: 220rpx;
				height: 164rpx;
				position: relative;

				image {
					border-radius: 11rpx;
				}

				.box_left_img_text {
					width: 63rpx;
					height: 36rpx;
					background: #05B6F6;
					border-radius: 8rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #FDFEFF;
					line-height: 36rpx;
					text-align: center;
					padding: 3rpx 5rpx;
					position: absolute;
					top: -10rpx;
					right: -5rpx;
				}
			}
		}

		.box_right {
			width: 300rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			margin-left: 15rpx;

			.box_right_title {
				font-weight: bold;
				font-size: 28rpx;
				color: #414141;
				display: inline-block;
				margin-bottom: 15rpx;
			}

			.box_right_ranking {
				width: 182rpx;
				height: 34rpx;
				background: #ECF3FF;
				border-radius: 6rpx;
				line-height: 34rpx;
				text-align: center;
				font-size: 24rpx;
				font-weight: 400;
				color: #05B6F6;
				margin-bottom: 20rpx;
			}

			.box_right_sell {
				font-weight: 400;
				font-size: 22rpx;
				color: #777777;
				margin-bottom: 20rpx;
			}

			.box_right_price {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.box_right_price_left {
					font-weight: 500;
					font-size: 30rpx;
					color: #FB4E44;
				}

				.box_right_price_right_1 {
					width: 92rpx;
					height: 45rpx;
					background: #00C2A0;
					border-radius: 30rpx;
					font-size: 24rpx;
					font-weight: bold;
					color: #FFFFFF;
					line-height: 45rpx;
					text-align: center;
					position: relative;
				}

				.box_right_price_right {
					width: 101rpx;
					height: 47rpx;
					background: #05B6F6;
					border-radius: 17rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #FDFEFF;
					line-height: 47rpx;
					text-align: center;
					position: relative;

					.box_right_price_right_count {
						width: 30rpx;
						height: 30rpx;
						background: #F65329;
						border-radius: 50%;
						text-align: center;
						line-height: 30rpx;
						font-size: 24rpx;
						font-weight: 600;
						color: #FFF8F6;
						position: absolute;
						top: -10rpx;
						right: -10rpx;
					}

				}
			}
		}
	}
</style>