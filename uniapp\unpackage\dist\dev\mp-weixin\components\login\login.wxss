@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uicon-close {
  font-size: 50rpx;
}
.user_box {
  width: 600rpx;
  height: 700rpx;
  text-align: center;
  padding: 30rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.user_box .user_box_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 34rpx;
  font-weight: 600;
}
.user_box .user_avatar image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
}
.user_box .user_ipt {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
  color: #3d3d3d;
  padding: 20rpx 0;
  box-sizing: border-box;
  border-bottom: 1rpx #eee solid;
}
.user_box .user_ipt input {
  width: 400rpx;
  text-align: left;
}
.user_box .user_tag {
  background-color: #f3f3ff;
  padding: 24rpx 30rpx;
  box-sizing: border-box;
  font-size: 24rpx;
  color: #848484;
  text-align: left;
  border-radius: 16rpx;
}
.user_box .user_btn {
  background-color: #05B6F6;
  height: 80rpx;
  width: 100%;
  font-size: 28rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 80rpx;
  border-radius: 50rpx;
}
.login {
  height: 1000rpx;
  background: #FFFFFF;
  z-index: 9999999;
}
.login .close {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  z-index: 99999991;
}
.login .hadr {
  padding: 30rpx 40rpx;
}
.login .hadr view:nth-child(1) {
  font-size: 32rpx;
  font-weight: 600;
  color: #0D0D0D;
}
.login .hadr view:nth-child(2) {
  font-size: 24rpx;
  font-weight: 500;
  color: #9A9A9A;
  padding: 20rpx 0;
}
.login .input {
  padding: 0 40rpx;
}
.login .input .input_1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #CCCCCC;
  padding: 30rpx 0;
}
.login .input .input_1 .input_1_left {
  font-size: 32rpx;
  font-weight: 500;
  color: #0D0D0D;
}
.login .input .input_1 input {
  width: 300rpx;
  font-size: 32rpx;
  font-weight: 400;
  color: #9A9A9A;
}
.login .input .input_1 .input_1_right {
  width: 162rpx;
  height: 57rpx;
  background: #95DBF5;
  border-radius: 29rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 57rpx;
  text-align: center;
}
.login .input .input_2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #CCCCCC;
  padding: 30rpx 0;
}
.login .input .input_2 .input_2_left {
  font-size: 32rpx;
  font-weight: 500;
  color: #0D0D0D;
}
.login .input .input_2 input {
  width: 300rpx;
  font-size: 32rpx;
  font-weight: 400;
  color: #9A9A9A;
}
.login .input .input_2 .input_2_right {
  width: 162rpx;
  height: 57rpx;
  background: #95DBF5;
  border-radius: 29rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 57rpx;
  text-align: center;
}
.login .wechat .wechat_division {
  width: 80%;
  height: 30rpx;
  margin: 0 auto;
  padding: 40rpx 0;
  font-size: 24rpx;
}
.login .wechat .wechat_img {
  width: 100rpx;
  height: 100rpx;
  margin: 0 auto;
  margin-top: 40rpx;
}
.login .wechat .agreement {
  width: 523rpx;
  height: 66rpx;
  margin: 30rpx auto;
  font-size: 24rpx;
  font-weight: 400;
  color: #9A9A9A;
  display: flex;
  justify-content: space-between;
}
.login .wechat .agreement text:nth-child(2) {
  color: #4270BA;
}
.login .wechat .agreement text:nth-child(3) {
  color: #4270BA;
}
.login .wechat .agreement text:nth-child(5) {
  color: #4270BA;
}

