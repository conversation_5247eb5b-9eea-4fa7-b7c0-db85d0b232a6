<template>
	<view class="container">
		<!-- 授课信息 -->
		<view class="course-list u-border-bottom" v-for="item in detail.orderProducts" :key="item.id">
			<view class="title">
				<text>{{item.productName}}</text>
				<view class="btns" @click="goClick(item)" v-if="item.needRedeem">
					<image src="@/static/Project_drawing 23.png" mode=""></image>
					<text>去核销</text>
				</view>
				<view class="done" v-else>
					{{item.statusName}}
				</view>
			</view>
			<view class="mid">
				<!-- <text class="subject">考研英语</text> -->
				<text class="date" v-if="item.startTime">{{item.startTime}}-{{item.endTime}} | 共{{item.hours}}节</text>
			</view>

			<view class="bottom">
				<view class="teacher-list">
					<view v-if="item.teachers.length">
						<view class="teacher-info" v-for="(t,index) in item.teachers" :key="index">
							<image class="avatar" :src="t.img" />
							<text>{{t.title}}</text>
						</view>
					</view>


				</view>
				<view class="course-money">
					￥{{item.totalPrice}}
				</view>
			</view>
		</view>
		<!-- <view class="course-list u-border-bottom">
			<view class="title">
				<text>【25数学考研】精讲课程</text>
				<view class="done">
					已完成
				</view>
			</view>
			<view class="mid">
				<text class="subject">考研英语</text>
				<text class="date">2024.02.04-2024.12.16 | 共54节</text>
			</view>

			<view class="bottom">
				<view class="teacher-list">
					<view class="teacher-info">
						<image class="avatar" src="https://test-1300870289.cos.ap-nanjing.myqcloud.com/sun.png" />
						<text>孙老师</text>
					</view>
					<view class="teacher-info">
						<image class="avatar" src="https://test-1300870289.cos.ap-nanjing.myqcloud.com/sun.png" />
						<text>孙老师</text>
					</view>
					<view class="teacher-info">
						<image class="avatar" src="https://test-1300870289.cos.ap-nanjing.myqcloud.com/sun.png" />
						<text>孙老师</text>
					</view>
				</view>
				<view class="course-money">
					￥20000.00
				</view>
			</view>
		</view> -->
		<view class="detail">
			<view class="title u-border-bottom">
				<text>订单信息</text>
				<!-- <text class="status">待核销</text> -->
			</view>
			<view class="list-info u-border-bottom">
				<view class="info">
					<text>订单编号</text>
					<text>{{detail.sn}}</text>
				</view>
				<view class="info">
					<text>下单时间</text>
					<text>{{detail.transTime}}</text>
				</view>
				<!-- <view class="info">
					<text>活动优惠</text>
					<text>{{detail.couponAmount}}</text>
				</view>
				<view class="info">
					<text>抵扣优惠</text>
					<text>0.00</text>
				</view> -->
				<view class="info">
					<text>优惠券优惠
					</text>
					<text>{{detail.couponAmount}}</text>
				</view>
			</view>
			<view class="bottom">
				<view class="btn">申请退款</view>
				<view class="total">
					实际支付：<text>￥{{detail.checkoutPrice}}</text>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	import {
		orderDetail
	} from '@/api/comm.js'
	export default {
		data() {
			return {
				id: '',
				detail: {},
			};
		},
		onLoad(options) {
			this.id = options.id
			console.log(this.id)
			this.getDetails()
		},
		onShow() {

		},
		methods: {
			async getDetails() {
				const {
					data,
					errCode,
					msg
				} = await orderDetail(this.id)
				if (errCode == 0) {
					this.detail = data
				} else {

				}
			},
			goClick(item) {
				let obj = JSON.stringify({
					...item,
					sn: this.detail.sn,
					transTime: this.detail.transTime,
					orderId: this.detail.id,
				})

				uni.navigateTo({
					url: `/pages/me_all/orderdetails/orderdetails?obj=${obj}`
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		// background-color: $container-bg-color;
		padding: 0 30rpx;
		min-height: 100vh;
		padding-top: 42rpx;
		margin-bottom: 33rpx;
		box-sizing: border-box;

		.u-border-bottom {
			border: none;
		}

		.course-list {
			padding: 28rpx;
			margin-bottom: 32rpx;
			background-color: #fff;
			border-radius: 20rpx;
			width: 690rpx;
			box-sizing: border-box;

			.title {
				// padding-top: 28rpx;
				padding-bottom: 18rpx;
				font-weight: bold;
				font-size: 26rpx;
				color: #060606;
				display: flex;
				align-items: center;
				justify-content: space-between;

				// border-bottom: 1rpx solid #E5E5E5;

				.btns {
					width: 140rpx;
					height: 45rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 30rpx;
					border: 1rpx solid #8a8a8a;

					image {
						width: 20rpx;
						height: 20rpx;
						margin-right: 5rpx;
					}

					text {
						font-size: 22rpx;
						color: #8a8a8a;
						margin-top: -5rpx;
					}
				}

				.done {
					width: 110rpx;
					height: 45rpx;
					line-height: 45rpx;
					text-align: center;
					font-size: 22rpx;
					color: #00C2A0;
					border-radius: 30rpx;
					border: 1rpx solid #00C2A0;
				}

				.status {
					font-weight: 500;
					font-size: 24rpx;
					color: #A2A2A2;
				}
			}

			.mid {
				margin-top: 6rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.subject {
					font-weight: bold;
					font-size: 22rpx;
					color: #09CC8C;
					padding: 9rpx 7rpx;
					background: #EEFAF6;
					border-radius: 10rpx;
				}

				.tag {
					padding: 0 8rpx;
					height: 48rpx;
					background-color: #EEFAF6;
					// color: $main-color;
					line-height: 48rpx;
					font-size: 22rpx;
					font-weight: bold;
					border-radius: 10rpx;
				}

				.date {
					margin-left: 8rpx;
					font-size: 22rpx;
					color: #A4A4A4;
				}
			}

			.bottom {
				margin-top: 20rpx;
				// margin-bottom: 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.teacher-list {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					padding-bottom: 20rpx;

					.teacher-info {
						margin-right: 24rpx;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						font-size: 22rpx;
						color: #818181;

						.avatar {
							width: 60rpx;
							height: 60rpx;
							border-radius: 100%;
							margin-bottom: 2rpx;
						}

					}
				}

				.course-money {
					color: #E16965;
					font-size: 30rpx;
				}
			}

		}

		.detail {
			background-color: #fff;
			border-radius: 20rpx;


			.title {
				padding: 28rpx;
				padding-bottom: 20rpx;
				color: #201E2E;
				font-size: 28rpx;
				font-weight: bold;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1rpx solid #E5E5E5;

				.status {
					font-weight: 500;
					font-size: 24rpx;
					color: #A2A2A2;
				}
			}

			.list-info {
				padding-top: 28rpx;
				padding-bottom: 28rpx;
				border-bottom: 1rpx solid #E5E5E5;

				.info {
					display: flex;
					align-items: center;
					justify-content: space-between;
					color: #A2A2A2;
					font-size: 24rpx;
					padding: 0 28rpx;
					padding-bottom: 18rpx;

					text {
						&:last-child {
							color: #201E2E;
						}
					}
				}
			}

			.bottom {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 26rpx 28rpx 36rpx;

				.btn {
					width: 110rpx;
					height: 45rpx;
					line-height: 45rpx;
					text-align: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #343434;
					border-radius: 30rpx;
					border: 1rpx solid #989898;
				}
			}

			.total {
				color: #A4A4A4;
				font-size: 24rpx;
				display: flex;
				align-items: center;

				text {
					font-size: 32rpx;
					color: #E16965;
				}
			}
		}
	}
</style>