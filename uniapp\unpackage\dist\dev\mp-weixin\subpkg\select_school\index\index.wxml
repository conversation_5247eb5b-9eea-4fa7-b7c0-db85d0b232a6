<view class="content data-v-aa0cedb0"><height vue-id="978fc644-1" hg="{{System_height}}" class="data-v-aa0cedb0" bind:__l="__l"></height><view class="content_header data-v-aa0cedb0"><view class="nav-title data-v-aa0cedb0" style="{{'top:'+(System_height+'rpx')+';'}}"><text class="data-v-aa0cedb0">考研择校</text></view><uni-icons class="back-left data-v-aa0cedb0" style="{{'top:'+(System_height+'rpx')+';'}}" vue-id="978fc644-2" type="left" size="24" color="#fff" data-event-opts="{{[['^tap',[['back']]]]}}" bind:tap="__e" bind:__l="__l"></uni-icons><image class="header-image data-v-aa0cedb0" src="/static/select_school/top_bg.png" mode="aspectFit"></image></view><view class="ai-section data-v-aa0cedb0"><view class="ai-container data-v-aa0cedb0"><view class="ai-images data-v-aa0cedb0"><image class="ai-bg-4 data-v-aa0cedb0" src="/static/select_school/ai4-56586a.png"></image><image class="ai-bg-1 data-v-aa0cedb0" src="/static/select_school/ai1-56586a.png"></image><image class="ai-bg-2 data-v-aa0cedb0" src="/static/select_school/ai2-56586a.png"></image><view class="ai-center data-v-aa0cedb0"><image class="ai-main data-v-aa0cedb0" src="/static/select_school/ai-56586a.png"></image><image class="ai-text data-v-aa0cedb0" src="/static/select_school/ai_text-56586a.png"></image></view></view><view data-event-opts="{{[['tap',[['generateReport',['$event']]]]]}}" class="generate-btn data-v-aa0cedb0" bindtap="__e"><text class="data-v-aa0cedb0">点击生成报告</text></view><view class="usage-count data-v-aa0cedb0">可用次数：2</view></view></view><view class="report-section data-v-aa0cedb0"><view class="section-title data-v-aa0cedb0">查看报告</view><view class="report-list data-v-aa0cedb0"><block wx:for="{{reportList}}" wx:for-item="report" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewReport',['$0'],[[['reportList','',index]]]]]]]}}" class="report-item data-v-aa0cedb0" bindtap="__e"><view class="report-header data-v-aa0cedb0"><view class="report-name data-v-aa0cedb0">{{report.name}}</view><view class="{{['report-status','data-v-aa0cedb0',report.status==='已生成'?'status-completed':'status-pending']}}">{{''+report.status+''}}</view></view><view class="report-desc data-v-aa0cedb0">{{report.description}}</view><view class="report-footer data-v-aa0cedb0"><view class="report-time data-v-aa0cedb0">{{"生成时间："+report.createTime}}</view><view data-event-opts="{{[['tap',[['viewDetail',['$0'],[[['reportList','',index]]]]]]]}}" class="report-detail data-v-aa0cedb0" catchtap="__e">查看详情</view></view></view></block></view></view><login vue-id="978fc644-3" show="{{enter}}" data-event-opts="{{[['^closepage',[['closepage']]]]}}" bind:closepage="__e" class="data-v-aa0cedb0" bind:__l="__l"></login></view>