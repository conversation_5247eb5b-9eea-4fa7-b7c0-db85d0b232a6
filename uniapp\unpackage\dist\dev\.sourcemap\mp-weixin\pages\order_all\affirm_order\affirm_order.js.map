{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/affirm_order/affirm_order.vue?8a49", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/affirm_order/affirm_order.vue?b805", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/affirm_order/affirm_order.vue?5e98", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/affirm_order/affirm_order.vue?3f65", "uni-app:///pages/order_all/affirm_order/affirm_order.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/affirm_order/affirm_order.vue?f2b0", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/affirm_order/affirm_order.vue?1eab"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "showCoupon", "showAdress", "payCheck", "readCheck", "couponvalue", "num", "couponDeafultSel", "selcoupon", "couponMoney", "addressDetail", "goodsList", "total", "value", "openid", "show", "externalUrl", "href", "isRead", "code", "productId", "campusId", "cart", "totalPrice", "checkoutPrice", "validCouponList", "couponAmount", "onShow", "console", "onLoad", "quantity", "created", "methods", "addAddress", "uni", "url", "select", "rule", "getDetail", "useCouponId", "errCode", "msg", "item", "get<PERSON><PERSON><PERSON>", "pagClick", "title", "icon", "duration", "shoppingAddressId", "getmpPay", "orderId", "callWeChatPay", "timeStamp", "nonceStr", "package", "signType", "paySign", "success", "fail", "handlePaymentSuccess", "handlePaymentFailure", "radioChange", "editClick", "close", "open", "groupChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyK1nB;AAOA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAEA;IACAC;EACA;EACAC;IACA;IACA;MACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;MACA;QACAT;QACAU;MACA;IACA;IACA;IACA;EACA;EACAC;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA;kBACAjB;kBACAC;kBACAiB;gBACA;cAAA;gBAAA;gBAPAvC;gBACAwC;gBACAC;gBAMA;kBACA;kBACA;kBACA;kBACA;kBACA;oBACA,uCACAC;sBACA3B;oBAAA;kBAEA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA4B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAA;gBAHA3C;gBACAwC;gBACAC;gBAEA;kBACA;oBACA;oBACAb;kBACA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAV;kBACAW;kBACAC;kBAAA;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACAb;kBACAW;kBACAC;kBAAA;kBACAC;gBACA;gBAAA;cAAA;gBAGAb;kBACAW;gBACA;gBAAA;gBAAA;gBAAA,OAMA;kBACAxB;kBACAC;kBACAiB;kBACAS;gBACA;cAAA;gBAAA;gBARAhD;gBACAwC;gBACAC;gBAOA;kBACA;oBACAP;sBACAC;oBACA;oBACAD;kBACA;oBACA;kBACA;gBAEA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAf;kBACAW;gBACA;gBAAA;gBAAA;gBAAA,OAMA;kBACAK;gBAEA;cAAA;gBAAA;gBANAlD;gBACAwC;gBACAC;gBAMA;kBACA;gBAEA;kBACAP;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACA;IACAiB;MAAA;MACA;MACAxD;QACAyD;QACAC;QACAC;QACAC;QACAC;QACAC;UACA;UACAvB;YACAC;UACA;UACAD;QACA;QACAwB;UACA;UACA;QACA;MACA;IAEA;IACA;IACAC;MACA;MACAhE;QACAkD;QACAC;QACAC;MACA;;MAEA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAa;MACAhC;MAEA;QACA;QACAjC;UACAkD;UACAC;QACA;MACA;QACA;QACAnD;UACAkD;UACAC;QACA;MACA;IACA;IAEAe;MACA;IACA;IAEAC;MACA5B;QACAC;MACA;IACA;IACA4B;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACtcA;AAAA;AAAA;AAAA;AAAyqC,CAAgB,+oCAAG,EAAC,C;;;;;;;;;;;ACA7rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order_all/affirm_order/affirm_order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order_all/affirm_order/affirm_order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./affirm_order.vue?vue&type=template&id=5c301432&scoped=true&\"\nvar renderjs\nimport script from \"./affirm_order.vue?vue&type=script&lang=js&\"\nexport * from \"./affirm_order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./affirm_order.vue?vue&type=style&index=0&id=5c301432&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5c301432\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order_all/affirm_order/affirm_order.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./affirm_order.vue?vue&type=template&id=5c301432&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio/u-radio\" */ \"uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.validCouponList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./affirm_order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./affirm_order.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\r\n\t\t<view class=\"container-info\">\r\n\r\n\t\t\t<!-- 地址信息 -->\r\n\t\t\t<view class=\"address\" v-if=\"addressDetail.addr\">\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<view class=\"user-address\">\r\n\t\t\t\t\t\t<text>{{addressDetail.name}} +86 {{addressDetail.phone}}</text>\r\n\t\t\t\t\t\t<!-- <text class=\"tag\">默认</text> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"detail\">{{addressDetail.addr}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\" size=\"20\" @click=\"editClick\"></u-icon>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"add-address\" v-else>\r\n\t\t\t\t<view class=\"add-address-btn\" @click=\"addAddress\">\r\n\t\t\t\t\t+添加地址\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 课程信息 -->\r\n\t\t\t<view class=\"course-info\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t课程信息\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-container\" v-for=\"(item,index) in goodsList\" :key=\"index\">\r\n\r\n\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t<text>{{item.productName}}</text>\r\n\t\t\t\t\t\t<text>￥{{item.unitPrice}} </text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"course-detail\">\r\n\t\t\t\t\t\t<!-- <text class=\"tag\">考研数学</text> -->\r\n\t\t\t\t\t\t<text v-show=\"item.startTime&&item.hours\">{{item.startTime}}-{{item.endTime}} |\r\n\t\t\t\t\t\t\t共{{item.hours}}节</text>\r\n\t\t\t\t\t\t<text class=\"num\"> × {{item.quantity}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"teacher-info\">\r\n\t\t\t\t\t\t主讲老师：<text v-for=\"(t,i) in item.teachers\">{{t.title}}、</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 付款信息 -->\r\n\t\t\t<view class=\"pay-info\">\r\n\t\t\t\t<view class=\"h1-title\">\r\n\t\t\t\t\t付款信息\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<text>商品金额</text>\r\n\t\t\t\t\t<!-- <text>￥{{total}}</text> -->\r\n\t\t\t\t\t<text>￥{{totalPrice}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"info\">\r\n\t\t\t\t\t<text>活动优惠</text>\r\n\t\t\t\t\t<text class=\"color-red\">￥33800.00</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<text>抵扣优惠</text>\r\n\t\t\t\t\t<text class=\"color-red\">￥0</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"info\">\r\n\t\t\t\t\t<text>优惠券优惠</text>\r\n\t\t\t\t\t<view class=\"choose-coupon\" @click=\"open\">\r\n\t\t\t\t\t\t<!-- <text class=\"color-red\">￥{{couponMoney}}</text> -->\r\n\t\t\t\t\t\t<text class=\"color-red\" v-if=\"couponAmount!=0\"> - ￥{{couponAmount}}</text>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" size=\"18\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 支付方式 -->\r\n\t\t\t<view class=\"pay-info\">\r\n\t\t\t\t<view class=\"h1-title\">\r\n\t\t\t\t\t支付方式\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"pay-type\">\r\n\t\t\t\t\t<u-icon name=\"weixin-fill\" color=\"rgb(40  183 97)\" size=\"48\" label=\"微信\" labelSize=\"14px\"></u-icon>\r\n\t\t\t\t\t<radio style=\"transform:scale(0.8)\" value=\"1\" activeBackgroundColor=\"#1BB394\" :checked=\"true\" />\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"author-info\">\r\n\t\t\t\t<radio-group @change=\"radioChange\" class=\"radio-group\">\r\n\t\t\t\t\t<radio style=\"transform:scale(0.6)\" value=\"1\" activeBackgroundColor=\"#1BB394\" />\r\n\t\t\t\t\t<text>我已阅读 </text>\r\n\t\t\t\t\t<text>备案内容承诺公式、退款协议</text>\r\n\t\t\t\t</radio-group>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tip\">\r\n\t\t\t\t*涉及优惠活动的订单发起退款时，实际退费金额需要重新核算\r\n\t\t\t</view>\r\n\r\n\r\n\t\t</view>\r\n\t\t<view class=\"bottom-opt\">\r\n\t\t\t<view class=\"left\">\r\n\t\t\t\t<!-- <text class=\"real-money\">实付金额<text>￥{{total}}</text></text> -->\r\n\t\t\t\t<text class=\"real-money\">实付金额<text>￥{{checkoutPrice}}</text></text>\r\n\t\t\t\t<!-- <text class=\"preferential\">已优惠￥3800.00</text> -->\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"btn\" @click=\"pagClick\">\r\n\t\t\t\t立即购买\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 优惠券底部弹出层 -->\r\n\t\t<u-popup :show=\"showCoupon\" mode=\"bottom\" @close=\"close\">\r\n\t\t\t<view class=\"coupon-conainer\">\r\n\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t<text>优惠券详情</text>\r\n\t\t\t\t\t<u-icon class=\"close\" @click=\"close\" name=\"close\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"coupon-list\">\r\n\t\t\t\t\t<view class=\"num\">\r\n\t\t\t\t\t\t<!-- 可用优惠券({{num}}) -->\r\n\t\t\t\t\t\t可用优惠券({{validCouponList.length}})\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"coupon-container\">\r\n\t\t\t\t\t\t<u-radio-group placement=\"column\" @change=\"groupChange\" v-model=\"couponDeafultSel\">\r\n\t\t\t\t\t\t\t<view class=\"coupon\" v-for=\"(item,index) in validCouponList\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t\t<!--  -->\r\n\t\t\t\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t\t\t\t<text>{{item.couponValue}} <text style=\"font-size: 32rpx;\">元</text></text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.hasMinOrderAmount\">满{{item.minOrderAmount}}可用</text>\r\n\t\t\t\t\t\t\t\t\t<text v-else>无门槛</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"name\">\r\n\t\t\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"detail-info\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-left\">\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- <text class=\"date\">有效期 {{item.date}}</text> -->\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"date\">有效期至\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text>{{item.expireTime}}</text></text>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"rule\" @click=\"rule(index)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<text>使用规则</text>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#009c7b\" size=\"24\"></u-icon>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"rules\" v-show=\"item.show\" v-html=\"item.description\">\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- :name=\"item.id\" -->\r\n\t\t\t\t\t\t\t\t\t\t\t<u-radio :name=\"item.id\" size=\"20px\" activeColor=\"#009c7b\" iconSize=\"18px\">\r\n\t\t\t\t\t\t\t\t\t\t\t</u-radio>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"submit\" @click=\"select\">\r\n\t\t\t\t\t\t\t\t提交\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</u-radio-group>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetShippingAddress,\r\n\t\tupdateShippingAddress,\r\n\t\tgetDefaultShippingAddress,\r\n\t\tpreOrder,\r\n\t\twxOrder,\r\n\t\txcxPay\r\n\t} from '@/api/comm.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowCoupon: false,\r\n\t\t\t\tshowAdress: true,\r\n\t\t\t\tpayCheck: true,\r\n\t\t\t\treadCheck: false,\r\n\t\t\t\tcouponvalue: \"\",\r\n\t\t\t\tnum: 4,\r\n\r\n\t\t\t\tcouponDeafultSel: '', //默认选中的优惠券\r\n\t\t\t\tselcoupon: {}, //选中的优惠券\r\n\t\t\t\tcouponMoney: 0, //优惠金额\r\n\t\t\t\taddressDetail: {},\r\n\t\t\t\tgoodsList: [],\r\n\t\t\t\ttotal: 0,\r\n\t\t\t\tvalue: '',\r\n\t\t\t\topenid: false,\r\n\t\t\t\tshow: false,\r\n\t\t\t\texternalUrl: '',\r\n\t\t\t\thref: '',\r\n\t\t\t\tisRead: 0,\r\n\t\t\t\tcode: '',\r\n\t\t\t\tproductId: '', // 产品id\r\n\t\t\t\tcampusId: '', //校区id\r\n\t\t\t\tcart: [], //购物车数据\r\n\t\t\t\ttotalPrice: 0, //商品总金额\r\n\t\t\t\tcheckoutPrice: 0, //折后商品金额\r\n\t\t\t\tvalidCouponList: [], //可用的优惠券列表\r\n\t\t\t\tcouponAmount: '', //优惠金额\r\n\t\t\t};\r\n\t\t},\r\n\t\tonShow() {\r\n\r\n\t\t\tthis.$forceUpdate()\r\n\t\t\tconsole.log(this.addressDetail)\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tconst pages = getCurrentPages()\r\n\t\t\tif (pages.length >= 2) {\r\n\t\t\t\tconst prevPage = pages[pages.length - 2] // 获取上一页实例\r\n\t\t\t\tif (prevPage.route === 'pages/order_all/affirm_order/affirm_order') {\r\n\t\t\t\t\tthis.addressDetail = JSON.parse(option.query.item)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.campusId = uni.getStorageSync('campusId')\r\n\t\t\tlet arr = uni.getStorageSync('cartItems')\r\n\t\t\tthis.cart = arr.map(item => {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tproductId: item.id,\r\n\t\t\t\t\tquantity: item.quantity\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tthis.getAddress()\r\n\t\t\tthis.getDetail()\r\n\t\t},\r\n\t\tcreated() {},\r\n\t\tmethods: {\r\n\t\t\t// 点击添加地址\r\n\t\t\taddAddress() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/order_all/shipping_address/shipping_address'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 点击提交优惠券\r\n\t\t\tselect() {\r\n\t\t\t\tif (!this.couponDeafultSel) {\r\n\t\t\t\t\tthis.showCoupon = false\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.getDetail().then(res => {\r\n\t\t\t\t\t\tthis.showCoupon = false\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\trule(index) {\r\n\t\t\t\tif (this.validCouponList[index].show) {\r\n\t\t\t\t\tthis.validCouponList[index].show = 0\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.validCouponList[index].show = 1\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 获取结算详情\r\n\t\t\tasync getDetail() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await preOrder({\r\n\t\t\t\t\tcampusId: this.campusId,\r\n\t\t\t\t\tcart: this.cart,\r\n\t\t\t\t\tuseCouponId: this.couponDeafultSel,\r\n\t\t\t\t})\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tthis.goodsList = data.orderProducts\r\n\t\t\t\t\tthis.totalPrice = data.totalPrice\r\n\t\t\t\t\tthis.checkoutPrice = data.checkoutPrice\r\n\t\t\t\t\tthis.couponAmount = data.couponAmount\r\n\t\t\t\t\tthis.validCouponList = data.validCouponList.map(item => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\tshow: 0\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getAddress() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await getDefaultShippingAddress()\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tif (data) {\r\n\t\t\t\t\t\tthis.addressDetail = data\r\n\t\t\t\t\t\tconsole.log('1', this.addressDetail)\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// // 点击立即支付\r\n\t\t\tasync pagClick() {\r\n\t\t\t\tif (!this.addressDetail.addr) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先添加地址再进行支付',\r\n\t\t\t\t\t\ticon: 'none', // 不显示图标\r\n\t\t\t\t\t\tduration: 2000 // 显示时长（毫秒）\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.isRead == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先阅读并同意隐私协议',\r\n\t\t\t\t\t\ticon: 'none', // 不显示图标\r\n\t\t\t\t\t\tduration: 2000 // 显示时长（毫秒）\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '操作中...'\r\n\t\t\t\t})\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\terrCode,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = await wxOrder({\r\n\t\t\t\t\t\tcampusId: this.campusId,\r\n\t\t\t\t\t\tcart: this.cart,\r\n\t\t\t\t\t\tuseCouponId: this.couponDeafultSel,\r\n\t\t\t\t\t\tshoppingAddressId: this.addressDetail.id\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\t\tif (data.checkoutPrice == 0) {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: `/page/order_all/submit_order/submit_order?show=1`\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tuni.removeStorageSync('cartItems')\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.getmpPay(data.id)\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.tip(msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\t//TODO handle the exception\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//小程序支付接口\r\n\t\t\tasync getmpPay(val) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '操作中...'\r\n\t\t\t\t})\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\terrCode,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = await xcxPay({\r\n\t\t\t\t\t\torderId: val,\r\n\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\t\tthis.callWeChatPay(data)\r\n\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.tip(msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\t//TODO handle the exception\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// // 调用微信支付\r\n\t\t\tcallWeChatPay(payParams) {\r\n\t\t\t\t// 3. 调用微信支付\r\n\t\t\t\twx.requestPayment({\r\n\t\t\t\t\ttimeStamp: payParams.timeStamp,\r\n\t\t\t\t\tnonceStr: payParams.nonceStr,\r\n\t\t\t\t\tpackage: payParams.package,\r\n\t\t\t\t\tsignType: payParams.signType,\r\n\t\t\t\t\tpaySign: payParams.paySign,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t// 支付成功回调\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: `/page/order_all/submit_order/submit_order?show=1`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tuni.removeStorageSync('cartItems')\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t// 支付失败回调\r\n\t\t\t\t\t\tthis.handlePaymentFailure(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\t\t\t},\r\n\t\t\t// 支付成功处理\r\n\t\t\thandlePaymentSuccess() {\r\n\t\t\t\t// console.log('支付成功，订单ID:', orderId);\r\n\t\t\t\twx.showToast({\r\n\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 这里可以添加支付成功后的业务逻辑\r\n\t\t\t\t// 例如：跳转到订单详情页\r\n\t\t\t\t// wx.navigateTo({\r\n\t\t\t\t// \turl: `/pages/order/detail?id=${orderId}`\r\n\t\t\t\t// });\r\n\t\t\t},\r\n\r\n\t\t\t// 支付失败处理\r\n\t\t\thandlePaymentFailure(err) {\r\n\t\t\t\tconsole.warn('支付失败:', err);\r\n\r\n\t\t\t\tif (err.errMsg.includes('cancel')) {\r\n\t\t\t\t\t// 用户取消支付\r\n\t\t\t\t\twx.showToast({\r\n\t\t\t\t\t\ttitle: '支付已取消',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 支付失败\r\n\t\t\t\t\twx.showToast({\r\n\t\t\t\t\t\ttitle: '支付失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tradioChange(e) {\r\n\t\t\t\tthis.isRead = e.detail.value\r\n\t\t\t},\r\n\r\n\t\t\teditClick() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/order_all/shipping_address/shipping_address?type=1`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.showCoupon = false\r\n\t\t\t\tthis.couponDeafultSel = ''\r\n\t\t\t},\r\n\t\t\topen() {\r\n\t\t\t\tthis.showCoupon = true\r\n\t\t\t},\r\n\t\t\tgroupChange(n) {\r\n\t\t\t\t// //选中的优惠券\r\n\t\t\t\t// this.selcoupon = this.couponList.find(item => item.id == n)\r\n\t\t\t\t// this.couponMoney = this.selcoupon.value\r\n\t\t\t\t// this.showCoupon = false\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t\tpadding-bottom: 120rpx;\r\n\r\n\t\tview {\r\n\t\t\tbox-sizing: border-box;\r\n\t\t}\r\n\r\n\t\t::v-deep .u-popup__content {\r\n\t\t\tborder-radius: 16rpx 16rpx 0rpx 0rpx;\r\n\t\t}\r\n\r\n\t\t.coupon-conainer {\r\n\t\t\t.title {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tfont-weight: 800;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #0A0A0A;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding: 50rpx 0;\r\n\r\n\t\t\t\t::v-deep .u-icon--right {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\tright: 60rpx;\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.coupon-list {\r\n\t\t\t\tmax-height: 700rpx;\r\n\t\t\t\toverflow-y: scroll;\r\n\t\t\t\tpadding-bottom: 120rpx;\r\n\r\n\t\t\t\t.num {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #0A0A0A;\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.coupon-container {\r\n\t\t\t\t\tmargin-top: 36rpx;\r\n\t\t\t\t\tpadding: 0 30rpx;\r\n\r\n\t\t\t\t\t.coupon {\r\n\t\t\t\t\t\tmargin-bottom: 28rpx;\r\n\t\t\t\t\t\theight: 170rpx;\r\n\t\t\t\t\t\tbackground-size: contain;\r\n\t\t\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t\t\t.left {\r\n\t\t\t\t\t\t\twidth: 192rpx;\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\tbackground-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-left.png);\r\n\t\t\t\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t\t\t\t\tbackground-size: contain;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\t\t\tcolor: #CDF3E7;\r\n\t\t\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.right {\r\n\t\t\t\t\t\t\tbackground-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-right.png);\r\n\t\t\t\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t\t\t\t\tbackground-size: contain;\r\n\t\t\t\t\t\t\twidth: 500rpx;\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\tpadding: 24rpx 34rpx;\r\n\r\n\t\t\t\t\t\t\t.name {\r\n\t\t\t\t\t\t\t\tcolor: #4C5370;\r\n\t\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.detail-info {\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t\t\t\t.detail-left {\r\n\t\t\t\t\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t\t\t\t\ttext.date {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #AFAFAF;\r\n\t\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t.rule {\r\n\t\t\t\t\t\t\t\t\t\tmargin-top: 16rpx;\r\n\t\t\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\t\t\t\t// color: $main-color;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t.rules {\r\n\t\t\t\t\t\t\t\t\t\twidth: 299rpx;\r\n\t\t\t\t\t\t\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\t\t\t\t\t\t\tbox-shadow: 0rpx 2rpx 10rpx 0rpx rgba(0, 0, 0, 0.13);\r\n\t\t\t\t\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\t\t\ttop: 100rpx;\r\n\t\t\t\t\t\t\t\t\t\tleft: -20rpx;\r\n\t\t\t\t\t\t\t\t\t\tpadding: 20rpx 20rpx;\r\n\t\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\t\t\t\tcolor: #A0A0A0;\r\n\t\t\t\t\t\t\t\t\t\tz-index: 99999;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t.detail-right {\r\n\t\t\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t\t\t\t\t\t.use-btn {\r\n\t\t\t\t\t\t\t\t\t\twidth: 132rpx;\r\n\t\t\t\t\t\t\t\t\t\theight: 54rpx;\r\n\t\t\t\t\t\t\t\t\t\tborder-radius: 32rpx;\r\n\t\t\t\t\t\t\t\t\t\tborder: 1rpx solid #01997A;\r\n\t\t\t\t\t\t\t\t\t\tcolor: #01997A;\r\n\t\t\t\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\t\t\t\tline-height: 54rpx;\r\n\t\t\t\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.submit {\r\n\t\t\t\twidth: 530rpx;\r\n\t\t\t\theight: 92rpx;\r\n\t\t\t\tline-height: 92rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tposition: fixed;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tmargin: 50rpx auto;\r\n\t\t\t\tbackground: #01997A;\r\n\t\t\t\tborder-radius: 61rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.container-info {\r\n\t\t\t// background-color: $container-bg-color;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tpadding-top: 36rpx;\r\n\r\n\t\t\tpadding-bottom: 40rpx;\r\n\r\n\t\t\t.address {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tpadding: 34rpx 28rpx;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\r\n\t\t\t\t.info {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\ttext-align: left;\r\n\r\n\t\t\t\t\t.user-address {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\t\t\tcolor: #060606;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\r\n\t\t\t\t\t\t.tag {\r\n\t\t\t\t\t\t\twidth: 67rpx;\r\n\t\t\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\t\t\tbackground: #EEFAF6;\r\n\t\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\t\tline-height: 40rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tcolor: #09CC8C;\r\n\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\tmargin-left: 18rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\ttext.detail {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t\t\t\tcolor: #5A5A5A;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.add-address {\r\n\t\t\t\theight: 150rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t.add-address-btn {\r\n\t\t\t\t\twidth: 570rpx;\r\n\t\t\t\t\theight: 76rpx;\r\n\t\t\t\t\tbackground: #CDF3E7;\r\n\t\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\t\tcolor: #01997A;\r\n\t\t\t\t\tline-height: 76rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.course-info {\r\n\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tpadding: 22rpx 28rpx;\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #201E2E;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.info-container {\r\n\t\t\t\t\tpadding-bottom: 24rpx;\r\n\t\t\t\t\tborder-bottom: 1rpx solid #f1f1f1;\r\n\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.price {\r\n\t\t\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\t\t\tcolor: #060606;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t// &:last-child {\r\n\t\t\t\t\t\t\t// \tfont-weight: normal;\r\n\t\t\t\t\t\t\t// \tfont-size: 30rpx;\r\n\t\t\t\t\t\t\t// }\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.course-detail {\r\n\t\t\t\t\t\tmargin-top: 24rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t// &:first-child {\r\n\t\t\t\t\t\t\t// \tdisplay: inline-block;\r\n\t\t\t\t\t\t\t// \tbackground-color: #EEFAF6;\r\n\t\t\t\t\t\t\t// \tcolor: #09CC8C;\r\n\t\t\t\t\t\t\t// \tfont-size: 22rpx;\r\n\t\t\t\t\t\t\t// \tfont-weight: bold;\r\n\t\t\t\t\t\t\t// \tpadding: 10rpx 2rpx;\r\n\t\t\t\t\t\t\t// \tborder-radius: 10rpx;\r\n\t\t\t\t\t\t\t// }\r\n\r\n\t\t\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\t\t\t// margin-left: 8rpx;\r\n\t\t\t\t\t\t\t\tcolor: #A4A4A4;\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tcolor: #818181;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.teacher-info {\r\n\t\t\t\t\t\tmargin-top: 16rpx;\r\n\t\t\t\t\t\tcolor: #818181;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\r\n\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.pay-info {\r\n\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\tpadding: 22rpx 28rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\r\n\t\t\t\t.h1-title {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tcolor: #201E2E;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.info {\r\n\t\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tcolor: #201E2E;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\t\t.choose-coupon {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\t// margin-right: -28rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.color-red {\r\n\t\t\t\t\tcolor: #E62E2E;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.pay-type {\r\n\t\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t::v-deep .u-radio-group--row {\r\n\t\t\t\t\t\tjustify-content: flex-end;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.author-info {\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-start;\r\n\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t// margin-left: 6rpx;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\tcolor: #A4A4A4;\r\n\r\n\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tcolor: #01997A;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.tip {\r\n\t\t\t\tcolor: #E62E2E;\r\n\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\tpadding-left: 50rpx;\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\r\n\t\t.bottom-opt {\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 0;\r\n\t\t\tpadding: 0 22rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\theight: 120rpx;\r\n\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t.left {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tflex-direction: column;\r\n\r\n\t\t\t\t.real-money {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #201E2E;\r\n\t\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tcolor: #E62E2E;\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.preferential {\r\n\t\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tcolor: #201E2E;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\ttext-align: left;\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.btn {\r\n\t\t\t\twidth: 240rpx;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tline-height: 80rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\tbackground-color: #01997A;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./affirm_order.vue?vue&type=style&index=0&id=5c301432&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./affirm_order.vue?vue&type=style&index=0&id=5c301432&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557568655\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}