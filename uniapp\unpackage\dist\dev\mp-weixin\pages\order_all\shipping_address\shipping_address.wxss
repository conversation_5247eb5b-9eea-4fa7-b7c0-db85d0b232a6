
page {
	background: #F6F7FB;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content-container.data-v-f00be0f2 {
  padding: 30rpx;
}
.content-container .address.data-v-f00be0f2 {
  margin-top: 36rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 34rpx 28rpx;
  border-radius: 12rpx;
}
.content-container .address .info.data-v-f00be0f2 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  text-align: left;
}
.content-container .address .info .user-address.data-v-f00be0f2 {
  height: 40rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #060606;
  font-weight: bold;
  font-size: 28rpx;
}
.content-container .address .info .user-address .tag.data-v-f00be0f2 {
  width: 67rpx;
  height: 40rpx;
  background: #EEFAF6;
  border-radius: 10rpx;
  line-height: 40rpx;
  text-align: center;
  font-size: 22rpx;
  margin-left: 18rpx;
}
.content-container .address .info .user-address .bg-arrow-down.data-v-f00be0f2 {
  width: 30rpx;
  height: 30rpx;
  background: #01997A;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}
.content-container .address .info text.detail.data-v-f00be0f2 {
  width: 100%;
  margin-top: 14rpx;
  color: #5A5A5A;
  font-weight: bold;
  font-size: 24rpx;
}
.content-container .address .opt.data-v-f00be0f2 {
  color: #5A5A5A;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30%;
}
.content-container .address .opt text.data-v-f00be0f2 {
  padding: 0 10rpx;
}
.btn-container.data-v-f00be0f2 {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}
.btn-container .btn.data-v-f00be0f2 {
  width: 400rpx;
  height: 80rpx;
  background: linear-gradient(268deg, #01997A 0%, #08AB8A 100%);
  border-radius: 40rpx;
  color: #fff;
  font-size: 30rpx;
  line-height: 80rpx;
  text-align: center;
}

