{"version": 3, "sources": ["webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content_two.vue?8341", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content_two.vue?387d", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content_two.vue?6943", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content_two.vue?4952", "uni-app:///components/user_content_two.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content_two.vue?6c43", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content_two.vue?4d14"], "names": ["data", "list", "title", "content", "zzsh", "type", "xygh", "sxbk", "jyzb", "obj", "grade_1", "urls", "role", "props", "msgtype", "default", "computed", "created", "methods", "again", "fetchSseData", "requestUrl", "uni", "buffers", "abortController", "timeout20s", "responseTimeout", "clearTimeout", "console", "replace", "result", "name", "signal", "getTagList", "reportId", "advice", "tagList", "mark", "item", "filter", "children", "length", "for<PERSON>ach", "child", "push", "id", "subtitle", "grandson"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrCA;AAAA;AAAA;AAAA;AAA0mB,CAAgB,ooBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACmD9nB;AACA;AAIA;AAEA;AAAA;AAAA,gBACA;EACAA;IACA;MACAC;QACAC;QACAC;UACAC;YACAF;YACAC;YACAE;YACAJ;UAGA;UACAK;YACAJ;YACAC;YACAE;YACAJ;UAGA;UACAM;YACAL;YACAC;YACAE;YACAJ;UAGA;UACAO;YACAN;YACAC;YACAE;YACAJ;UAGA;QACA;MACA;MACAQ;QACAL;QACAE;QACAC;QACAC;MACA;MACAE,UAEA;MACAC;MACAC;IAEA;EACA;EACAC;IACAC;MACAT;MACAU;QAAA;MAAA;IACA;EACA;EACAC,4BACA,0CACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;EAEA;EACAC;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC,iBACA;gBACAA,uBACAC,wEACAjB,0CACA,mEACA,mEACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAkB;gBAAA;gBAEAC,yCACA;gBACAC;kBACAD;gBACA,WAEA;gBACAE;kBACAF;gBACA,WACA;gBAAA;gBAAA,OACA;kBACAG;kBACAD;oBACAF;kBACA;kBACA;oBACA;sBACAG;oBACA;oBACA;sBACA;oBACA;sBACA;sBACAC;sBACAzB;sBACA,6EACA,uCACA,mCACA0B,sBACA,uEACAA;oBACA;kBACA;oBACA;sBACAF;oBACA;oBACA;kBACA;oBACA;sBACA;wBACAJ,0EACA;wBACA,YACA;wBACA;wBACA;wBAEA;0BACAO;4BACAC;4BACA5B;0BACA;wBACA;wBACA;sBACA;oBACA;oBACA,sEACAE,wCACA;kBACA;gBACA;kBACA2B;gBACA;cAAA;gBACA;gBACAL;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAH,aACA;gBACA9B;kBACAkC;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAJ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEAK;gBACA;kBACAC;kBACAN;oBACA;oBACA;sBACAO;oBACA;sBACAA;oBACA;oBACA;oBACAC,cACAC;sBAAA,8CACAC,SACAC,SACA;oBAAA,GACAC;sBACAC;wBACA,wCACAC;0BACAC;0BACAC;0BACA3C;0BACAE,sBACA0C,SACA1C;wBACA;sBAEA;oBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EAGA;AACA;AAAA,4B;;;;;;;;;;;;;ACnSA;AAAA;AAAA;AAAA;AAA6qC,CAAgB,mpCAAG,EAAC,C;;;;;;;;;;;ACAjsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/user_content_two.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./user_content_two.vue?vue&type=template&id=777afb4d&scoped=true&\"\nvar renderjs\nimport script from \"./user_content_two.vue?vue&type=script&lang=js&\"\nexport * from \"./user_content_two.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user_content_two.vue?vue&type=style&index=0&id=777afb4d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"777afb4d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/user_content_two.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_content_two.vue?vue&type=template&id=777afb4d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l0 = _vm.__map(item.content, function (obj, key) {\n      var $orig = _vm.__get_orig(obj)\n      var g0 = obj.list.length\n      var g1 = !obj.list.length && !_vm.typeList.includes(obj.type)\n      var g2 = _vm.typeList.includes(obj.type)\n      var g3 = _vm.typeList.includes(obj.type)\n      return {\n        $orig: $orig,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      }\n    })\n    return {\n      $orig: $orig,\n      l0: l0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_content_two.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_content_two.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"info\" v-for=\"(item, index) in list\" :key=\"index\">\r\n\t\t\t<view class=\"info-title\">\r\n\t\t\t\t<image class=\"logo\" src=\"../static/imgs/rhomb.png\" mode=\"\"></image>\r\n\t\t\t\t<text class=\"title-text\">\r\n\t\t\t\t\t{{ item.title }}\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"info-content\" v-for=\"(obj, key) in item.content\" :key=\"key\">\r\n\t\t\t\t<view class=\"content-title\">\r\n\t\t\t\t\t<view class=\"cricle\">\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content-title-text\">\r\n\t\t\t\t\t\t{{ obj.title }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"subtitle-content-container\">\r\n\t\t\t\t\t<template v-if=\"obj.list.length\">\r\n\t\t\t\t\t\t<view class=\"subtitle-content\" v-for=\"(value, k) in obj.list\" :key=\"k\">\r\n\r\n\r\n\t\t\t\t\t\t\t<view class=\"subtitle-text\">\r\n\t\t\t\t\t\t\t\t{{ value.name }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"subtitle-content-text\">\r\n\t\t\t\t\t\t\t\t<rich-text :nodes=\"value.content\"></rich-text>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t<!-- <rich-text :nodes=\"obj.content\"></rich-text> -->\r\n\t\t\t\t\t<view class=\"\" v-else v-html=\"obj.content\"></view>\r\n\t\t\t\t\t<!-- {{obj.list.length}}{{obj.list}} -->\r\n\t\t\t\t\t<!-- <view class=\"\" @click=\"again('xygh')\">重新获取</view> -->\r\n\t\t\t\t\t<text class=\"loading\" v-if=\"!obj.list.length&&!typeList.includes(obj.type)\">\r\n\t\t\t\t\t\t<view class=\"loading-spinner\"></view>\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<text style=\"color:red;\" v-if=\"typeList.includes(obj.type)\"> 服务器繁忙，请重试</text>\r\n\t\t\t\t\t<view class=\"again\" v-if=\"typeList.includes(obj.type)\" @click=\"again(obj.type)\">\r\n\t\t\t\t\t\t重新生成</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport sseEvent from \"@/utils/sse_event.js\"\r\n\timport {\r\n\t\tgetReportTagList\r\n\t\t// getOrgListBy\r\n\t} from \"@/api/user.js\"\r\n\timport {\r\n\t\tmapState\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlist: [{\r\n\t\t\t\t\ttitle: \"大一期间核心学业规划\",\r\n\t\t\t\t\tcontent: {\r\n\t\t\t\t\t\tzzsh: {\r\n\t\t\t\t\t\t\ttitle: '组织生活',\r\n\t\t\t\t\t\t\tcontent: '',\r\n\t\t\t\t\t\t\ttype: 'zzsh',\r\n\t\t\t\t\t\t\tlist: [\r\n\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\txygh: {\r\n\t\t\t\t\t\t\ttitle: '学业规划',\r\n\t\t\t\t\t\t\tcontent: '',\r\n\t\t\t\t\t\t\ttype: 'xygh',\r\n\t\t\t\t\t\t\tlist: [\r\n\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsxbk: {\r\n\t\t\t\t\t\t\ttitle: '升学准备',\r\n\t\t\t\t\t\t\tcontent: '',\r\n\t\t\t\t\t\t\ttype: 'sxbk',\r\n\t\t\t\t\t\t\tlist: [\r\n\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tjyzb: {\r\n\t\t\t\t\t\t\ttitle: '就业指导',\r\n\t\t\t\t\t\t\tcontent: '',\r\n\t\t\t\t\t\t\ttype: 'jyzb',\r\n\t\t\t\t\t\t\tlist: [\r\n\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}\r\n\t\t\t\t}],\r\n\t\t\t\tobj: {\r\n\t\t\t\t\tzzsh: '',\r\n\t\t\t\t\txygh: '',\r\n\t\t\t\t\tsxbk: '',\r\n\t\t\t\t\tjyzb: ''\r\n\t\t\t\t},\r\n\t\t\t\tgrade_1: {\r\n\r\n\t\t\t\t},\r\n\t\t\t\turls: '',\r\n\t\t\t\trole: '',\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tmsgtype: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => []\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t...mapState('user', ['typeList']),\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// this.role = JSON.parse(uni.getStorageSync('USER-INFO-KEY')).role\r\n\t\t\t// if (this.role == 'user') {\r\n\t\t\t// \tthis.urls = '/my_dxt/report/queryBlock2'\r\n\t\t\t// }\r\n\t\t\t// if (this.role == 'student') {\r\n\t\t\tthis.urls = '/stu/report/queryBlock2'\r\n\t\t\t// }\r\n\t\t\tfor (let type in this.list[0]['content']) {\r\n\t\t\t\tthis.fetchSseData(type)\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tagain(type) {\r\n\t\t\t\tthis.$store.commit('user/removeType', type)\r\n\t\t\t\tthis.list[0]['content'][type].content = ''\r\n\t\t\t\tthis.fetchSseData(type)\r\n\t\t\t},\r\n\t\t\tasync fetchSseData(type) {\r\n\t\t\t\tlet requestUrl = ''\r\n\t\t\t\t// if (this.role == 'student') {\r\n\t\t\t\trequestUrl =\r\n\t\t\t\t\t`${uni.http.baseUrl}${this.urls}?\r\n\t\t\t\t\t\t\t\t\ttype=${type}\r\n\t\t\t\t\t\t\t\t\t&token=${this.$store.getters.token}\r\n\t\t\t\t\t\t\t\t\t&reportId=${this.$store.state.user.reportInfo.report.id}\r\n\t\t\t\t\t\t\t\t\t`;\r\n\t\t\t\t// }\r\n\t\t\t\t// if (this.role == 'user') {\r\n\t\t\t\t// \tlet params = {\r\n\t\t\t\t// \t\t...this.$store.state.user.reportInfo,\r\n\t\t\t\t// \t\treportId: this.$store.state.user.reportInfo.report.id,\r\n\t\t\t\t// \t\ttype: type,\r\n\t\t\t\t// \t\ttoken: this.$store.getters.token,\r\n\t\t\t\t// \t\tpostGraduation: JSON.stringify(this.$store.state.user.reportInfo.postGraduation),\r\n\t\t\t\t// \t}\r\n\t\t\t\t// \tlet queryStr = Object.keys(params)\r\n\t\t\t\t// \t\t.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))\r\n\t\t\t\t// \t\t.join('&');\r\n\t\t\t\t// \trequestUrl = `${uni.http.baseUrl}${this.urls}?${queryStr}`;\r\n\t\t\t\t// }\r\n\t\t\t\tlet buffers = '';\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst abortController = new AbortController();\r\n\t\t\t\t\t// 20秒总超时\r\n\t\t\t\t\tconst timeout20s = setTimeout(() => {\r\n\t\t\t\t\t\tabortController.abort();\r\n\t\t\t\t\t}, 20000);\r\n\r\n\t\t\t\t\t// 10秒无响应中断\r\n\t\t\t\t\tlet responseTimeout = setTimeout(() => {\r\n\t\t\t\t\t\tabortController.abort();\r\n\t\t\t\t\t}, 10000);\r\n\t\t\t\t\t// 每次调用 sseEvent 都需要等待它完成后才能继续下一个\r\n\t\t\t\t\tawait sseEvent(requestUrl, type, (buffer, eventType, t, f) => {\r\n\t\t\t\t\t\tclearTimeout(responseTimeout);\r\n\t\t\t\t\t\tresponseTimeout = setTimeout(() => {\r\n\t\t\t\t\t\t\tabortController.abort();\r\n\t\t\t\t\t\t}, 10000);\r\n\t\t\t\t\t\tif (eventType === \"say\") {\r\n\t\t\t\t\t\t\tif (timeout20s) {\r\n\t\t\t\t\t\t\t\tclearTimeout(timeout20s);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (f == 'json') {\r\n\t\t\t\t\t\t\t\tthis.list[0]['content'][type].list = buffer\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tlet content = this.list[0]['content'][type].content\r\n\t\t\t\t\t\t\t\tconsole.log(content)\r\n\t\t\t\t\t\t\t\tcontent += buffer\r\n\t\t\t\t\t\t\t\tthis.list[0]['content'][type].content = content.replace(/\\*\\*([^*]+)\\*\\*/g,\r\n\t\t\t\t\t\t\t\t\t\t\"<b>$1</b>\").replace(/&emsp;(.+?)##/g,\r\n\t\t\t\t\t\t\t\t\t\t\"<view class='sec-p'>$1</view>##\")\r\n\t\t\t\t\t\t\t\t\t.replace(/##(.+?)\\n/g,\r\n\t\t\t\t\t\t\t\t\t\t\"<view class='sec-title'>$1</view>&emsp;\").replace(/\\n{1,2}/g, \"<br>\")\r\n\t\t\t\t\t\t\t\t\t.replace(/[\\[(（【]注[:：].*?[】）)\\]]/g, \"\")\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else if (eventType === \"reply\") {\r\n\t\t\t\t\t\t\tif (timeout20s) {\r\n\t\t\t\t\t\t\t\tclearTimeout(timeout20s);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.list[0]['content'][mask].list[index].content = buffer;\r\n\t\t\t\t\t\t} else if (eventType === \"end\") {\r\n\t\t\t\t\t\t\tif (f != 'json') {\r\n\t\t\t\t\t\t\t\tif (['zzsh', 'xygh', 'sxbk', 'jyzb'].includes(t)) {\r\n\t\t\t\t\t\t\t\t\tbuffers = this.list[0]['content'][type].content.replace(/&emsp;(.+?$)/g,\r\n\t\t\t\t\t\t\t\t\t\t\"<view class='sec-p'>$1</view>\");\r\n\t\t\t\t\t\t\t\t\tconst regex =\r\n\t\t\t\t\t\t\t\t\t\t/<view class='sec-title'>(.*?)<\\/view>.*?<view class='sec-p'>(.*?)<\\/view>/gs;\r\n\t\t\t\t\t\t\t\t\tconst result = [];\r\n\t\t\t\t\t\t\t\t\tlet match;\r\n\r\n\t\t\t\t\t\t\t\t\twhile ((match = regex.exec(buffers)) !== null) {\r\n\t\t\t\t\t\t\t\t\t\tresult.push({\r\n\t\t\t\t\t\t\t\t\t\t\tname: match[1].trim(),\r\n\t\t\t\t\t\t\t\t\t\t\tcontent: match[2].trim()\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tthis.list[0]['content'][t].list = result\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.$store.commit('user/setPlan', {\r\n\t\t\t\t\t\t\t\t[type]: this.list[0]['content'][t].list\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tsignal: abortController.signal\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 请求成功完成，清除定时器\r\n\t\t\t\t\tclearTimeout(timeout20s);\r\n\t\t\t\t\tclearTimeout(responseTimeout);\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tthis.$store.commit('user/setType', type);\r\n\t\t\t\t\tconsole.error('请求错误:', error);\r\n\t\t\t\t}\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\t\t\tasync getTagList() {\r\n\t\t\t\tlet result = {}\r\n\t\t\t\t// if (this.role == 'student') {\r\n\t\t\t\tconst data = {\r\n\t\t\t\t\treportId: this.$store.state.user.reportInfo.report.id,\r\n\t\t\t\t}\r\n\t\t\t\tresult = await getReportTagList(data)\r\n\t\t\t\t// }\r\n\t\t\t\t// if (this.role == 'user') {\r\n\t\t\t\t// \tconst data = {\r\n\t\t\t\t// \t\tstudentId: this.$store.state.user.reportInfo.studentId,\r\n\t\t\t\t// \t}\r\n\t\t\t\t// \tresult = await getOrgListBy(data)\r\n\t\t\t\t// }\r\n\r\n\t\t\t\tlet advice = {}\r\n\t\t\t\tif (result.errCode == 0) {\r\n\t\t\t\t\tconst tagList = {}\r\n\t\t\t\t\tresult.data.forEach((item, index) => {\r\n\t\t\t\t\t\tlet mark = ''\r\n\t\t\t\t\t\tif (index == 0) {\r\n\t\t\t\t\t\t\tmark = 'zzsh'\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tmark = item.mark\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.list[0]['content'][mark]['title'] = item.name\r\n\t\t\t\t\t\titem.children\r\n\t\t\t\t\t\t\t.filter(child => (Array.isArray(child.children) && child\r\n\t\t\t\t\t\t\t\t.children\r\n\t\t\t\t\t\t\t\t.length >\r\n\t\t\t\t\t\t\t\t0))\r\n\t\t\t\t\t\t\t.forEach(child => {\r\n\t\t\t\t\t\t\t\tchild.children.forEach(grandson => {\r\n\t\t\t\t\t\t\t\t\tthis.list[0]['content'][mark]['list']\r\n\t\t\t\t\t\t\t\t\t\t.push({\r\n\t\t\t\t\t\t\t\t\t\t\tid: grandson.id,\r\n\t\t\t\t\t\t\t\t\t\t\tsubtitle: grandson.name,\r\n\t\t\t\t\t\t\t\t\t\t\tcontent: grandson.content,\r\n\t\t\t\t\t\t\t\t\t\t\ttype: grandson.type ?\r\n\t\t\t\t\t\t\t\t\t\t\t\tgrandson\r\n\t\t\t\t\t\t\t\t\t\t\t\t.type : ''\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t\tbackground: linear-gradient(180deg, rgba(203, 242, 224, 0.2) 0%, rgba(255, 255, 255, 0) 20%);\r\n\t\tborder-radius: 14rpx;\r\n\t\tborder: 2rpx dashed #1BB394;\r\n\t\tpadding: 24rpx 28rpx;\r\n\r\n\t\t@keyframes icon-loading {\r\n\t\t\t0% {\r\n\t\t\t\ttransform: rotate(0);\r\n\t\t\t}\r\n\r\n\t\t\t100% {\r\n\t\t\t\ttransform: rotate(360deg);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.loading {\r\n\t\t\tcolor: #909090;\r\n\t\t}\r\n\r\n\t\t.loading svg {\r\n\t\t\twill-change: transform;\r\n\t\t\twidth: 1em;\r\n\t\t\theight: 1em;\r\n\t\t\tanimation: 0.6s linear infinite icon-loading;\r\n\t\t}\r\n\r\n\t\t.again {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\twidth: 100rpx;\r\n\t\t\theight: 35rpx;\r\n\t\t\tline-height: 35rpx;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 16rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tbackground: #1bb394;\r\n\t\t\tcursor: pointer;\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t}\r\n\r\n\t\t.loading-spinner {\r\n\t\t\twidth: 36px;\r\n\t\t\theight: 36px;\r\n\t\t\tborder: 4px solid rgba(0, 0, 0, 0.1);\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tborder-top-color: currentColor;\r\n\t\t\tanimation: spin 1s linear infinite;\r\n\t\t}\r\n\r\n\t\t@keyframes spin {\r\n\t\t\t0% {\r\n\t\t\t\ttransform: rotate(0deg);\r\n\t\t\t}\r\n\r\n\t\t\t100% {\r\n\t\t\t\ttransform: rotate(360deg);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.info {\r\n\t\t\tpadding-top: 30rpx;\r\n\r\n\t\t\t.info-title {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t.logo {\r\n\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.title-text {\r\n\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\tcolor: #4C5370;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.info-content {\r\n\t\t\t\tmargin-top: 20rpx;\r\n\r\n\t\t\t\t.content-title {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t.cricle {\r\n\t\t\t\t\t\twidth: 6rpx;\r\n\t\t\t\t\t\theight: 6rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tbackground-color: #AEE8CD;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.content-title-text {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\tcolor: #5A5A5A;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.subtitle-content {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.subtitle-content-container {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tborder-radius: 9rpx;\r\n\t\t\t\t\tborder: 1rpx solid #A0E4C4;\r\n\t\t\t\t\tpadding: 28rpx;\r\n\t\t\t\t\t// padding-top: 0;\r\n\r\n\t\t\t\t\t.subtitle-text {\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\tpadding: 0 6rpx;\r\n\t\t\t\t\t\tborder-radius: 4rpx 4rpx 4rpx 4rpx;\r\n\t\t\t\t\t\tborder: 1rpx solid #FF9B3A;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\tline-height: 32rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tcolor: #FF9B3A;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.subtitle-content-text {\r\n\t\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\tcolor: #504E4E;\r\n\t\t\t\t\t\tline-height: 24rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t::v-deep .sec-title {\r\n\t\t\tborder: 1rpx solid #ff9b3a;\r\n\t\t\tborder-radius: 4rpx;\r\n\t\t\tfont-size: 16rpx !important;\r\n\t\t\tpadding: 6rpx;\r\n\t\t\tcolor: #ff9b3a;\r\n\t\t\tposition: relative;\r\n\t\t\t// margin-bottom: 5px;\r\n\r\n\t\t}\r\n\r\n\t\t::v-deep .sec-p {\r\n\t\t\twidth: 100%;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tpadding: 10rpx 15rpx;\r\n\t\t\tfont-size: 16rpx;\r\n\t\t\tcolor: #504E4E;\r\n\t\t\tline-height: 24rpx;\r\n\t\t\t// border: 1px solid #e3e3e3;\r\n\t\t\tdisplay: block;\r\n\t\t\t// margin: 10px 0 15px;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_content_two.vue?vue&type=style&index=0&id=777afb4d&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_content_two.vue?vue&type=style&index=0&id=777afb4d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557570245\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}