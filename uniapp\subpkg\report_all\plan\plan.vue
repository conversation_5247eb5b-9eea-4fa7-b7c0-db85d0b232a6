<template>
	<view class="container">
		<view class="header">
			<button class="transparent-button" @click="back">
				<uni-icons class="left-icon" type="left" size="20"></uni-icons>
			</button>
			<image class="header-img"
				src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/report_header_bg.png"
				mode=" widthFix"></image>
			<text class="teacher-info">学生：{{ reportUserInfo.name }}</text>
		</view>
		<view class="content">
			<view class="base-info">
				<blue-title :title="'第一部分：个人基础信息'"></blue-title>
				<view class="base-info-item-container">
					<view class="base-info-item width-25">
						<text class="item-title">学员姓名：</text>
						<text class="item">{{ reportUserInfo.name }}</text>
					</view>
					<view class="base-info-item">
						<text class="item-title">性别：</text>
						<view class="item" v-if="reportUserInfo.gender ==0">
							未知
						</view>
						<view class="item" v-if="reportUserInfo.gender == 2">
							女
						</view>
						<view class="item" v-if="reportUserInfo.gender == 1">
							男
						</view>
					</view>
					<view class="base-info-item">
						<text class="item-title">本科入学年份：</text>
						<text class="item">{{ reportUserInfo.joinYear }}</text>
					</view>
					<view class="base-info-item  width-25">
						<text class="item-title">本科院校：</text>
						<text class="item">{{ reportUserInfo.schoolName }}</text>
					</view>
					<view class="base-info-item">
						<text class="item-title">学院：</text>
						<text class="item">{{ reportUserInfo.collegeName }}</text>
					</view>
					<view class="base-info-item">
						<text class="item-title">专业：</text>
						<text class="item major">{{ reportUserInfo.majorName }}</text>
					</view>
					<view class="base-info-item  width-25">
						<text class="item-title">学员性格：</text>
						<text class="item">{{ reportUserInfo.personality == 1 ? '内向' : '外向' }}</text>
					</view>
					<view class="base-info-item">
						<text class="item-title">毕业发展：</text>
						<text class="item">{{ reportUserInfo.postGraduationLabel }}</text>
					</view>
				</view>


				<view class="hobby">
					<view class="hobby-item">
						<text class="hobby-title">体育特长：</text>
						<text class="hobby-info">{{ reportUserInfo.sportsInterest }}</text>
					</view>
					<view class="hobby-item">
						<text class="hobby-title">艺术特长：</text>
						<text class="hobby-info">{{ reportUserInfo.artInterest }}</text>
					</view>
					<view class="hobby-item">
						<text class="hobby-title">其它特长：</text>
						<text class="hobby-info">{{ reportUserInfo.academicInterest }}</text>
					</view>
					<view class="hobby-item">
						<text class="hobby-title">综合描述：</text>
						<text class="hobby-info">{{ reportUserInfo.collegePlan }}</text>
					</view>
				</view>

			</view>



			<!-- 高考信息 -->
			<view class="exam-info">
				<view class="base-info">
					<blue-title :title="'第二部分：高考基础信息'"></blue-title>
				</view>

				<view class="table">
					<view class="header">
						<view class="title">
							总分
						</view>
						<view class="title">
							排名
						</view>
						<view class="title">
							位次
						</view>
						<view class="title">
							语文
						</view>
						<view class="title">
							数学
						</view>
						<view class="title">
							外语
						</view>
						<view class="title">
							物理
						</view>
						<view class="title">
							化学
						</view>
						<view class="title">
							生物
						</view>
						<view class="title">
							政治
						</view>
						<view class="title">
							历史
						</view>
						<view class="title">
							地理
						</view>



					</view>
					<view class="table-line">
						<view class="table-line-item">
							{{ reportUserInfo.totalScore }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.rank }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.position }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.chineseScore }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.mathScore }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.foreignLangScore }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.physicsScore ? reportUserInfo.physicsScore : '-' }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.chemistryScore ? reportUserInfo.chemistryScore : '-' }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.biologyScore ? reportUserInfo.biologyScore : '-' }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.politicsScore ? reportUserInfo.politicsScore : '-' }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.historyScore ? reportUserInfo.historyScore : '-' }}
						</view>
						<view class="table-line-item">
							{{ reportUserInfo.geographyScore ? reportUserInfo.geographyScore : '-' }}
						</view>
					</view>
				</view>

			</view>



			<!-- 本科院校信息 -->
			<view class="university-info">
				<blue-title :title="'第三部分：院校基本信息'"></blue-title>
				<view class="university-tag">
					<image class="logo" :src="reportInfo.school.logo" mode=""></image>
					<view class="tag">
						<text class="name">
							{{ reportInfo.school.name }}
						</text>
						<view class="tag-list">
							<view class="tag-list-item" v-for="(tag, index) in reportInfo.school.tags" :key="index">
								{{ tag }}
							</view>
						</view>
					</view>
				</view>
				<user-content :msgtype='schoolInfo'></user-content>
			</view>
			<!-- 就业方向 -->
			<view class="plan">
				<blue-title :title="'第四部分：就业方向'"></blue-title>
				<user-content style='margin-top: 28rpx;' :msgtype='careerDirection'></user-content>
			</view>
			<!-- 升学规划 -->
			<view class="plan">
				<blue-title :title="'第五部分：升学规划'"></blue-title>
				<user-content style='margin-top: 28rpx;' :msgtype='educationPlanning'></user-content>
			</view>

			<view class="ability">
				<blue-title :title="'第六部分：学术能力提升'"></blue-title>
				<user-content style='margin-top: 28rpx;' :msgtype='academicAbilityEnhancement'></user-content>
			</view>

			<view class="university-plan">
				<blue-title :title="'第七部分：大学规划'"></blue-title>
				<user-content-two :msgtype='organizationalLife' style='margin-top: 28rpx;'></user-content-two>
			</view>

			<!-- 预科建议 -->
			<view class="ability">
				<blue-title :title="'第八部分：预科推荐'"></blue-title>
				<user-content style='margin-top: 28rpx;' :isSse="false"></user-content>
			</view>
		</view>

		<view class="footer">
			<button :loading="loading"
				:style="{ background: generating ? '#ccc' : 'linear-gradient(268deg, #26C8AC 0%, #19C990 100%)' }"
				class="save-btn" @click="handleSave">
				保存报告
			</button>
		</view>
	</view>
</template>

<script>
	import blueTitle from "@/components/blue_title.vue"
	import userContent from "@/components/user_content.vue"
	import userContentTwo from "@/components/user_content_two.vue"
	import {
		mapState
	} from "vuex"
	import {
		saveReport,
		generatePDF,
	} from '@/api/user.js'
	export default {
		data() {
			return {
				generating: true,
				loading: false,
				timerId: null,
				schoolInfo: [{
						type: 'schoolInfo',
						label: '学校简介'
					},
					{
						type: 'majorInfo',
						label: '专业简介'
					},
					{
						type: 'majorPeople',
						label: '专业招生人数'
					},
					{
						type: 'byl',
						label: '专业保研率'
					},
					{
						type: 'kaoyan',
						label: '专业考研情况'
					}
				],

				//就业方向
				careerDirection: [{
						type: 'bk_jiuye',
						label: '专业本科就业路径'
					},
					{
						type: 'ss_jiuye',
						label: '专业硕士就业路径'
					},
					{
						type: 'bs_jiuye',
						label: '专业博士就业路径'
					}
				],
				educationPlanning: [{
						type: 'gh_zzy',
						label: '转专业'
					},
					{
						type: 'gh_baoyan',
						label: '保研'
					},
					{
						type: 'gh_kaoyan',
						label: '考研'
					},
					{
						type: 'gh_liuxue',
						label: '留学'
					}
				],
				academicAbilityEnhancement: [{
						type: 'ts_z_js',
						label: '专业相关竞赛'
					},
					{
						type: 'ts_n_js',
						label: '非专业相关竞赛'
					},
					{
						type: 'ts_z_ky',
						label: '专业相关科研'
					},
					{
						type: 'ts_n_ky',
						label: '可跨专业的相关科研'
					},
					{
						type: 'ts_z_zs',
						label: '专业相关证书'
					},
					{
						type: 'ts_n_zs',
						label: '非专业相关证书'
					}
				],
				universityPlanning: [

				],
				organizationalLife: [{
						type: 'zzsh_sport',
						label: '体育特长',
						key: 'sportsInterest'
					},
					{
						type: 'zzsh_art',
						label: '艺术特长',
						key: 'artInterest'
					},
					{
						type: 'zzsh_qita',
						label: '其他特长',
						key: 'academicInterest'
					},
					{
						type: 'zzsh_zonghe',
						label: '综合描述',
						key: 'collegePlan'
					}
				],
				role: '',
			};
		},
		components: {
			blueTitle,
			userContent,
			userContentTwo
		},
		created() {
			this.role = JSON.parse(uni.getStorageSync('USER-INFO-KEY')).role
			if (this.role == 'user') {
				this.urls = '/my_dxt/report/queryBlock2'
			}
			if (this.role == 'student') {
				this.urls = '/stu/report/queryBlock2'
			}
			console.log(this.reportUserInfo)
			if (this.organizationalLife.filter(item => this.reportUserInfo[item.key] != '' && this.reportUserInfo[item
					.key] != null).length > 0) {
				this.organizationalLife = [{
					type: 'zzsh_zonghe',
					label: '综合描述',
					key: 'collegePlan'
				}]

			} else {
				this.organizationalLife = []
			}
			console.log(this.organizationalLife)
		},
		computed: {
			...mapState('user', ['reportInfo', 'reportUserInfo', "Plan"]),
			...mapState(['eventSourceState'])
		},
		watch: {
			eventSourceState: {
				handler(newval) {
					if (this.timerId != null) {
						clearTimeout(this.timerId)
					}
					this.timerId = setTimeout(() => {
						if (newval.length == 0) {
							this.generating = false
						}
					}, 1500)
				},
				deep: true
			}
		},

		methods: {
			back() {
				const userInfo = JSON.parse(uni.getStorageSync('USER-INFO-KEY'));
				if (userInfo) {
					// 根据角色跳转到对应首页
					const homePage = userInfo.role === 'user' ?
						'/pages/institution/institution' :
						'/pages/admission_report/admission_report'
					uni.reLaunch({
						url: homePage
					})
				}
			},
			// 处理保存按钮点击

			async handleSave() {

				if (this.generating || this.loading) {
					return
				}
				try {
					this.loading = true
					uni.showLoading({

						title: '保存中...'

					})
					let result = {}
					let resultPdf = {}
					if (this.role == 'student') {
						result = await saveReport(this.reportInfo.report.id, this.Plan)
					}
					if (this.role == 'user') {

						result = await saveOrgReport(this.reportInfo.report.id, this.Plan)
						console.log(result)
					}
					console.log(result)
					if (result.errCode == 0) {
						this.loading = true
						uni.showLoading({

							title: '保存中...'

						})
						if (this.role == 'student') {
							resultPdf = await generatePDF(this.reportInfo.report.id)
						}
						if (this.role == 'user') {
							resultPdf = await generateOrgPDF(this.reportInfo.report.id)
						}
						if (resultPdf.errCode == 0) {
							uni.showToast({

								title: '保存成功',

								icon: 'success'

							})
							uni.hideLoading();
						} else {
							uni.showToast({

								title: resultPdf.msg,

								icon: 'error'

							})
						}
					} else {
						uni.showToast({

							title: result.msg,

							icon: 'error'

						})
					}

				} catch (e) {
					console.log(e)
					uni.showToast({

						title: '保存失败',

						icon: 'error'

					})


				} finally {
					this.loading = false
				}

			}

		},

		beforeDestroy() {
			if (this.timerId) {
				clearTimeout(this.timerId)
				this.timerId = null
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding-bottom: 20px;
	}

	.header {
		.header-img {
			width: 100%;
			height: 1070rpx;
		}

		position: relative;

		.teacher-info {
			position: absolute;
			top: 308rpx;
			left: 520rpx;
			width: 200rpx;
			height: 50rpx;
			line-height: 50rpx;
			text-align: center;
			color: #000;
		}
	}

	.content {
		padding-top: 28rpx;
		padding: 0 30rpx;

		.base-info-item-container {
			width: 100%;
			margin-top: 18rpx;
			font-size: $primary-font-size;
			display: flex;
			align-content: space-between;
			flex-wrap: wrap;
			justify-content: flex-start;

			.base-info-item {
				width: 33%;
				display: flex;
				align-items: start;
				margin-right: 10rpx;
				justify-content: flex-start;

				.item-title {
					font-weight: 400;
					font-size: $primary-font-size;
					color: #5A5A5A;
				}

				.item {
					color: #5A5A5A;
				}

				.major {
					width: 80%;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					/* 限制显示行数 */
					-webkit-box-orient: vertical;
					overflow: hidden;
					text-overflow: ellipsis;
					line-height: 25rpx !important;
					margin-top: 10rpx;
				}
			}

			.width-25 {
				width: 28%;
			}


		}

		/*爱好*/
		.hobby {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: flex-start;

		}

		.hobby-item {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			border: 1rpx solid #A0E4C4;
			border-radius: 4rpx;
			padding: 8rpx 12rpx;
			margin-top: 18rpx;
		}

		.hobby-title {
			font-weight: bold;
			font-size: $primary-font-size;
			color: #5A5A5A;
			width: 100rpx;
		}

		.hobby-info {
			font-weight: 400;
			font-size: $primary-font-size;
			color: #5A5A5A;
			margin-left: 14rpx;
			width: 80%;
		}


		.exam-info {
			margin-top: 20rpx;

			.table {
				margin-top: 20rpx;
				height: 48rpx;
				border-radius: 8rpx;
				border: 1rpx solid #1BB394;
				font-size: 12rpx;

				.header,
				.table-line {
					height: 24rpx;
					border-bottom: 1rpx solid #1BB394;
					display: flex;
					align-items: flex-start;
					align-items: center;

					.title,
					.table-line-item {
						height: 24rpx;
						line-height: 24rpx;
						flex: 1;
						border-right: 1rpx solid #1BB394;
						text-align: center;

						&:last-child {
							border: 0;

						}

					}
				}

				.table-line {
					border: 0;
				}
			}
		}


		.university-info {
			margin-top: 32rpx;

			.university-tag {
				margin-top: 18rpx;
				margin-bottom: 32rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.logo {
					width: 70rpx;
					height: 70rpx;
					margin-right: 24rpx;
				}

				.tag {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: flex-start;

					.name {
						font-weight: bold;
						font-size: 20rpx;
						color: #5A5A5A;
						width: 100%;
						text-align: left;
					}

					.tag-list {
						margin-top: 10rpx;
						width: 100%;
						display: flex;
						align-items: center;
						// justify-content: space-between;

						.tag-list-item {
							width: 68rpx;
							height: 30rpx;
							background: #FFB975;
							border-radius: 6rpx;
							font-weight: 400;
							font-size: 14rpx;
							color: #FFFFFF;
							line-height: 30rpx;
							margin-right: 10rpx;
							text-align: center;
						}
					}
				}
			}
		}

		/* 升学规划*/
		.plan,
		.ability,
		.university-plan {
			margin-top: 32rpx;
		}
	}

	.save-btn {

		margin-top: 108rpx;
		width: 550rpx;
		height: 80rpx;
		background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
		border-radius: 40rpx 40rpx 40rpx 40rpx;
		font-weight: bold;
		font-size: 30rpx;
		line-height: 80rpx;
		text-align: center;
		color: #FFFFFF;
	}

	uni-button:after {
		content: none !important;
		border: none !important;
	}

	.transparent-button {
		position: absolute;
		top: 30rpx;
		left: 30rpx;
		z-index: 99999;
		padding: 0;
		margin: 0;
		background: transparent;
		border: none;
		outline: none;
	}
</style>