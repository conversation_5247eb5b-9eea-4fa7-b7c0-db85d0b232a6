<view class="tmp-container data-v-32fd93bb"><view class="{{['container','data-v-32fd93bb',current>1?'container-bg':'']}}"><view class="header data-v-32fd93bb"><view class="top data-v-32fd93bb"><uni-icons class="left-icon data-v-32fd93bb" vue-id="2cb564ab-1" type="left" size="20" data-event-opts="{{[['^tap',[['back']]]]}}" bind:tap="__e" bind:__l="__l"></uni-icons><text class="main-title data-v-32fd93bb">大学生涯规划报告</text></view><view class="alert-msg data-v-32fd93bb"><image class="sound-img data-v-32fd93bb" src="/static/sound.png"></image><text class="info data-v-32fd93bb">请认真完善信息，以便于精准生成报告！</text></view></view><view class="steps data-v-32fd93bb"><view class="step active data-v-32fd93bb">1</view><view class="{{['step-line','data-v-32fd93bb',current>0?'line-bg':'']}}"></view><view class="step active data-v-32fd93bb">2</view><view class="{{['step-line','data-v-32fd93bb',current>1?'line-bg':'']}}"></view><view class="step active data-v-32fd93bb">3</view></view><view class="tip data-v-32fd93bb" style="{{'visibility:'+(showMask||showSearchList?'hidden':'visible')+';'}}"><text class="tip-text data-v-32fd93bb">{{title[current]}}</text></view><swiper class="swiper data-v-32fd93bb" style="{{'height:'+(current==0||current==2?'75vh':'95vh')+';'}}" current="{{current}}" disable-touch="{{true}}"><swiper-item class="data-v-32fd93bb"><view class="form data-v-32fd93bb"><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb"><text class="red data-v-32fd93bb">*</text>学生姓名</text><input class="value data-v-32fd93bb" style="text-align:right;" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',['trim']],['stuInfo']]]],['blur',[['$forceUpdate']]]]}}" value="{{stuInfo.name}}" bindinput="__e" bindblur="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">性别</text><picker mode="selector" range="{{genderOptions}}" data-event-opts="{{[['change',[['onGenderChange',['$event']]]]]}}" bindchange="__e" class="data-v-32fd93bb"><view class="right data-v-32fd93bb"><view class="value data-v-32fd93bb"><block wx:if="{{stuInfo.gender==0}}"><view class="picker data-v-32fd93bb">未知</view></block><block wx:if="{{stuInfo.gender==2}}"><view class="picker data-v-32fd93bb">女</view></block><block wx:if="{{stuInfo.gender==1}}"><view class="picker data-v-32fd93bb">男</view></block></view><uni-icons vue-id="2cb564ab-2" type="right" size="20" color="#C3C3C3" class="data-v-32fd93bb" bind:__l="__l"></uni-icons></view></picker></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb"><text class="red data-v-32fd93bb">*</text>本科入学年份</text><view class="right data-v-32fd93bb"><picker mode="selector" range="{{yearOptions}}" data-event-opts="{{[['change',[['onYearChange',['$event']]]]]}}" bindchange="__e" class="data-v-32fd93bb"><text class="value data-v-32fd93bb">{{stuInfo.joinYear}}</text><uni-icons vue-id="2cb564ab-3" type="right" size="20" color="#C3C3C3" class="data-v-32fd93bb" bind:__l="__l"></uni-icons></picker></view></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb"><text class="red data-v-32fd93bb">*</text>本科院校</text><view data-event-opts="{{[['tap',[['searchSchool',['$event']]]]]}}" class="right data-v-32fd93bb" bindtap="__e"><text class="value data-v-32fd93bb">{{stuInfo.schoolName}}</text><uni-icons vue-id="2cb564ab-4" type="right" size="20" color="#C3C3C3" class="data-v-32fd93bb" bind:__l="__l"></uni-icons></view></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb"><text class="red data-v-32fd93bb">*</text>本科专业</text><view data-event-opts="{{[['tap',[['searchMajor',['$event']]]]]}}" class="right data-v-32fd93bb" bindtap="__e"><text class="value data-v-32fd93bb">{{stuInfo.majorName}}</text><uni-icons vue-id="2cb564ab-5" type="right" size="20" color="#C3C3C3" class="data-v-32fd93bb" bind:__l="__l"></uni-icons></view></view><view class="form-item-box data-v-32fd93bb"><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">本科学院</text><view data-event-opts="{{[['tap',[['searchCollege',['$event']]]]]}}" class="right data-v-32fd93bb" bindtap="__e"><text class="value data-v-32fd93bb">{{stuInfo.collegeNames}}</text><uni-icons vue-id="2cb564ab-6" type="right" size="20" color="#C3C3C3" class="data-v-32fd93bb" bind:__l="__l"></uni-icons></view></view><text data-event-opts="{{[['tap',[['cursorClick',['$event']]]]]}}" class="cursor data-v-32fd93bb" bindtap="__e">点击手动添加本科院校</text></view><block wx:if="{{cursorFlag}}"><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">手动添加本科学院</text><input class="value data-v-32fd93bb" style="text-align:right;" placeholder="请输入本科学院" data-event-opts="{{[['blur',[['collegeBlur',['$event']]]],['input',[['__set_model',['$0','collegeName','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.collegeName}}" bindblur="__e" bindinput="__e"/></view></block><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">学生性格</text><view class="value data-v-32fd93bb"><uni-data-checkbox bind:input="__e" vue-id="2cb564ab-7" mode="button" selectedColor="#1BB394" localdata="{{sex1}}" value="{{stuInfo.personality}}" data-event-opts="{{[['^input',[['__set_model',['$0','personality','$event',[]],['stuInfo']]]]]}}" class="data-v-32fd93bb" bind:__l="__l"></uni-data-checkbox></view></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb"><text class="red data-v-32fd93bb">*</text>职业发展</text><view class="value career data-v-32fd93bb"><uni-data-checkbox bind:input="__e" vue-id="2cb564ab-8" multiple="{{true}}" mode="button" selectedColor="#1BB394" localdata="{{occupationData}}" value="{{stuInfo.postGraduation}}" data-event-opts="{{[['^input',[['__set_model',['$0','postGraduation','$event',[]],['stuInfo']]]]]}}" class="data-v-32fd93bb" bind:__l="__l"></uni-data-checkbox></view></view></view></swiper-item><swiper-item class="data-v-32fd93bb"><view class="form data-v-32fd93bb"><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb"><text class="red data-v-32fd93bb">*</text>总分</text><input class="value data-v-32fd93bb" placeholder="请输入总分(必填)0-750" type="number" data-event-opts="{{[['input',[['__set_model',['$0','totalScore','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.totalScore}}" bindinput="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">位次</text><input class="value data-v-32fd93bb" placeholder="请输入位次(选填)" type="number" data-event-opts="{{[['input',[['__set_model',['$0','position','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.position}}" bindinput="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">语文</text><input class="value data-v-32fd93bb" placeholder="请输入语文分数(选填)0-150" type="number" data-event-opts="{{[['input',[['__set_model',['$0','chineseScore','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.chineseScore}}" bindinput="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb"><text class="red data-v-32fd93bb">*</text>数学</text><input class="value data-v-32fd93bb" placeholder="请输入数学分数(必填)0-150" type="number" data-event-opts="{{[['input',[['__set_model',['$0','mathScore','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.mathScore}}" bindinput="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb"><text class="red data-v-32fd93bb">*</text>外语</text><input class="value data-v-32fd93bb" placeholder="请输入外语分数(必填)0-150" type="number" data-event-opts="{{[['input',[['__set_model',['$0','foreignLangScore','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.foreignLangScore}}" bindinput="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">物理</text><input class="value data-v-32fd93bb" placeholder="请输入物理分数(选填)0-100" type="number" data-event-opts="{{[['input',[['__set_model',['$0','physicsScore','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.physicsScore}}" bindinput="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">化学</text><input class="value data-v-32fd93bb" placeholder="请输入化学分数(选填)0-100" type="number" data-event-opts="{{[['input',[['__set_model',['$0','chemistryScore','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.chemistryScore}}" bindinput="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">生物</text><input class="value data-v-32fd93bb" placeholder="请输入生物分数(选填)0-100" type="number" data-event-opts="{{[['input',[['__set_model',['$0','biologyScore','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.biologyScore}}" bindinput="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">政治</text><input class="value data-v-32fd93bb" placeholder="请输入政治分数(选填)0-100" type="number" data-event-opts="{{[['input',[['__set_model',['$0','politicsScore','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.politicsScore}}" bindinput="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">历史</text><input class="value data-v-32fd93bb" placeholder="请输入历史分数(选填)0-100" type="number" data-event-opts="{{[['input',[['__set_model',['$0','historyScore','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.historyScore}}" bindinput="__e"/></view><view class="form-item data-v-32fd93bb"><text class="label data-v-32fd93bb">地理</text><input class="value data-v-32fd93bb" placeholder="请输入地理分数(选填)0-100" type="number" data-event-opts="{{[['input',[['__set_model',['$0','geographyScore','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.geographyScore}}" bindinput="__e"/></view></view></swiper-item><swiper-item class="data-v-32fd93bb"><view class="form data-v-32fd93bb"><view class="form-item-step-three data-v-32fd93bb"><text class="label data-v-32fd93bb">体育特长：</text><view class="value-container data-v-32fd93bb"><textarea class="value-input data-v-32fd93bb" maxlength="100" placeholder="比如篮球、足球、游泳等" data-event-opts="{{[['input',[['__set_model',['$0','sportsInterest','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.sportsInterest}}" bindinput="__e"></textarea></view></view><view class="form-item-step-three data-v-32fd93bb"><text class="label data-v-32fd93bb">艺术特长：</text><view class="value-container data-v-32fd93bb"><textarea class="value-input data-v-32fd93bb" maxlength="100" placeholder="比如乐器、唱歌、画画、舞蹈等" data-event-opts="{{[['input',[['__set_model',['$0','artInterest','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.artInterest}}" bindinput="__e"></textarea></view></view><view class="form-item-step-three data-v-32fd93bb"><text class="label data-v-32fd93bb">其他特长：</text><view class="value-container data-v-32fd93bb"><textarea class="value-input data-v-32fd93bb" maxlength="100" placeholder="比如英语口语、中英文演讲、编程等" data-event-opts="{{[['input',[['__set_model',['$0','academicInterest','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.academicInterest}}" bindinput="__e"></textarea></view></view><view class="form-item-step-three data-v-32fd93bb"><text class="label data-v-32fd93bb">综合描述：</text><view class="value-container data-v-32fd93bb"><textarea class="value-input data-v-32fd93bb" maxlength="100" placeholder="你想通过大学获得什么？" data-event-opts="{{[['input',[['__set_model',['$0','collegePlan','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.collegePlan}}" bindinput="__e"></textarea></view></view></view></swiper-item></swiper><block wx:if="{{current===0}}"><view class="footers data-v-32fd93bb"><button data-event-opts="{{[['tap',[['next',['$event']]]]]}}" class="next-button data-v-32fd93bb" bindtap="__e">下一步</button></view></block><block wx:else><view class="footer data-v-32fd93bb"><view data-event-opts="{{[['tap',[['prev',['$event']]]]]}}" class="{{['data-v-32fd93bb',current==2?'white':'prev-button']}}" style="width:305rpx;" bindtap="__e">上一步</view><view data-event-opts="{{[['tap',[['next',['$event']]]]]}}" class="next-button data-v-32fd93bb" style="width:305rpx;" bindtap="__e">下一步</view></view></block></view><block wx:if="{{showMask}}"><view data-event-opts="{{[['tap',[['closeMask',['$event']]]]]}}" class="mask data-v-32fd93bb" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="bg-container data-v-32fd93bb" catchtap="__e"><view class="ai3-bg-container data-v-32fd93bb"><view class="{{['ai3-bg','data-v-32fd93bb',(showAnimation)?'ai3-bg-animation':'']}}"></view></view><view class="ai2-bg-container data-v-32fd93bb"><view class="{{['ai2-bg','data-v-32fd93bb',(showAnimation)?'ai2-bg-animation':'']}}"></view></view><view class="ai-bg data-v-32fd93bb"></view></view><button class="submit data-v-32fd93bb" loading="{{showAnimation}}" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">AI生成报告</button></view></block><block wx:if="{{showSearchList}}"><view class="mask data-v-32fd93bb"><search-list vue-id="2cb564ab-9" typeNum="{{typeNum}}" url="{{searchListUrl}}" data-event-opts="{{[['^choose',[['choose']]],['^closeSerachList',[['closeSerachList']]]]}}" bind:choose="__e" bind:closeSerachList="__e" class="data-v-32fd93bb" bind:__l="__l"></search-list></view></block></view>