<view class="content data-v-408d747c"><height vue-id="176ff384-1" hg="{{System_height}}" class="data-v-408d747c" bind:__l="__l"></height><view class="content_header data-v-408d747c"><view class="nav-title data-v-408d747c" style="{{'top:'+(System_height+'rpx')+';'}}"><text class="data-v-408d747c">大学生涯规划报告</text></view><uni-icons class="back-left data-v-408d747c" style="{{'top:'+(System_height+'rpx')+';'}}" vue-id="176ff384-2" type="left" size="24" color="#2D2D2D" data-event-opts="{{[['^tap',[['back']]]]}}" bind:tap="__e" bind:__l="__l"></uni-icons></view><view class="notification data-v-408d747c"><image class="notification-icon data-v-408d747c" src="/static/select_school/notification_icon-56586a.png"></image><text class="notification-text data-v-408d747c">请认真完善信息，以便于精准生成报告！</text></view><view class="step-indicator data-v-408d747c"><view class="step-item data-v-408d747c"><view class="step-number data-v-408d747c">1</view></view><view class="step-item data-v-408d747c"><view class="step-number data-v-408d747c">2</view></view><view class="step-item active data-v-408d747c"><view class="step-number data-v-408d747c">3</view></view></view><view class="form-title data-v-408d747c"><view class="title-bg data-v-408d747c"></view><text class="title-text data-v-408d747c">兴趣类</text></view><view class="ai-section data-v-408d747c"><view class="ai-container data-v-408d747c"><image class="ai-bg-4 data-v-408d747c" src="/static/select_school/ai4-56586a.png"></image><image class="ai-bg-1 data-v-408d747c" src="/static/select_school/ai1-56586a.png"></image><image class="ai-bg-2 data-v-408d747c" src="/static/select_school/ai2-56586a.png"></image><view class="ai-center data-v-408d747c"><image class="ai-main data-v-408d747c" src="/static/select_school/ai-56586a.png"></image><image class="ai-text data-v-408d747c" src="/static/select_school/ai_text-56586a.png"></image></view></view></view><view class="form-content data-v-408d747c"><view class="form-item data-v-408d747c"><view class="form-label data-v-408d747c">体育特长：</view><textarea class="form-textarea data-v-408d747c" placeholder="比如篮球、足球、游泳等" data-event-opts="{{[['input',[['__set_model',['$0','sportsSkills','$event',[]],['formData']]]]]}}" value="{{formData.sportsSkills}}" bindinput="__e"></textarea></view><view class="form-item data-v-408d747c"><view class="form-label data-v-408d747c">艺术特长：</view><textarea class="form-textarea data-v-408d747c" placeholder="比如乐器、唱歌、画画、舞蹈等" data-event-opts="{{[['input',[['__set_model',['$0','artSkills','$event',[]],['formData']]]]]}}" value="{{formData.artSkills}}" bindinput="__e"></textarea></view><view class="form-item data-v-408d747c"><view class="form-label data-v-408d747c">其他特长：</view><textarea class="form-textarea data-v-408d747c" placeholder="比如英语口语、中英文演讲、编程等" data-event-opts="{{[['input',[['__set_model',['$0','otherSkills','$event',[]],['formData']]]]]}}" value="{{formData.otherSkills}}" bindinput="__e"></textarea></view><view class="form-item data-v-408d747c"><view class="form-label data-v-408d747c">综合描述：</view><textarea class="form-textarea data-v-408d747c" placeholder="你想通过大学获得什么？" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],['formData']]]]]}}" value="{{formData.description}}" bindinput="__e"></textarea></view></view><view class="bottom-buttons data-v-408d747c"><view data-event-opts="{{[['tap',[['generateReport',['$event']]]]]}}" class="generate-btn data-v-408d747c" bindtap="__e"><text class="data-v-408d747c">AI生成报告</text></view><view data-event-opts="{{[['tap',[['nextStep',['$event']]]]]}}" class="next-btn data-v-408d747c" bindtap="__e"><text class="data-v-408d747c">下一步</text></view></view></view>