{"version": 3, "sources": ["webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content.vue?5ab0", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content.vue?2610", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content.vue?4243", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content.vue?6746", "uni-app:///components/user_content.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content.vue?b67c", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/user_content.vue?bf82"], "names": ["data", "urls", "role", "props", "msgtype", "type", "default", "isSse", "created", "computed", "methods", "fetchData", "console", "title", "content", "requestUrl", "uni", "item", "params", "reportId", "token", "postGraduation", "queryStr", "map", "join", "abortController", "timeout20s", "responseTimeout", "clearTimeout", "replace", "signal", "againClick", "getData", "result", "arr", "obj", "adviceData", "getOrgData", "studentId"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACoC1nB;AACA;AAIA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA,gBACA;EACAA;IACA;MACAA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;MACA;MACA;MACA;MACA;MACA;MACA;IAEA;EACA;;EACAC,4BACA,0CACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,uCACA;gBAAA;gBAAA;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA;0BACA;4BACAP;4BACAQ;4BACAC;0BACA;0BACAC;0BACA;4BACAA,uBACAC,wEACAC,+CACA,mEACA,mEACA;0BACA;0BACA;4BACAC,yCACA;8BACAC;8BACAd;8BACAe;8BACAC;4BAAA;4BAEAC,+BACAC;8BAAA;4BAAA,GACAC;4BACAT;0BACA;0BAAA;0BAIAU,yCACA;0BACAC;4BACAD;0BACA,WAEA;0BACAE;4BACAF;0BACA;0BAAA;0BAAA,OAEA;4BACA;8BAAA;4BAAA;;4BAEA;4BACAG;4BACAD;8BACAF;4BACA;4BACA;8BACA;gCACAG;8BACA;8BACA;8BACAd;8BACA,8DACAe;4BACA;8BACA;gCACAD;8BACA;8BACA;4BACA;4BAEA,sEACAX,oCACA;0BACA;4BACAa;0BACA;wBAAA;0BAEA;0BACAF;0BACAA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAGA;0BACAhB;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAd;gBACA;gBACA;gBACAF;gBACA;kBACAA,uBACAC,uEACAC,6CACA,kEACA,kEACA;gBACA;gBACA;kBACAC,yCACA;oBACAC;oBACAd;oBACAe;oBACAC;kBAAA;kBAGAC,+BACAC;oBAAA;kBAAA,GACAC;kBACAT;gBACA;gBAAA;gBAGAU,yCAEA;gBACAC;kBACAD;gBACA,WAEA;gBACAE;kBACAF;gBACA;gBAAA;gBAAA,OAEA;kBACA;oBAAA;kBAAA;;kBAEA;kBACAG;kBACAD;oBACAF;kBACA;kBAEA;oBACA;oBACAX;oBACA,+DACAe;kBACA;oBACA;kBACA;kBAEA,uEACAZ,qCACA;gBACA;kBACAa;gBACA;cAAA;gBAEA;gBACAF;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAhC;kBACAmB;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAc;gBACA;kBACAC;kBACAD;oBAEA;sBACA;sBACAE;sBACAA;sBACAA;wBAAA;sBAAA;sBACAD;oBACA;kBAGA;kBACA;kBACAE;kBACAH;oBACA;sBACA;sBACAE;sBACAA;sBACAA;wBAAA;sBAAA;sBACAC;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACArC;kBACAsC;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAL;gBACA;kBACAC;kBACAD;oBAEA;sBACA;sBACAE;sBACAA;sBACAA;wBAAA;sBAAA;sBACAD;oBACA;kBAGA;kBACA;kBACAE;kBACAH;oBACA;sBACA;sBACAE;sBACAA;sBACAA;wBAAA;sBAAA;sBACAC;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC7TA;AAAA;AAAA;AAAA;AAAyqC,CAAgB,+oCAAG,EAAC,C;;;;;;;;;;;ACA7rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/user_content.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./user_content.vue?vue&type=template&id=305ea780&scoped=true&\"\nvar renderjs\nimport script from \"./user_content.vue?vue&type=script&lang=js&\"\nexport * from \"./user_content.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user_content.vue?vue&type=style&index=0&id=305ea780&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"305ea780\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/user_content.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_content.vue?vue&type=template&id=305ea780&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.data, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = !item.content && !_vm.typeList.includes(item.type)\n    var g1 = _vm.typeList.includes(item.type)\n    var g2 = _vm.typeList.includes(item.type)\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n      g2: g2,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_content.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_content.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"info\" v-for=\"(item, index) in data\" :key=\"index\">\r\n\t\t\t<view class=\"info-title\">\r\n\t\t\t\t<image class=\"logo\" src=\"../static/imgs/rhomb.png\" mode=\"\"></image>\r\n\t\t\t\t<text class=\"title-text\">\r\n\t\t\t\t\t{{ item.title }}\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-content\">\r\n\t\t\t\t<rich-text :nodes=\"item.content\"></rich-text>\r\n\t\t\t\t<text class=\"loading\" v-if=\"!item.content&&!typeList.includes(item.type)\"><svg viewBox=\"0 0 36 36\"\r\n\t\t\t\t\t\tversion=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\" data-icon=\"spin\">\r\n\t\t\t\t\t\t<defs>\r\n\t\t\t\t\t\t\t<linearGradient x1=\"0%\" y1=\"100%\" x2=\"100%\" y2=\"100%\" id=\"linearGradient-1\">\r\n\t\t\t\t\t\t\t\t<stop stop-color=\"currentColor\" stop-opacity=\"0\" offset=\"0%\"></stop>\r\n\t\t\t\t\t\t\t\t<stop stop-color=\"currentColor\" stop-opacity=\"0.50\" offset=\"39.9430698%\"></stop>\r\n\t\t\t\t\t\t\t\t<stop stop-color=\"currentColor\" offset=\"100%\"></stop>\r\n\t\t\t\t\t\t\t</linearGradient>\r\n\t\t\t\t\t\t</defs>\r\n\t\t\t\t\t\t<g stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\r\n\t\t\t\t\t\t\t<rect fill-opacity=\"0.01\" fill=\"none\" x=\"0\" y=\"0\" width=\"36\" height=\"36\"></rect>\r\n\t\t\t\t\t\t\t<path\r\n\t\t\t\t\t\t\t\td=\"M34,18 C34,9.163444 26.836556,2 18,2 C11.6597233,2 6.18078805,5.68784135 3.59122325,11.0354951\"\r\n\t\t\t\t\t\t\t\tstroke=\"url(#linearGradient-1)\" stroke-width=\"4\" stroke-linecap=\"round\"></path>\r\n\t\t\t\t\t\t</g>\r\n\t\t\t\t\t</svg></text>\r\n\t\t\t\t<text style=\"color:red;\" v-if=\"typeList.includes(item.type)\"> 服务器繁忙，请重试</text>\r\n\t\t\t\t<view class=\"again\" v-if=\"typeList.includes(item.type)\" @click=\"againClick(item)\">重新生成\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport sseEvent from \"@/utils/sse_event.js\"\r\n\timport {\r\n\t\tgetAdviceList,\r\n\t\tgetOrgAdviceList\r\n\t} from \"@/api/user.js\"\r\n\timport {\r\n\t\tmapState\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdata: [],\r\n\t\t\t\turls: '',\r\n\t\t\t\trole: '',\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tmsgtype: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => []\r\n\t\t\t},\r\n\t\t\tisSse: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// this.role = JSON.parse(uni.getStorageSync('USER-INFO-KEY')).role\r\n\t\t\t// if (this.role == 'user') {\r\n\t\t\t// \tthis.urls = '/my_dxt/report/queryBlock2'\r\n\t\t\t// }\r\n\t\t\t// if (this.role == 'student') {\r\n\t\t\tthis.urls = '/stu/report/queryBlock2'\r\n\t\t\t// }\r\n\t\t\tif (this.isSse) {\r\n\t\t\t\tthis.fetchData()\r\n\t\t\t} else {\r\n\t\t\t\t// if (this.role == 'student') {\r\n\t\t\t\tthis.getData()\r\n\t\t\t\t// } else {\r\n\t\t\t\t// \tthis.getOrgData()\r\n\t\t\t\t// }\r\n\t\t\t\t//普通http 获取数据\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t...mapState('user', ['typeList']),\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync fetchData() {\r\n\t\t\t\tconsole.log(this.$store.state.user.reportInfo)\r\n\t\t\t\tfor (let [index, item] of this.msgtype.entries()) {\r\n\t\t\t\t\tthis.data.push({\r\n\t\t\t\t\t\ttype: item.type,\r\n\t\t\t\t\t\ttitle: item.label,\r\n\t\t\t\t\t\tcontent: ' '\r\n\t\t\t\t\t});\r\n\t\t\t\t\tlet requestUrl = ''\r\n\t\t\t\t\tif (this.role == 'student') {\r\n\t\t\t\t\t\trequestUrl =\r\n\t\t\t\t\t\t\t`${uni.http.baseUrl}${this.urls}?\r\n\t\t\t\t\t\t\t\t\ttype=${item.type}\r\n\t\t\t\t\t\t\t\t\t&token=${this.$store.getters.token}\r\n\t\t\t\t\t\t\t\t\t&reportId=${this.$store.state.user.reportInfo.report.id}\r\n\t\t\t\t\t\t\t\t\t`;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.role == 'user') {\r\n\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\t...this.$store.state.user.reportInfo,\r\n\t\t\t\t\t\t\treportId: this.$store.state.user.reportInfo.report.id,\r\n\t\t\t\t\t\t\ttype: item.type,\r\n\t\t\t\t\t\t\ttoken: this.$store.getters.token,\r\n\t\t\t\t\t\t\tpostGraduation: JSON.stringify(this.$store.state.user.reportInfo.postGraduation),\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tlet queryStr = Object.keys(params)\r\n\t\t\t\t\t\t\t.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))\r\n\t\t\t\t\t\t\t.join('&');\r\n\t\t\t\t\t\trequestUrl = `${uni.http.baseUrl}${this.urls}?${queryStr}`;\r\n\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tconst abortController = new AbortController();\r\n\t\t\t\t\t\t// 20秒总超时\r\n\t\t\t\t\t\tconst timeout20s = setTimeout(() => {\r\n\t\t\t\t\t\t\tabortController.abort();\r\n\t\t\t\t\t\t}, 20000);\r\n\r\n\t\t\t\t\t\t// 10秒无响应中断\r\n\t\t\t\t\t\tlet responseTimeout = setTimeout(() => {\r\n\t\t\t\t\t\t\tabortController.abort();\r\n\t\t\t\t\t\t}, 10000);\r\n\r\n\t\t\t\t\t\tawait sseEvent(requestUrl, item.type, (buffer, eventType) => {\r\n\t\t\t\t\t\t\tconst idx = this.data.findIndex(o => o.type === item.type);\r\n\r\n\t\t\t\t\t\t\t// 每次收到数据重置10秒中断计时器\r\n\t\t\t\t\t\t\tclearTimeout(responseTimeout);\r\n\t\t\t\t\t\t\tresponseTimeout = setTimeout(() => {\r\n\t\t\t\t\t\t\t\tabortController.abort();\r\n\t\t\t\t\t\t\t}, 10000);\r\n\t\t\t\t\t\t\tif (eventType === \"say\") {\r\n\t\t\t\t\t\t\t\tif (timeout20s) {\r\n\t\t\t\t\t\t\t\t\tclearTimeout(timeout20s);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tlet content = this.data[idx].content;\r\n\t\t\t\t\t\t\t\tcontent += buffer;\r\n\t\t\t\t\t\t\t\tthis.data[idx].content = content.replace(/\\n{1,2}/g, \"<br>\")\r\n\t\t\t\t\t\t\t\t\t.replace(/\\*\\*([^*]+)\\*\\*/g, \"<b>$1</b>\");\r\n\t\t\t\t\t\t\t} else if (eventType === \"reply\") {\r\n\t\t\t\t\t\t\t\tif (timeout20s) {\r\n\t\t\t\t\t\t\t\t\tclearTimeout(timeout20s);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthis.data[idx].content = buffer;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tthis.$store.commit('user/setPlan', {\r\n\t\t\t\t\t\t\t\t[item.type]: this.data[idx].content\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\tsignal: abortController.signal\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t// 请求成功完成，清除定时器\r\n\t\t\t\t\t\tclearTimeout(timeout20s);\r\n\t\t\t\t\t\tclearTimeout(responseTimeout);\r\n\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tthis.$store.commit('user/setType', item.type);\r\n\t\t\t\t\t\tconsole.error('请求错误:', error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 点击重试\r\n\t\t\tasync againClick(item) {\r\n\t\t\t\titem.content = ''\r\n\t\t\t\t// \r\n\t\t\t\tthis.$store.commit('user/removeType', item.type)\r\n\t\t\t\tlet requestUrl = ''\r\n\t\t\t\tif (this.role == 'student') {\r\n\t\t\t\t\trequestUrl =\r\n\t\t\t\t\t\t`${uni.http.baseUrl}${this.urls}?\r\n\t\t\t\t\t\t\t\ttype=${item.type}\r\n\t\t\t\t\t\t\t\t&token=${this.$store.getters.token}\r\n\t\t\t\t\t\t\t\t&reportId=${this.$store.state.user.reportInfo.report.id}\r\n\t\t\t\t\t\t\t\t`;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.role == 'user') {\r\n\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t...this.$store.state.user.reportInfo,\r\n\t\t\t\t\t\treportId: this.$store.state.user.reportInfo.report.id,\r\n\t\t\t\t\t\ttype: item.type,\r\n\t\t\t\t\t\ttoken: this.$store.getters.token,\r\n\t\t\t\t\t\tpostGraduation: JSON.stringify(this.$store.state.user.reportInfo.postGraduation),\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlet queryStr = Object.keys(params)\r\n\t\t\t\t\t\t.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))\r\n\t\t\t\t\t\t.join('&');\r\n\t\t\t\t\trequestUrl = `${uni.http.baseUrl}${this.urls}?${queryStr}`;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst abortController = new AbortController();\r\n\r\n\t\t\t\t\t// 20秒总超时\r\n\t\t\t\t\tconst timeout20s = setTimeout(() => {\r\n\t\t\t\t\t\tabortController.abort();\r\n\t\t\t\t\t}, 20000);\r\n\r\n\t\t\t\t\t// 10秒无响应中断\r\n\t\t\t\t\tlet responseTimeout = setTimeout(() => {\r\n\t\t\t\t\t\tabortController.abort();\r\n\t\t\t\t\t}, 10000);\r\n\r\n\t\t\t\t\tawait sseEvent(requestUrl, item.type, (buffer, eventType) => {\r\n\t\t\t\t\t\tconst idx = this.data.findIndex(o => o.type === item.type);\r\n\r\n\t\t\t\t\t\t// 每次收到数据重置10秒中断计时器\r\n\t\t\t\t\t\tclearTimeout(responseTimeout);\r\n\t\t\t\t\t\tresponseTimeout = setTimeout(() => {\r\n\t\t\t\t\t\t\tabortController.abort();\r\n\t\t\t\t\t\t}, 10000);\r\n\r\n\t\t\t\t\t\tif (eventType === \"say\") {\r\n\t\t\t\t\t\t\tlet content = this.data[idx].content;\r\n\t\t\t\t\t\t\tcontent += buffer;\r\n\t\t\t\t\t\t\tthis.data[idx].content = content.replace(/\\n{1,2}/g, \"<br>\")\r\n\t\t\t\t\t\t\t\t.replace(/\\*\\*([^*]+)\\*\\*/g, \"<b>$1</b>\");\r\n\t\t\t\t\t\t} else if (eventType === \"reply\") {\r\n\t\t\t\t\t\t\tthis.data[idx].content = buffer;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tthis.$store.commit('user/setPlan', {\r\n\t\t\t\t\t\t\t[item.type]: this.data[idx].content\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tsignal: abortController.signal\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 请求成功完成，清除定时器\r\n\t\t\t\t\tclearTimeout(timeout20s);\r\n\t\t\t\t\tclearTimeout(responseTimeout);\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tthis.$store.commit('user/setType', item.type);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getData() {\r\n\t\t\t\tconst data = {\r\n\t\t\t\t\treportId: this.$store.state.user.reportInfo.report.id,\r\n\t\t\t\t}\r\n\t\t\t\tconst result = await getAdviceList(data)\r\n\t\t\t\tif (result.errCode == 0) {\r\n\t\t\t\t\tlet arr = []\r\n\t\t\t\t\tresult.data.forEach(item => {\r\n\r\n\t\t\t\t\t\tif (item.children) {\r\n\t\t\t\t\t\t\tlet obj = {}\r\n\t\t\t\t\t\t\tobj.id = item.id\r\n\t\t\t\t\t\t\tobj.title = item.name\r\n\t\t\t\t\t\t\tobj.content = item.children.map(i => i.content).join(\"<br />\");\r\n\t\t\t\t\t\t\tarr.push(obj)\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.data = arr\r\n\t\t\t\t\tlet adviceData = []\r\n\t\t\t\t\tresult.data.forEach(item => {\r\n\t\t\t\t\t\tif (item.children) {\r\n\t\t\t\t\t\t\tlet obj = {}\r\n\t\t\t\t\t\t\tobj.id = item.id\r\n\t\t\t\t\t\t\tobj.title = item.name\r\n\t\t\t\t\t\t\tobj.content = item.children.map(i => i.content).join(\"<br />\");\r\n\t\t\t\t\t\t\tadviceData.push(obj)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$store.commit('user/setPlan', {\r\n\t\t\t\t\t\t'advice': adviceData\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tasync getOrgData() {\r\n\t\t\t\tconst data = {\r\n\t\t\t\t\tstudentId: this.$store.state.user.reportInfo.studentId,\r\n\t\t\t\t}\r\n\t\t\t\tconst result = await getOrgAdviceList(data)\r\n\t\t\t\tif (result.errCode == 0) {\r\n\t\t\t\t\tlet arr = []\r\n\t\t\t\t\tresult.data.forEach(item => {\r\n\r\n\t\t\t\t\t\tif (item.children) {\r\n\t\t\t\t\t\t\tlet obj = {}\r\n\t\t\t\t\t\t\tobj.id = item.id\r\n\t\t\t\t\t\t\tobj.title = item.name\r\n\t\t\t\t\t\t\tobj.content = item.children.map(i => i.content).join(\"<br />\");\r\n\t\t\t\t\t\t\tarr.push(obj)\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.data = arr\r\n\t\t\t\t\tlet adviceData = []\r\n\t\t\t\t\tresult.data.forEach(item => {\r\n\t\t\t\t\t\tif (item.children) {\r\n\t\t\t\t\t\t\tlet obj = {}\r\n\t\t\t\t\t\t\tobj.id = item.id\r\n\t\t\t\t\t\t\tobj.title = item.name\r\n\t\t\t\t\t\t\tobj.content = item.children.map(i => i.content).join(\"<br />\");\r\n\t\t\t\t\t\t\tadviceData.push(obj)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$store.commit('user/setPlan', {\r\n\t\t\t\t\t\t'advice': adviceData\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t\tbackground: linear-gradient(180deg, rgba(203, 242, 224, 0.2) 0%, rgba(255, 255, 255, 0) 20%);\r\n\t\tborder-radius: 14rpx;\r\n\t\tborder: 2rpx dashed #1BB394;\r\n\t\tpadding: 24rpx 28rpx;\r\n\r\n\t\t@keyframes icon-loading {\r\n\t\t\t0% {\r\n\t\t\t\ttransform: rotate(0);\r\n\t\t\t}\r\n\r\n\t\t\t100% {\r\n\t\t\t\ttransform: rotate(360deg);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.loading {\r\n\t\t\tcolor: #909090;\r\n\t\t}\r\n\r\n\t\t.loading svg {\r\n\t\t\twill-change: transform;\r\n\t\t\twidth: 1em;\r\n\t\t\theight: 1em;\r\n\t\t\tanimation: 0.6s linear infinite icon-loading;\r\n\t\t}\r\n\r\n\t\t.again {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\twidth: 100rpx;\r\n\t\t\theight: 35rpx;\r\n\t\t\tline-height: 35rpx;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 16rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tbackground: #1bb394;\r\n\t\t\tcursor: pointer;\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t}\r\n\r\n\t\t.info {\r\n\t\t\tpadding-top: 30rpx;\r\n\r\n\t\t\t&:first-child {\r\n\t\t\t\tpadding-top: 0rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.info-title {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t.logo {\r\n\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.title-text {\r\n\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\tcolor: #4C5370;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.info-content {\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\tcolor: #504E4E;\r\n\t\t\t\tline-height: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_content.vue?vue&type=style&index=0&id=305ea780&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user_content.vue?vue&type=style&index=0&id=305ea780&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557569770\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}