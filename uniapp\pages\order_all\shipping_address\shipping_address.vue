<template>
	<view class="container">
		<view class="content-container">
			<view class="address" v-for="(item,index) in list" :key="index">
				<view class="info" @click="select(item)">
					<view class="user-address">
						<text>{{item.name}} +86 {{item.phone}}</text>
						<template v-if="item.isDefault">


							<text class="tag">默认</text>
							<view class="bg-arrow-down">
								<u-icon name="checkbox-mark" color="#fff" size="28"></u-icon>
							</view>
						</template>
					</view>
					<text class="detail">{{item.addr}}</text>
				</view>
				<view class="opt">
					<text @click.self="editAddress(item)">编辑</text>
					<text @click.self="deleteAddress(item.id)">删除</text>
				</view>
			</view>
			<view style="margin-top: 150rpx;" v-if="list.length<1">
				<u-empty mode="data" :iconSize='150' :textSize='24' text='暂无邮寄地址' icon=""></u-empty>
			</view>
		</view>
		<view class="btn-container">
			<view class="btn" @click='add'>
				新增收货地址
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getShippingAddressList,
		delShippingAddress
	} from '@/api/comm.js'
	export default {
		data() {
			return {
				currentDefault: 3,
				list: [],
				type: 0,
			};
		},
		onLoad(option) {
			if (option.type) {
				this.type = option.type
			}
		},
		onShow() {
			this.getList()
		},
		methods: {
			// 点击选中地址
			select(item) {
				if (this.type == 1) {
					const prevPage = getCurrentPages()[getCurrentPages().length - 2].$vm;

					// 三步确保更新
					this.$set(prevPage, 'addressDetail', item);
					prevPage.$forceUpdate(); // 强制更新视图
					uni.navigateBack({
						success: () => {
							prevPage.$nextTick(() => {
								// 确保DOM已更新
								console.log('已强制刷新页面');
							});
						}
					});
				} else {
					uni.navigateTo({
						url: `/pages/order_all/affirm_order/affirm_order?item=${JSON.stringify(item)}`
					})
				}
			},
			// 点击删除地址
			async deleteAddress(id) {
				const {
					data,
					errCode,
					msg
				} = await delShippingAddress(id)
				if (errCode == 0) {
					uni.showToast({
						title: '删除成功',
						icon: "success"
					})
					this.getList()
				}
			},
			// 点击修改地址
			editAddress(item) {
				uni.navigateTo({
					url: `/pages/order_all/edit_address/edit_address?item=${JSON.stringify(item)}`
				})
			},
			async getList() {
				const {
					data,
					errCode,
					msg
				} = await getShippingAddressList()
				if (errCode == 0) {
					this.list = data
				} else {
					uni.tip(msg)
				}
			},
			add() {
				uni.navigateTo({
					url: "pages/order_all/add_address/add_address"
				})
			},
			del() {

			}
		}
	}
</script>
<style>
	page {
		background: #F6F7FB;
	}
</style>
<style lang="scss" scoped>
	.content-container {
		padding: 30rpx;

		.address {
			margin-top: 36rpx;
			background-color: #fff;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 34rpx 28rpx;
			border-radius: 12rpx;

			.info {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-direction: column;
				text-align: left;

				.user-address {
					height: 40rpx;
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					color: #060606;
					font-weight: bold;
					font-size: 28rpx;

					.tag {
						width: 67rpx;
						height: 40rpx;
						background: #EEFAF6;
						border-radius: 10rpx;
						line-height: 40rpx;
						text-align: center;
						// color: $main-color;
						font-size: 22rpx;
						margin-left: 18rpx;
					}

					.bg-arrow-down {
						width: 30rpx;
						height: 30rpx;
						background: #01997A;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-left: 10rpx;
					}
				}

				text.detail {
					width: 100%;
					margin-top: 14rpx;
					color: #5A5A5A;
					font-weight: bold;
					font-size: 24rpx;
				}
			}

			.opt {
				color: #5A5A5A;
				font-size: 24rpx;
				font-weight: bold;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30%;

				text {
					padding: 0 10rpx;
				}
			}
		}
	}

	.btn-container {
		position: fixed;
		bottom: 0;
		width: 100%;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fff;

		.btn {
			width: 400rpx;
			height: 80rpx;
			background: linear-gradient(268deg, #01997A 0%, #08AB8A 100%);
			border-radius: 40rpx;
			color: #fff;
			font-size: 30rpx;
			line-height: 80rpx;
			text-align: center;
		}
	}
</style>