{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report/report.vue?46e4", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report/report.vue?6311", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report/report.vue?7175", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report/report.vue?6762", "uni-app:///subpkg/report_all/report/report.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report/report.vue?af70", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report/report.vue?5baa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "showAnimation", "showMask", "genderOptions", "yearOptions", "selected<PERSON>ear", "selected<PERSON><PERSON>", "current", "searchListUrl", "showSearchList", "stuInfo", "name", "schoolName", "schoolId", "majorName", "majorId", "collegeId", "collegeNames", "collegeName", "gender", "joinYear", "personality", "postGraduation", "totalScore", "position", "chineseScore", "mathScore", "foreignLangScore", "physicsScore", "chemistryScore", "biologyScore", "politicsScore", "historyScore", "geographyScore", "sportsInterest", "artInterest", "academicInterest", "collegePlan", "sex1", "text", "value", "title", "occupationData", "searchFlag", "cursorFlag", "typeNum", "created", "methods", "closeMask", "college<PERSON>lur", "cursorClick", "getYearOptions", "length", "next", "rank", "prev", "back", "uni", "url", "onGenderChange", "onYearChange", "searchSchool", "searchMajor", "searchCollege", "closeSerachList", "choose", "submit", "filter", "map", "join", "result", "console", "computed", "components", "searchList"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,0VAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC+QpnB;AAGA;AAEA;AAEA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC,QACA,UACA,UACA,MACA;MACAC;QACAH;QACAC;MACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EAEA;MACAG;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CAEA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAC;MACA;QAAA;MAAA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;UACA;UACA;UACA;YACA;UACA;QAEA;QAEA;UACA,oBAaA;YAZA9B;YACA+B;YACA9B;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAE;YACAD;YACAE;;UAGA;UACA;YACA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;;UAEA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;UACA;UACA;QAEA;;QAEA;MACA;IACA;IACAsB;MACA;IACA;IACAC;MACA;MACA;QACAC;UACAC;QACA;MACA;QACA;MACA;IAEA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAEA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;MAAA;MAGA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAEA;gBACA;gBACA;gBACA;;gBAGA,4DACAC;kBAAA;gBAAA,GACAC;kBAAA;gBAAA,GACAC;gBAAA;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACAb;oBACAC;kBACA;kBACA;kBACA;gBACA;kBACAD;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAc;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACAC,4BACA,0CACA;EACAC;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1rBA;AAAA;AAAA;AAAA;AAAmqC,CAAgB,yoCAAG,EAAC,C;;;;;;;;;;;ACAvrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "subpkg/report_all/report/report.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg/report_all/report/report.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./report.vue?vue&type=template&id=32fd93bb&scoped=true&\"\nvar renderjs\nimport script from \"./report.vue?vue&type=script&lang=js&\"\nexport * from \"./report.vue?vue&type=script&lang=js&\"\nimport style0 from \"./report.vue?vue&type=style&index=0&id=32fd93bb&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"32fd93bb\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg/report_all/report/report.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report.vue?vue&type=template&id=32fd93bb&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniDataCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox\" */ \"@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"tmp-container\">\r\n\t\t<view class=\"container\" :class=\"current > 1 ? 'container-bg' : ''\">\r\n\t\t\t<!-- 顶部标题 -->\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<view class=\"top\">\r\n\t\t\t\t\t<uni-icons @tap=\"back\" class=\"left-icon\" type=\"left\" size=\"20\"></uni-icons>\r\n\t\t\t\t\t<text class=\"main-title\">大学生涯规划报告</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"alert-msg\">\r\n\t\t\t\t\t<image class=\"sound-img\" src=\"@/static/sound.png\"></image>\r\n\t\t\t\t\t<text class=\"info\">请认真完善信息，以便于精准生成报告！</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 步骤导航 -->\r\n\t\t\t<view class=\"steps\">\r\n\t\t\t\t<view class=\"step active\">1</view>\r\n\t\t\t\t<view class=\"step-line\" :class=\"current > 0 ? 'line-bg' : ''\"></view>\r\n\t\t\t\t<view class=\"step active \">2</view>\r\n\t\t\t\t<view class=\"step-line\" :class=\"current > 1 ? 'line-bg' : ''\"></view>\r\n\t\t\t\t<view class=\"step active\">3</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"tip\" :style=\"{ visibility: showMask || showSearchList ? 'hidden' : 'visible' }\">\r\n\t\t\t\t<text class=\"tip-text\">{{ title[current] }}</text>\r\n\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t<!-- 表单内容 -->\r\n\t\t\t<swiper class=\"swiper\" :current=\"current\"\r\n\t\t\t\t:style='{ height: (current == 0 || current == 2) ? \"75vh\" : \"95vh\" }' :disable-touch=\"true\">\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<view class=\"form\">\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\"><text class=\"red\">*</text>学生姓名</text>\r\n\t\t\t\t\t\t\t<input class=\"value\" style=\"text-align: right;\" v-model.trim=\"stuInfo.name\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入姓名\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">性别</text>\r\n\t\t\t\t\t\t\t<picker mode=\"selector\" :range=\"genderOptions\" @change=\"onGenderChange\">\r\n\t\t\t\t\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"value\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"stuInfo.gender ==0\">\r\n\t\t\t\t\t\t\t\t\t\t\t未知\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"stuInfo.gender == 2\">\r\n\t\t\t\t\t\t\t\t\t\t\t女\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"picker\" v-if=\"stuInfo.gender == 1\">\r\n\t\t\t\t\t\t\t\t\t\t\t男\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"20\" color=\"#C3C3C3\"></uni-icons>\r\n\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\"><text class=\"red\">*</text>本科入学年份</text>\r\n\t\t\t\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t\t\t\t<picker mode=\"selector\" :range=\"yearOptions\" @change=\"onYearChange\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"value\">{{ stuInfo.joinYear }}</text>\r\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"20\" color=\"#C3C3C3\"></uni-icons>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\"><text class=\"red\">*</text>本科院校</text>\r\n\t\t\t\t\t\t\t<view class=\"right\" @click=\"searchSchool\">\r\n\t\t\t\t\t\t\t\t<text class=\"value\">{{ stuInfo.schoolName }}</text>\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"20\" color=\"#C3C3C3\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\"><text class=\"red\">*</text>本科专业</text>\r\n\t\t\t\t\t\t\t<view class=\"right\" @click=\"searchMajor\">\r\n\t\t\t\t\t\t\t\t<text class=\"value\">{{ stuInfo.majorName }}</text>\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"20\" color=\"#C3C3C3\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item-box\">\r\n\t\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"label\">本科学院</text>\r\n\t\t\t\t\t\t\t\t<view class=\"right\" @click=\"searchCollege\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"value\">{{ stuInfo.collegeNames }}</text>\r\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"20\" color=\"#C3C3C3\"></uni-icons>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"cursor\" @click=\"cursorClick\">点击手动添加本科院校</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\" v-if=\"cursorFlag\">\r\n\t\t\t\t\t\t\t<text class=\"label\">手动添加本科学院</text>\r\n\t\t\t\t\t\t\t<input class=\"value\" style=\"text-align: right;\" @blur=\"collegeBlur\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.collegeName\" placeholder=\"请输入本科学院\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">学生性格</text>\r\n\t\t\t\t\t\t\t<view class=\"value\">\r\n\t\t\t\t\t\t\t\t<uni-data-checkbox v-model=\"stuInfo.personality\" mode=\"button\" selectedColor=\"#1BB394\"\r\n\t\t\t\t\t\t\t\t\t:localdata=\"sex1\"></uni-data-checkbox>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\"><text class=\"red\">*</text>职业发展</text>\r\n\t\t\t\t\t\t\t<view class=\"value career\">\r\n\t\t\t\t\t\t\t\t<uni-data-checkbox :multiple=\"true\" v-model.trim=\"stuInfo.postGraduation\" mode=\"button\"\r\n\t\t\t\t\t\t\t\t\tselectedColor=\"#1BB394\" :localdata=\"occupationData\"></uni-data-checkbox>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\r\n\t\t\t\t\t<view class=\"form\">\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\"><text class=\"red\">*</text>总分</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入总分(必填)0-750\" type=\"number\" class=\"value\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.totalScore\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">位次</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入位次(选填)\" type=\"number\" class=\"value\" v-model=\"stuInfo.position\" />\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">语文</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入语文分数(选填)0-150\" type=\"number\" class=\"value\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.chineseScore\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\"><text class=\"red\">*</text>数学</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入数学分数(必填)0-150\" type=\"number\" class=\"value\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.mathScore\" />\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\"><text class=\"red\">*</text>外语</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入外语分数(必填)0-150\" type=\"number\" class=\"value\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.foreignLangScore\" />\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">物理</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入物理分数(选填)0-100\" type=\"number\" class=\"value\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.physicsScore\" />\r\n\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">化学</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入化学分数(选填)0-100\" type=\"number\" class=\"value\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.chemistryScore\" />\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">生物</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入生物分数(选填)0-100\" type=\"number\" class=\"value\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.biologyScore\" />\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">政治</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入政治分数(选填)0-100\" type=\"number\" class=\"value\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.politicsScore\" />\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">历史</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入历史分数(选填)0-100\" type=\"number\" class=\"value\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.historyScore\" />\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t\t<text class=\"label\">地理</text>\r\n\t\t\t\t\t\t\t<input placeholder=\"请输入地理分数(选填)0-100\" type=\"number\" class=\"value\"\r\n\t\t\t\t\t\t\t\tv-model=\"stuInfo.geographyScore\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</swiper-item>\r\n\r\n\r\n\t\t\t\t<swiper-item>\r\n\r\n\t\t\t\t\t<view class=\"form\">\r\n\t\t\t\t\t\t<view class=\"form-item-step-three\">\r\n\t\t\t\t\t\t\t<text class=\"label\">体育特长：</text>\r\n\t\t\t\t\t\t\t<view class=\"value-container\">\r\n\t\t\t\t\t\t\t\t<textarea maxlength='100' v-model=\"stuInfo.sportsInterest\" class=\"value-input\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"比如篮球、足球、游泳等\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item-step-three\">\r\n\t\t\t\t\t\t\t<text class=\"label\">艺术特长：</text>\r\n\t\t\t\t\t\t\t<view class=\"value-container\">\r\n\t\t\t\t\t\t\t\t<textarea maxlength='100' v-model=\"stuInfo.artInterest\" class=\"value-input\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"比如乐器、唱歌、画画、舞蹈等\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"form-item-step-three\">\r\n\t\t\t\t\t\t\t<text class=\"label\">其他特长：</text>\r\n\t\t\t\t\t\t\t<view class=\"value-container\">\r\n\t\t\t\t\t\t\t\t<textarea maxlength='100' v-model=\"stuInfo.academicInterest\" class=\"value-input\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"比如英语口语、中英文演讲、编程等\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"form-item-step-three\">\r\n\t\t\t\t\t\t\t<text class=\"label\">综合描述：</text>\r\n\t\t\t\t\t\t\t<view class=\"value-container\">\r\n\t\t\t\t\t\t\t\t<textarea maxlength='100' v-model=\"stuInfo.collegePlan\" class=\"value-input\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"你想通过大学获得什么？\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</swiper-item>\r\n\r\n\r\n\r\n\t\t\t</swiper>\r\n\t\t\t<!-- 底部按钮 -->\r\n\t\t\t<view class=\"footers\" v-if=\"current === 0\">\r\n\t\t\t\t<button class=\"next-button\" @click=\"next\">下一步</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"footer\" v-else>\r\n\t\t\t\t<view style=\"width: 305rpx;\" :class=\"[current==2 ? 'white' : 'prev-button']\" @click=\"prev\">上一步</view>\r\n\t\t\t\t<view style=\"width: 305rpx;\" class=\"next-button\" @click=\"next\">下一步</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t</view>\r\n\r\n\t\t<view class=\"mask\" v-if=\"showMask\" @click=\"closeMask\">\r\n\r\n\t\t\t<view class=\"bg-container\" @click.stop>\r\n\t\t\t\t<view class=\"ai3-bg-container\">\r\n\t\t\t\t\t<view class=\"ai3-bg\" :class='{ \"ai3-bg-animation\": showAnimation }'>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"ai2-bg-container\">\r\n\t\t\t\t\t<view class=\"ai2-bg\" :class='{ \"ai2-bg-animation\": showAnimation }'>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"ai-bg\">\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<button :loading='showAnimation' class=\"submit\" @click=\"submit\">\r\n\t\t\t\tAI生成报告\r\n\t\t\t</button>\r\n\r\n\t\t</view>\r\n\r\n\t\t<view class=\"mask\" v-if=\"showSearchList\">\r\n\t\t\t<search-list :typeNum=\"typeNum\" @choose='choose' @closeSerachList='closeSerachList'\r\n\t\t\t\t:url=\"searchListUrl\"></search-list>\r\n\t\t</view>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState\r\n\t} from \"vuex\"\r\n\timport moment from \"moment\"\r\n\timport searchList from \"@/components/search_list/search_list.vue\";\r\n\timport {\r\n\t\tprepareReport\r\n\t} from \"@/api/user.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowAnimation: false,\r\n\t\t\t\tshowMask: false,\r\n\t\t\t\tgenderOptions: ['未知', '男', '女'],\r\n\t\t\t\tyearOptions: this.getYearOptions(),\r\n\t\t\t\tselectedYear: moment().year(),\r\n\t\t\t\tselectedGender: '',\r\n\t\t\t\tcurrent: 0,\r\n\t\t\t\tsearchListUrl: '',\r\n\t\t\t\tshowSearchList: false,\r\n\t\t\t\tstuInfo: {\r\n\t\t\t\t\tname: \"\",\r\n\t\t\t\t\tschoolName: '请选择本科院校',\r\n\t\t\t\t\tschoolId: 0,\r\n\t\t\t\t\tmajorName: '请选择专业',\r\n\t\t\t\t\tmajorId: 0,\r\n\t\t\t\t\tcollegeId: '',\r\n\t\t\t\t\tcollegeNames: '请选择本科学院',\r\n\t\t\t\t\tcollegeName: '',\r\n\t\t\t\t\tgender: 0,\r\n\t\t\t\t\tjoinYear: '',\r\n\t\t\t\t\tpersonality: 0,\r\n\t\t\t\t\tpostGraduation: [],\r\n\t\t\t\t\ttotalScore: \"\",\r\n\t\t\t\t\t// rank: \"\",\r\n\t\t\t\t\tposition: \"\",\r\n\t\t\t\t\tchineseScore: \"\",\r\n\t\t\t\t\tmathScore: \"\",\r\n\t\t\t\t\tforeignLangScore: \"\",\r\n\t\t\t\t\tphysicsScore: \"\",\r\n\t\t\t\t\tchemistryScore: \"\",\r\n\t\t\t\t\tbiologyScore: \"\",\r\n\t\t\t\t\tpoliticsScore: '',\r\n\t\t\t\t\thistoryScore: \"\",\r\n\t\t\t\t\tgeographyScore: \"\",\r\n\t\t\t\t\tsportsInterest: '',\r\n\t\t\t\t\tartInterest: '',\r\n\t\t\t\t\tacademicInterest: '',\r\n\t\t\t\t\tcollegePlan: ''\r\n\t\t\t\t},\r\n\t\t\t\tsex1: [{\r\n\t\t\t\t\t\ttext: '外向',\r\n\t\t\t\t\t\tvalue: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '内向',\r\n\t\t\t\t\t\tvalue: 2\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\ttitle: [\r\n\t\t\t\t\t'个人基础信息',\r\n\t\t\t\t\t'高考成绩信息',\r\n\t\t\t\t\t'兴趣类'\r\n\t\t\t\t],\r\n\t\t\t\toccupationData: [{\r\n\t\t\t\t\t\ttext: '保研',\r\n\t\t\t\t\t\tvalue: \"1\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ttext: '考研',\r\n\t\t\t\t\t\tvalue: \"2\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '留学',\r\n\t\t\t\t\t\tvalue: \"3\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '读博',\r\n\t\t\t\t\t\tvalue: \"4\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '就业',\r\n\t\t\t\t\t\tvalue: \"5\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '创业',\r\n\t\t\t\t\t\tvalue: \"6\",\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t],\r\n\t\t\t\tsearchFlag: '',\r\n\t\t\t\tcursorFlag: false,\r\n\t\t\t\ttypeNum: 1, //1是院校  2是专业  3是学院\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// if (this.$store.state.user.userInfo.name) {\r\n\t\t\t// \tthis.stuInfo = {\r\n\t\t\t// \t\t...this.$store.state.user.userInfo\r\n\t\t\t// \t}\r\n\t\t\t// }\r\n\t\t\t// let formData = this.$store.state.user.userInfo\r\n\t\t\t// for (const key in this.$store.state.user.userInfo) {\r\n\t\t\t// \tif (formData[key] != null && formData[key] != undefined) {\r\n\t\t\t// \t\tthis.stuInfo[key] = formData[key]\r\n\t\t\t// \t}\r\n\t\t\t// \tthis.stuInfo.collegeNames = formData.collegeName\r\n\t\t\t// \tthis.stuInfo.collegeName = ''\r\n\t\t\t// }\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tcloseMask() {\r\n\t\t\t\tthis.showMask = false;\r\n\t\t\t},\r\n\t\t\t// 手动输入本科学院输入框失去焦点\r\n\t\t\tcollegeBlur() {\r\n\t\t\t\tif (this.stuInfo.collegeName) {\r\n\t\t\t\t\tthis.stuInfo.collegeId = ''\r\n\t\t\t\t\tthis.stuInfo.collegeNames = '请选择本科学院'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 点击手动添加本科学院\r\n\t\t\tcursorClick() {\r\n\t\t\t\tthis.cursorFlag = !this.cursorFlag\r\n\t\t\t},\r\n\t\t\tgetYearOptions() {\r\n\t\t\t\tconst currentYear = new Date().getFullYear();\r\n\t\t\t\treturn Array.from({\r\n\t\t\t\t\tlength: 5\r\n\t\t\t\t}, (_, index) => currentYear - index + '');\r\n\t\t\t},\r\n\t\t\tnext() {\r\n\t\t\t\tif (this.current == 2) {\r\n\t\t\t\t\t// const {sportsInterest, artInterest, academicInterest, collegePlan} = this.stuInfo\r\n\t\t\t\t\t// const validateTextLength = (text) => {\r\n\t\t\t\t\t//   return text !== \"\" && text.length <= 100;\r\n\t\t\t\t\t// };\r\n\r\n\t\t\t\t\t// if (!validateTextLength(sportsInterest)) {\r\n\t\t\t\t\t//   return uni.tip(\"体育特长必须填写且不超过100个字符\");\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// if (!validateTextLength(artInterest)) {\r\n\t\t\t\t\t//   return uni.tip(\"艺术特长必须填写且不超过100个字符\");\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// if (!validateTextLength(academicInterest)) {\r\n\t\t\t\t\t//   return uni.tip(\"学术/其他特长必须填写且不超过100个字符\");\r\n\t\t\t\t\t// }\r\n\t\t\t\t\t// if (!validateTextLength(collegePlan)) {\r\n\t\t\t\t\t//   return uni.tip(\"综合描述必须填写且不超过100个字符\");\r\n\t\t\t\t\t// }\r\n\t\t\t\t\tthis.showMask = true\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (this.current == 0) {\r\n\t\t\t\t\t\tif (this.stuInfo.name == \"\") {\r\n\t\t\t\t\t\t\treturn uni.tip(\"姓名不能为空\")\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.stuInfo.gender == -1) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"请选择性别\")\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.stuInfo.joinYear == \"\") {\r\n\t\t\t\t\t\t\treturn uni.tip(\"请选择入学年份\")\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.stuInfo.schoolId == 0) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"请选择院校\")\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.stuInfo.majorId == 0) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"请选择专业\")\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// if (this.stuInfo.personality == 0) {\r\n\t\t\t\t\t\t// \treturn uni.tip(\"请选择性格\")\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t\tif (this.stuInfo.postGraduation.length == 0) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"请选择职业发展\")\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (this.current == 1) {\r\n\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\ttotalScore,\r\n\t\t\t\t\t\t\trank,\r\n\t\t\t\t\t\t\tposition,\r\n\t\t\t\t\t\t\tchineseScore,\r\n\t\t\t\t\t\t\tmathScore,\r\n\t\t\t\t\t\t\tforeignLangScore,\r\n\t\t\t\t\t\t\tphysicsScore,\r\n\t\t\t\t\t\t\tchemistryScore,\r\n\t\t\t\t\t\t\tbiologyScore,\r\n\t\t\t\t\t\t\thistoryScore,\r\n\t\t\t\t\t\t\tpoliticsScore,\r\n\t\t\t\t\t\t\tgeographyScore\r\n\t\t\t\t\t\t} = this.stuInfo;\r\n\r\n\t\t\t\t\t\t// 验证总分\r\n\t\t\t\t\t\tif (totalScore == \"\" || totalScore < 0 || totalScore > 750) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"请输入正确的总分\");\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 验证必填项：rank, position, chineseScore, mathScore, foreignLangScore\r\n\t\t\t\t\t\t// if (position <= 0) {\r\n\t\t\t\t\t\t// \treturn uni.tip(\"位次必须大于0\");\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t\t// if (chineseScore == \"\" || chineseScore < 0 || chineseScore > 150) {\r\n\t\t\t\t\t\t// \treturn uni.tip(\"语文成绩必须在0到150之间\");\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t\tif (mathScore == \"\" || mathScore < 0 || mathScore > 150) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"数学成绩必须在0到150之间\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (foreignLangScore == \"\" || foreignLangScore < 0 || foreignLangScore > 150) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"外语成绩必须在0到150之间\");\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 验证选填科目（如果填写了分数）\r\n\t\t\t\t\t\tconst validateScore = (score) => {\r\n\t\t\t\t\t\t\treturn score === \"\" || (score >= 0 && score <= 100);\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tconst chineseValidate = (score) => {\r\n\t\t\t\t\t\t\treturn score === \"\" || (score >= 0 && score <= 150);\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tif (position !== \"\" && position < 0) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"位次必须大于0\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (chineseScore !== \"\" && !chineseValidate(chineseScore)) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"语文成绩应在0到150之间\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (physicsScore !== \"\" && !validateScore(physicsScore)) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"物理成绩应在0到100之间\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (chemistryScore !== \"\" && !validateScore(chemistryScore)) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"化学成绩应在0到100之间\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (biologyScore !== \"\" && !validateScore(biologyScore)) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"生物成绩应在0到100之间\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (politicsScore !== \"\" && !validateScore(politicsScore)) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"政治成绩应在0到100之间\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (historyScore !== \"\" && !validateScore(historyScore)) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"历史成绩应在0到100之间\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (geographyScore !== \"\" && !validateScore(geographyScore)) {\r\n\t\t\t\t\t\t\treturn uni.tip(\"地理成绩应在0到100之间\");\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// // 计算所有分数的总和\r\n\t\t\t\t\t\t// let totalCalculatedScore = 0;\r\n\t\t\t\t\t\t// totalCalculatedScore += chineseScore ? parseInt(chineseScore * 10) : 0;\r\n\t\t\t\t\t\t// totalCalculatedScore += mathScore ? parseInt(mathScore * 10) : 0;\r\n\t\t\t\t\t\t// totalCalculatedScore += foreignLangScore ? parseInt(foreignLangScore * 10) : 0;\r\n\t\t\t\t\t\t// totalCalculatedScore += physicsScore ? parseInt(physicsScore * 10) : 0;\r\n\t\t\t\t\t\t// totalCalculatedScore += chemistryScore ? parseInt(chemistryScore * 10) : 0;\r\n\t\t\t\t\t\t// totalCalculatedScore += biologyScore ? parseInt(biologyScore * 10) : 0;\r\n\t\t\t\t\t\t// totalCalculatedScore += historyScore ? parseInt(historyScore * 10) : 0;\r\n\t\t\t\t\t\t// totalCalculatedScore += geographyScore ? parseInt(geographyScore * 10) : 0;\r\n\r\n\t\t\t\t\t\t// // 验证总分是否匹配\r\n\t\t\t\t\t\t// if (totalCalculatedScore != totalScore * 10) {\r\n\t\t\t\t\t\t// \treturn uni.tip(\"各科成绩的总和应等于总分\");\r\n\t\t\t\t\t\t// }\r\n\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthis.current = this.current + 1 > 2 ? 2 : this.current + 1\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tprev() {\r\n\t\t\t\tthis.current = this.current - 1 < 0 ? 0 : this.current - 1\r\n\t\t\t},\r\n\t\t\tback() {\r\n\t\t\t\t// uni.navigateBack()\r\n\t\t\t\tif (this.current == 0) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/subpkg/report_all/index/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.current = this.current - 1 < 0 ? 0 : this.current - 1\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\tonGenderChange(e) {\r\n\t\t\t\t//更新选择的性别\r\n\t\t\t\tthis.stuInfo.gender = e.detail.value\r\n\t\t\t},\r\n\t\t\tonYearChange(e) {\r\n\t\t\t\tthis.stuInfo.joinYear = this.yearOptions[e.detail.value]\r\n\t\t\t},\r\n\t\t\tsearchSchool() {\r\n\t\t\t\tthis.typeNum = 1\r\n\t\t\t\tthis.showSearchList = true\r\n\t\t\t\tthis.searchFlag = 'school'\r\n\t\t\t\tthis.searchListUrl = '/stu/school/fetchBy'\r\n\t\t\t},\r\n\t\t\tsearchMajor() {\r\n\t\t\t\tif (this.stuInfo.schoolId == 0) {\r\n\t\t\t\t\treturn uni.tip(\"请先选择院校\")\r\n\t\t\t\t}\r\n\t\t\t\tthis.typeNum = 2\r\n\t\t\t\tthis.showSearchList = true\r\n\t\t\t\tthis.searchFlag = 'major'\r\n\t\t\t\tthis.searchListUrl = '/stu/school/fetchMajorBy?schoolId=' + this.stuInfo.schoolId\r\n\t\t\t},\r\n\t\t\tsearchCollege() {\r\n\r\n\t\t\t\tif (this.stuInfo.schoolId == 0) {\r\n\t\t\t\t\treturn uni.tip(\"请先选择院校\")\r\n\t\t\t\t}\r\n\t\t\t\tthis.typeNum = 3\r\n\t\t\t\tthis.showSearchList = true\r\n\t\t\t\tthis.searchFlag = 'college'\r\n\t\t\t\tthis.searchListUrl = '/stu/school/fetchCollegeBy?schoolId=' + this.stuInfo.schoolId\r\n\t\t\t},\r\n\t\t\tcloseSerachList() {\r\n\t\t\t\tthis.showSearchList = false\r\n\t\t\t\tthis.searchFlag = ''\r\n\t\t\t\tthis.searchListUrl = ''\r\n\t\t\t},\r\n\t\t\tchoose(item) {\r\n\t\t\t\tswitch (this.searchFlag) {\r\n\t\t\t\t\tcase 'school':\r\n\t\t\t\t\t\tthis.stuInfo.schoolName = item.name\r\n\t\t\t\t\t\tthis.stuInfo.schoolId = item.id\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'major':\r\n\t\t\t\t\t\tthis.stuInfo.majorName = item.name\r\n\t\t\t\t\t\tthis.stuInfo.majorId = item.id\r\n\t\t\t\t\t\t// const url = '/stu/school/fetchCollegeBy?schoolId=' + this.stuInfo.schoolId;\r\n\t\t\t\t\t\t// uni.http.get(url).then(res => {\r\n\t\t\t\t\t\t// \tif (res.errCode == 0 && typeof res.data.data[0] != 'undefined') {\r\n\t\t\t\t\t\t// \t\tthis.stuInfo.collegeId = res.data.data[0].id\r\n\t\t\t\t\t\t// \t\tthis.stuInfo.collegeName = res.data.data[0].name\r\n\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'college':\r\n\t\t\t\t\t\tthis.stuInfo.collegeNames = item.name\r\n\t\t\t\t\t\tthis.stuInfo.collegeId = item.id\r\n\t\t\t\t\t\tthis.stuInfo.collegeName = ''\r\n\t\t\t\t\t\t// const url = '/stu/school/fetchCollegeBy?schoolId=' + this.stuInfo.schoolId;\r\n\t\t\t\t\t\t// uni.http.get(this.searchListUrl).then(res => {\r\n\t\t\t\t\t\t// \tif (res.errCode == 0 && typeof res.data.data[0] != 'undefined') {\r\n\t\t\t\t\t\t// \t\tthis.stuInfo.collegeId = res.data.data[0].id\r\n\t\t\t\t\t\t// \t\tthis.stuInfo.collegeName = res.data.data[0].name\r\n\t\t\t\t\t\t// \t}\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.closeSerachList()\r\n\t\t\t},\r\n\t\t\tasync submit() {\r\n\r\n\t\t\t\t//测试代码，正式生成替换成 this.stuInfo\r\n\t\t\t\t// const data = {\r\n\t\t\t\t// \t\"name\": \"陈测试\",\r\n\t\t\t\t// \t\"schoolName\": \"合肥工业大学\",\r\n\t\t\t\t// \t\"schoolId\": 71,\r\n\t\t\t\t// \t\"majorName\": \"金融工程\",\r\n\t\t\t\t// \t\"majorId\": 249,\r\n\t\t\t\t// \t\"collegeId\": 1313,\r\n\t\t\t\t// \t\"collegeName\": \"经济学院\",\r\n\t\t\t\t// \t\"gender\": 1,\r\n\t\t\t\t// \t\"joinYear\": 2026,\r\n\t\t\t\t// \t\"personality\": 2,\r\n\t\t\t\t// \t\"postGraduation\": [\r\n\t\t\t\t// \t\t3,\r\n\t\t\t\t// \t\t6\r\n\t\t\t\t// \t],\r\n\t\t\t\t// \t\"totalScore\": \"600\",\r\n\t\t\t\t// \t\"rank\": \"15000\",\r\n\t\t\t\t// \t\"position\": \"11000\",\r\n\t\t\t\t// \t\"chineseScore\": \"120\",\r\n\t\t\t\t// \t\"mathScore\": \"120\",\r\n\t\t\t\t// \t\"foreignLangScore\": \"120\",\r\n\t\t\t\t// \t\"physicsScore\": \"80\",\r\n\t\t\t\t// \t\"chemistryScore\": \"80\",\r\n\t\t\t\t// \t\"biologyScore\": \"80\",\r\n\t\t\t\t// \t\"politicsScore\": \"\",\r\n\t\t\t\t// \t\"historyScore\": \"\",\r\n\t\t\t\t// \t\"geographyScore\": \"\",\r\n\t\t\t\t// \t\"sportsInterest\": \"篮球\",\r\n\t\t\t\t// \t\"artInterest\": \"唱歌\",\r\n\t\t\t\t// \t\"academicInterest\": \"没有\",\r\n\t\t\t\t// \t\"collegePlan\": \"有留学的想法，想在大学时候开始准备雅思，为了留学做打算，同时注重大学期间的基础考试， 类似四级、六级.\"\r\n\t\t\t\t// }\r\n\r\n\t\t\t\t// data['postGraduationLabel'] = this.occupationData\r\n\t\t\t\t// \t.filter(item => data.postGraduation.includes(item.value))\r\n\t\t\t\t// \t.map(item => item.label)\r\n\t\t\t\t// \t.join(',')\r\n\r\n\r\n\t\t\t\tthis.stuInfo['postGraduationLabel'] = this.occupationData\r\n\t\t\t\t\t.filter(item => this.stuInfo.postGraduation.includes(item.value))\r\n\t\t\t\t\t.map(item => item.text)\r\n\t\t\t\t\t.join(',')\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.showAnimation = true\r\n\t\t\t\t\tconst result = await prepareReport(this.stuInfo)\r\n\t\t\t\t\t// const result = await prepareReport(data)\r\n\t\t\t\t\tif (result.errCode == 0) {\r\n\t\t\t\t\t\t//记录报告信息\r\n\t\t\t\t\t\tthis.$store.commit('user/setReportInfo', result.data);\r\n\t\t\t\t\t\t//记录报告的用户信息\r\n\t\t\t\t\t\tthis.$store.commit('user/setReportUserInfo', this.stuInfo);\r\n\t\t\t\t\t\t// this.$store.commit('user/setReportUserInfo', data);\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/subpkg/report_all/plan/plan'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.$store.dispatch('user/getReportData')\r\n\t\t\t\t\t\tthis.showMask = false\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.tip(result.msg)\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error(e)\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.showAnimation = false\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t...mapState('user', ['userInfo'])\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\tsearchList\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t/* 容器样式 */\r\n\t.container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbackground: linear-gradient(180deg, #CBF2E0 0%, #EEF7F6 30%, #FFFFFF 61%, #FFFFFF 100%);\r\n\t\theight: 100%;\r\n\t\tpadding: 0 32rpx;\r\n\t\tmin-height: 100vh;\r\n\t\tpadding-bottom: 20px;\r\n\t}\r\n\r\n\t.container-bg {\r\n\t\tbackground: linear-gradient(180deg, #CBF2E0 0%, #EEF7F6 30%, #F6F7FB 100%);\r\n\t}\r\n\r\n\t.swiper {\r\n\t\theight: 68vh;\r\n\t}\r\n\r\n\t/* 顶部标题样式 */\r\n\t.header {\r\n\t\tmargin-top: 70rpx;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.top {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.left-icon {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t}\r\n\r\n\t.alert-msg {\r\n\t\tmargin-top: 28rpx;\r\n\t\tpadding: 10rpx 0;\r\n\t\tpadding-left: 20rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbackground-color: #F5FFFD;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-start;\r\n\t\tfont-weight: 400;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #5A5A5A;\r\n\r\n\t\t.info {\r\n\t\t\tmargin-left: 4rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.sound-img {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t}\r\n\r\n\t.main-title {\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 34rpx;\r\n\t\tcolor: #2D2D2D;\r\n\t}\r\n\r\n\t.subtitle {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t/* 步骤导航样式 */\r\n\t.steps {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-start;\r\n\t}\r\n\r\n\t.tip {\r\n\t\tmargin-top: 36rpx;\r\n\r\n\t\t.tip-text {\r\n\t\t\tposition: relative;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tfont-weight: 800;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #060606;\r\n\t\t\tz-index: 100;\r\n\t\t}\r\n\r\n\t\t.tip-text::after {\r\n\t\t\tcontent: \"\";\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: -6rpx;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 20rpx;\r\n\t\t\t/* 指定高度 */\r\n\t\t\tbackground-color: #DBFF9C;\r\n\t\t\t/* 底部背景颜色 */\r\n\t\t\tz-index: -1;\r\n\t\t}\r\n\t}\r\n\r\n\t.step {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground-color: #ccc;\r\n\t\ttext-align: center;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 24rpx;\r\n\t\tgap: 0;\r\n\t}\r\n\r\n\t.step.active {\r\n\t\tbackground-color: #FF9D3E;\r\n\t\twidth: 43rpx;\r\n\t\theight: 43rpx;\r\n\t}\r\n\r\n\t.step-line {\r\n\t\tflex: 1;\r\n\t\theight: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.line-bg {\r\n\t\tbackground-color: #FFBD3A;\r\n\t}\r\n\r\n\t/* 表单样式 */\r\n\t.form {\r\n\t\tbackground-color: transparent;\r\n\t\tborder-radius: 10rpx;\r\n\t\tpadding: 20rpx 0;\r\n\t}\r\n\r\n\t.form-item-box {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmin-height: 106rpx;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\r\n\t\t.form-item {\r\n\t\t\tmin-height: 80rpx;\r\n\t\t\tborder-bottom: none;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\r\n\t\t.cursor {\r\n\t\t\tcolor: #f56c6c;\r\n\t\t\tfont-size: 22rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.form-item {\r\n\t\tdisplay: flex;\r\n\t\tmin-height: 106rpx;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\r\n\t\t.right {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: flex-end;\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.label {\r\n\t\tmin-width: 140rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\r\n\t\t.red {\r\n\t\t\tcolor: #f56c6c;\r\n\t\t}\r\n\t}\r\n\r\n\t.value {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\ttext-align: right;\r\n\t\tmin-width: 440rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.career {}\r\n\r\n\t::v-deep .checklist-group {\r\n\t\tjustify-content: flex-end;\r\n\t}\r\n\r\n\t.tag {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #1aad19;\r\n\t\tbackground-color: #e8f5e9;\r\n\t\tpadding: 6rpx 12rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t/* 底部按钮样式 */\r\n\t.footer {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.footers {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.next-button {\r\n\t\twidth: 80%;\r\n\t\theight: 80rpx;\r\n\t\tbackground-color: #1BB394;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 28rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tline-height: 80rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.prev-button {\r\n\t\twidth: 280rpx;\r\n\t\theight: 80rpx;\r\n\t\tbackground: #F6F7FB;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #5A5A5A;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t}\r\n\r\n\t.white {\r\n\t\twidth: 280rpx;\r\n\t\theight: 80rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #5A5A5A;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t}\r\n\r\n\t.form-item-step-three {\r\n\t\theight: 200rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 17rpx;\r\n\t\tpadding: 26rpx 28rpx;\r\n\t\tmargin-bottom: 26rpx;\r\n\r\n\t\t.label {\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #504E4E;\r\n\t\t}\r\n\t}\r\n\r\n\t.value-container {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 16rpx;\r\n\t}\r\n\r\n\t.value-input {\r\n\t\tflex: 1;\r\n\t\tpadding: 10rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 8rpx;\r\n\t\toutline: none;\r\n\r\n\t}\r\n\r\n\t.value-input:focus {\r\n\t\tborder-color: #1BB394;\r\n\t}\r\n\r\n\r\n\t::v-deep .checkbox__inner {\r\n\t\tborder-radius: 16rpx !important;\r\n\t}\r\n\r\n\t@keyframes rotate {\r\n\t\tfrom {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes rotateCounter {\r\n\t\tfrom {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\ttransform: rotate(-360deg);\r\n\t\t\t/* 负值实现逆时针旋转 */\r\n\t\t}\r\n\t}\r\n\r\n\t.mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100vw;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.8);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t.bg-container {\r\n\t\t\theight: 550rpx;\r\n\t\t\twidth: 550rpx;\r\n\t\t\tposition: relative;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\r\n\t\t.ai3-bg-container {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 50%;\r\n\t\t\ttop: 50%;\r\n\t\t\ttransform: translate(-50%, -50%);\r\n\t\t}\r\n\r\n\t\t.ai3-bg {\r\n\t\t\tbackground-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai3.png');\r\n\t\t\theight: 550rpx;\r\n\t\t\twidth: 550rpx;\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: contain;\r\n\t\t}\r\n\r\n\t\t.ai3-bg-animation {\r\n\t\t\tanimation: rotate 1.5s linear infinite;\r\n\t\t}\r\n\r\n\t\t.ai2-bg-container {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 50%;\r\n\t\t\ttop: 50%;\r\n\t\t\ttransform: translate(-50%, -50%);\r\n\t\t}\r\n\r\n\t\t.ai2-bg {\r\n\t\t\tbackground-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai2.png');\r\n\t\t\theight: 420rpx;\r\n\t\t\twidth: 494rpx;\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: contain;\r\n\t\t\tz-index: 99;\r\n\t\t}\r\n\r\n\t\t.ai2-bg-animation {\r\n\t\t\tanimation: rotateCounter 1.5s linear infinite;\r\n\t\t}\r\n\r\n\t\t.ai-bg {\r\n\t\t\tbackground-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai.png');\r\n\t\t\theight: 306rpx;\r\n\t\t\twidth: 306rpx;\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: contain;\r\n\t\t\tz-index: 100;\r\n\t\t}\r\n\r\n\t\t.submit {\r\n\t\t\tmargin-top: 108rpx;\r\n\t\t\twidth: 550rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tbackground: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);\r\n\t\t\tborder-radius: 40rpx 40rpx 40rpx 40rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report.vue?vue&type=style&index=0&id=32fd93bb&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report.vue?vue&type=style&index=0&id=32fd93bb&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557566382\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}