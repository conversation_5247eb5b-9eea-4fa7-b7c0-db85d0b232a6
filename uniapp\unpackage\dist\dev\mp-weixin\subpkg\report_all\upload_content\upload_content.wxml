<view class="container data-v-5a243c4a"><view class="value-container data-v-5a243c4a"><textarea class="value-input data-v-5a243c4a" maxlength="100" placeholder="请输入文本内容" data-event-opts="{{[['input',[['__set_model',['','feedbackContent','$event',[]]]]]]}}" value="{{feedbackContent}}" bindinput="__e"></textarea></view><view class="image-content data-v-5a243c4a"><u-upload vue-id="ea624aea-1" height="200rpx" width="200rpx" uploadText="上传照片" fileList="{{fileList}}" name="1" multiple="{{true}}" maxCount="{{6}}" data-event-opts="{{[['^afterRead',[['afterRead']]],['^delete',[['deletePic']]]]}}" bind:afterRead="__e" bind:delete="__e" class="data-v-5a243c4a" bind:__l="__l"></u-upload></view><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="btn data-v-5a243c4a" bindtap="__e">提交</view></view>