<template>
	<view class="search-container" @tap.self="close">
		<view class="content" @tap.stop="doNothing">
			<view class="search-input-container">
				<input type="text" class="search-input" v-model="value" placeholder="请输入关键词" />
				<image class="search-logo"
					src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/search.png" mode="widthFix">
				</image>
			</view>

			<view class="list">
				<view class="items">
					<view class="item-text" v-for="item  in  searchedList" :key="item.id" @click="chooseItem(item)">
						<rich-text :nodes="item.nameStr"></rich-text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		request
	} from "@/utils/request.js"
	import {
		shoolList
	} from "@/api/comm.js"
	export default {
		data() {
			return {
				value: '',
				list: [],
				timerId: null,
			};
		},
		props: {
			url: {
				type: String,
				required: true
			},
			typeNum: {
				type: Number,

			}
		},
		computed: {
			searchedList() {
				return this.list.map(item => {
					return {
						...item,
						nameStr: item.name.replace(this.value, '<span style="color:#2FC293">' + this.value +
							'</span>')
					}
				})
			}
		},
		created() {
			console.log('list', uni.getStorageSync('TOKEN'))
			this.getList('')
		},
		methods: {
			doNothing() {

			},
			close() {
				this.$emit('closeSerachList')
			},
			getshoolList(data) {
				return request({
					url: this.url,
					method: 'GET',
					data
				})
			},
			chooseItem(item) {
				delete item.nameStr
				this.$emit('choose', item)
			},
			getList(newVal) {
				if (this.timerId != null) {
					clearTimeout(this.timerId)
					this.timerId = null
				}
				this.timerId = setTimeout(async () => {
					const data = {}
					if (newVal != "") {
						data['name'] = newVal
					}
					// data['url'] = this.url
					let res = await this.getshoolList(data)
					if (res.errCode == 0) {
						this.list = res.data.data
					}
				}, 500)
			}
		},
		watch: {
			value(newVal, oldVal) {
				if (newVal.length > 0) {
					this.getList(newVal)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.search-container {
		height: 100vh;
		width: 100vw;
		background-color: rgba(0, 0, 0, 0.3);
		display: flex;
		align-items: center;
		justify-content: center;

		.content {
			z-index: 100;
			width: 632rpx;
			height: 900rpx;
			background: linear-gradient(181deg, #CBF2E0 0%, #FFFFFF 25%);
			padding: 64rpx 40rpx;
			border-radius: 32rpx;

			.search-input-container {
				background-color: #fff;
				border-radius: 20rpx;
				width: 600rpx;
				height: 76rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-right: 34rpx;
				// padding-left: 10rpx;
				border: 1rpx solid #2FC293;

				.search-input {
					height: 100%;
					border: 0;
					padding-left: 20rpx;
					flex: 1;
				}

				.search-logo {
					width: 34rpx;
				}
			}

			.list {
				width: 630rpx;
				height: 635rpx;
				background: #FFFFFF;
				box-shadow: 0rpx 11rpx 30rpx 1rpx rgba(55, 55, 55, 0.16);
				border-radius: 22rpx;
				margin-top: 20rpx;
				overflow-y: scroll;

				.items {

					display: flex;
					align-items: center;
					justify-content: center;
					flex-direction: column;

					.item-text {
						width: 100%;
						font-size: 30rpx;
						height: 42rpx;
						line-height: 42rpx;
						font-weight: 400;
						text-align: center;
						margin-top: 32rpx;
					}
				}
			}

		}
	}
</style>