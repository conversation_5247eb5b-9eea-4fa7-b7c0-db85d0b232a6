<view class="container data-v-305ea780"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info data-v-305ea780"><view class="info-title data-v-305ea780"><image class="logo data-v-305ea780" src="../static/imgs/rhomb.png" mode></image><text class="title-text data-v-305ea780">{{''+item.$orig.title+''}}</text></view><view class="info-content data-v-305ea780"><rich-text nodes="{{item.$orig.content}}" class="data-v-305ea780"></rich-text><block wx:if="{{item.g0}}"><text class="loading data-v-305ea780"><svg vue-id="{{'12455cc6-1-'+index}}" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" data-icon="spin" class="data-v-305ea780" bind:__l="__l" vue-slots="{{['default']}}"><defs vue-id="{{('12455cc6-2-'+index)+','+('12455cc6-1-'+index)}}" class="data-v-305ea780" bind:__l="__l" vue-slots="{{['default']}}"><linear-gradient vue-id="{{('12455cc6-3-'+index)+','+('12455cc6-2-'+index)}}" x1="0%" y1="100%" x2="100%" y2="100%" id="linearGradient-1" class="data-v-305ea780" bind:__l="__l" vue-slots="{{['default']}}"><stop vue-id="{{('12455cc6-4-'+index)+','+('12455cc6-3-'+index)}}" stop-color="currentColor" stop-opacity="0" offset="0%" class="data-v-305ea780" bind:__l="__l"></stop><stop vue-id="{{('12455cc6-5-'+index)+','+('12455cc6-3-'+index)}}" stop-color="currentColor" stop-opacity="0.50" offset="39.9430698%" class="data-v-305ea780" bind:__l="__l"></stop><stop vue-id="{{('12455cc6-6-'+index)+','+('12455cc6-3-'+index)}}" stop-color="currentColor" offset="100%" class="data-v-305ea780" bind:__l="__l"></stop></linear-gradient></defs><g vue-id="{{('12455cc6-7-'+index)+','+('12455cc6-1-'+index)}}" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" class="data-v-305ea780" bind:__l="__l" vue-slots="{{['default']}}"><rect vue-id="{{('12455cc6-8-'+index)+','+('12455cc6-7-'+index)}}" fill-opacity="0.01" fill="none" x="0" y="0" width="36" height="36" class="data-v-305ea780" bind:__l="__l"></rect><path vue-id="{{('12455cc6-9-'+index)+','+('12455cc6-7-'+index)}}" d="M34,18 C34,9.163444 26.836556,2 18,2 C11.6597233,2 6.18078805,5.68784135 3.59122325,11.0354951" stroke="url(#linearGradient-1)" stroke-width="4" stroke-linecap="round" class="data-v-305ea780" bind:__l="__l"></path></g></svg></text></block><block wx:if="{{item.g1}}"><text style="color:red;" class="data-v-305ea780">服务器繁忙，请重试</text></block><block wx:if="{{item.g2}}"><view data-event-opts="{{[['tap',[['againClick',['$0'],[[['data','',index]]]]]]]}}" class="again data-v-305ea780" bindtap="__e">重新生成</view></block></view></view></block></view>