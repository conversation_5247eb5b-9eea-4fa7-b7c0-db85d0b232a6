<view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="uni-load-more" bindtap="__e"><block wx:if="{{!webviewHide&&(iconType==='circle'||iconType==='auto'&&platform==='android')&&status==='loading'&&showIcon}}"><view class="uni-load-more__img uni-load-more__img--android-MP" style="{{'width:'+(iconSize+'px')+';'+('height:'+(iconSize+'px')+';')}}"><view class="uni-load-more__img-icon" style="{{'border-top-color:'+(color)+';'+('border-top-width:'+(iconSize/12)+';')}}"></view><view class="uni-load-more__img-icon" style="{{'border-top-color:'+(color)+';'+('border-top-width:'+(iconSize/12)+';')}}"></view><view class="uni-load-more__img-icon" style="{{'border-top-color:'+(color)+';'+('border-top-width:'+(iconSize/12)+';')}}"></view></view></block><block wx:else><block wx:if="{{!webviewHide&&status==='loading'&&showIcon}}"><view class="uni-load-more__img uni-load-more__img--ios-H5" style="{{'width:'+(iconSize+'px')+';'+('height:'+(iconSize+'px')+';')}}"><image src="{{imgBase64}}" mode="widthFix"></image></view></block></block><block wx:if="{{showText}}"><text class="uni-load-more__text" style="{{'color:'+(color)+';'}}">{{status==='more'?contentdownText:status==='loading'?contentrefreshText:contentnomoreText}}</text></block></view>