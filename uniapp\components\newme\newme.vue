<template>
	<view class="content">
		<view class="content_hadrimg">
			<image src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/icon.png" mode="">
			</image>
		</view>
		<view class="content_main">
			<view class="content_main_box">
				<view class="content_main_box_1">
					<!-- '/pages/me_all/personage/personage' -->
					<view class="content_main_box_1_left" @tap="routergo()">
						<view class="content_main_box_1_left_avatar">
							<view class="content_main_box_1_left_avatar_img">
								<image class="" src="../../static/me/icon1.svg" mode=""></image>
							</view>

							<view class="content_main_box_1_left_img">
								<image :src="user.avatar || '../../static/avatar.png'" mode="aspectFill"></image>
							</view>
							<!-- <view class="content_main_box_1_left_LV" v-if="user.level">LV{{user.level||0}}</view> -->
						</view>

						<view class="content_main_box_1_left_title flexColumn">
							<view class="content_main_box_1_left_title_1 flexc">
								<view class="me-text-beyond content_main_box_1_left_title_1_text">
									{{user.nickname||'请登录'}}
								</view>



							</view>
							<!-- <view class="content_main_box_1_left_title_1_btn" v-if="token">
								编辑
							</view> -->
							<!-- <view class="content_main_box_1_left_title_2">
								再获得{{user.upgrade||0}}个积分可升级为LV{{user.level+1||0}}
							</view>
							<view class="content_main_box_1_left_title_3 flexc">
								<view class="content_main_box_1_left_title_3_line">
									<u-line-progress :percentage="percentage" :showText="false" activeColor="#05B6F6"
										height='8'></u-line-progress>
								</view>
								<view class="" style="height: 8rpx;">
									{{user.score||0}}/{{user.upgrade||0}}
								</view>

							</view> -->
						</view>
					</view>
					<!-- <view class="content_main_box_1_right" @tap="routergo('/pages/me_all/codeqr/codeqr')">
						<image src="@/static/code.svg" mode=""></image>
					</view> -->
				</view>


			</view>
			<!-- <height :hg='!user.openid?100:10'></height> -->
			<view class="card-box">
				<view class="card-box_title">
					我的资产
				</view>

				<view class="card-box_title_content flexc flexs">
					<view class="card-box_title_content_item" @tap="routergo('/pages/me_all/integral/integral')">
						<view class="card-box_title_content_item_top flexc">

							<image class="img_1" src="../../static/me/icon2.png" mode=""></image>

							<view class="text_1">蝌蚪币</view>
						</view>
						<view class="card-box_title_content_item_bom">
							<span>{{asset.point || 0}}个</span><span>未使用</span>
						</view>

					</view>
					<view class="card-box_title_content_item" @tap="routergo('pages/me_all/coupon/coupon')">
						<view class="card-box_title_content_item_top flexc">
							<image class="img_2" src="../../static/me/icon3.svg" mode=""></image>
							<view class="text_1">优惠券</view>
						</view>
						<view class="card-box_title_content_item_bom">
							<span>{{asset.couponCnt || 0}}张</span><span>未使用</span>
						</view>

					</view>
					<!-- <view class="card-box_title_content_item" @tap="routergo('/pages/wallet/wallet')">
						<view class="card-box_title_content_item_top flexc ">
							<view class="">
								<image class="img_3" src="../../static/me/icon4.svg" mode=""></image>
							</view>
							<view class="text_1">钱包</view>
						</view>
						<view class="card-box_title_content_item_bom">
							<span>{{user.money || 0}}元</span><span>未使用</span>
						</view>

					</view> -->
				</view>



			</view>
			<view class="card" v-if="user.user">
				<view class="card_title">
					机构管理
				</view>
				<view class="card_groud lastflex">

					<view class="card_groud_item" @tap="routergo('/pages/me_all/order_off/order_off?goback='+2)">
						<image src="../../static/me/icon18.png" mode=""></image>
						<view class="">
							扫码核验
						</view>
					</view>
					<view class="card_groud_item" @tap="routergo('/pages/me_all/all_orders/all_orders?goback='+2)">
						<image src="../../static/me/icon19.png" mode=""></image>
						<view class="">
							全部订单
						</view>
					</view>
				</view>
			</view>
			<view class="card" v-else>
				<view class="card_title">
					我的订单
				</view>
				<view class="card_groud flexw">

					<view class="card_groud_item wrap"
						@tap="routergo('/pages/order_all/shipping_address/shipping_address?goback='+2)">
						<image src="../../static/me/icon12.png" mode=""></image>
						<view class="">
							已支付
						</view>
					</view>
					<!-- <button open-type="contact" @contact="token?contact:''">
						<view class="card_groud_item">
							<image src="../../static/me/icon13.png" mode=""></image>
							<view class="">
								待发货
							</view>
						</view>
					</button> -->
					<!-- <view class="card_groud_item"
						@tap="routerTo('/pages/order_all/login_protocol/login_protocol?name='+'会员协议'+'&state='+0)">
						<image src="../../static/me/icon8.svg" mode=""></image>
						<view class="">
							会员协议
						</view>
					</view> -->
					<!-- <view class="card_groud_item wrap"
						@tap="routergo('/pages/me_all/messagenotification/messagenotification')">
						<image src="../../static/me/icon14.png" mode=""></image>
						<view class="">
							待收货
						</view>
					</view> -->

					<view class="card_groud_item wrap" @tap="routergo('/pages/me_all/setmessage/setmessage')">
						<image src="../../static/me/icon15.png" mode=""></image>
						<view class="">
							待核验
						</view>
					</view>
					<view class="card_groud_item wrap"
						@tap="routergo('/pages/me_all/messagenotification/messagenotification')">
						<image src="../../static/me/icon14.png" mode=""></image>
						<view class="">
							已退款
						</view>
					</view>
					<!-- <view class="border"></view> -->
					<image class="border" src="../../static/me/icon16.png" mode=""></image>
					<view class="card_groud_item wrap" @tap="routergo('/pages/me_all/my_orders/my_orders')">
						<image src="../../static/me/icon17.png" mode=""></image>
						<view class="">
							全部订单
						</view>
					</view>
				</view>
			</view>
			<view class="card">
				<view class="card_title">
					我的功能
				</view>
				<view class="card_groud flexw">

					<view class="card_groud_item"
						@tap="routergo('/pages/order_all/shipping_address/shipping_address?goback='+2)">
						<image class="svgImg" src="../../static/me/icon5.svg" mode=""></image>
						<view class="">
							我的地址
						</view>
					</view>
					<!-- <button open-type="contact" @contact="token?contact:''">
						<view class="card_groud_item">
							<image class="svgImg" src="../../static/me/icon6.svg" mode=""></image>
							<view class="">
								官方客服
							</view>
						</view>
					</button> -->
					<!-- <view class="card_groud_item"
						@tap="routerTo('/pages/order_all/login_protocol/login_protocol?name='+'会员协议'+'&state='+0)">
						<image src="../../static/me/icon8.svg" mode=""></image>
						<view class="">
							会员协议
						</view>
					</view> -->
					<!-- <view class="card_groud_item"
						@tap="routergo('/pages/me_all/messagenotification/messagenotification')">
						<image class="svgImg" src="../../static/me/icon7.svg" mode=""></image>
						<view class="">
							消息通知
						</view>
					</view>

					<view class="card_groud_item" @tap="routergo('/pages/me_all/setmessage/setmessage')">
						<image class="svgImg" src="../../static/me/icon10.svg" mode=""></image>
						<view class="">
							我的设置
						</view>
					</view> -->
				</view>
			</view>

		</view>

		<login :show="enter" @closepage='closepage'></login>
	</view>
</template>

<script>
	import {
		userInfo
	} from "@/api/public.js"
	export default {
		// props: ['asset'],
		data() {
			return {
				enter: false,
				token: uni.getStorageSync('TOKEN') || null,
				user: uni.getStorageSync('user') || {},
				percentage: 0,
				asset: uni.getStorageSync('ASSET') || {}
			};
		},

		mounted() {
			console.log('this.enter', this.enter);
			// this.getUser()
		},

		methods: {
			// async getUser() {
			// 	let token = uni.getStorageSync('userinfo').token
			// 	if (token) {
			// 		let user = await userInfo()
			// 		if (user.code == 1) {
			// 			uni.setStorageSync('user', user.data)
			// 			this.percentage = (this.user.score / this.user.upgrade) * 100
			// 		}
			// 	}

			// },

			// contact(e) {
			// 	console.log(e);
			// },
			routergo(url) {

				let token = uni.getStorageSync('TOKEN')
				let user = uni.getStorageSync('user')
				if (token && user) {
					if (url) {
						uni.navigateTo({
							url: url
						})
					}

				} else {
					this.enter = true
					console.log('243343443', this.enter);
				}

			},
			closepage(data) {
				this.enter = false
				this.user = uni.getStorageSync('user')
				this.asset = data
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		min-height: 100vh;
		background-color: #F7F7F7 !important;
	}

	.content {
		position: relative;
		background-color: #F7F7F7 !important;

		.content_hadrimg {
			width: 100%;
			height: 503rpx;
			position: absolute;
			top: 0;
		}

		.content_main {
			position: absolute;
			top: 403rpx;
			position: relative;
			padding-bottom: 80rpx;

			.content_main_box {
				width: 703rpx;
				border-radius: 16rpx 16rpx 0 0;
				margin: 0 auto;
				position: absolute;
				top: -200rpx;
				left: 50%;
				background-color: transparent;
				transform: translateX(-50%);

				.content_main_box_1 {
					margin: 0 auto;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.content_main_box_1_left {
						display: flex;
						margin-left: 10rpx;

						.content_main_box_1_left_avatar {
							position: relative;
							width: 133rpx;
							height: 133rpx;
							margin-right: 20rpx;

							.content_main_box_1_left_avatar_img {
								width: 133rpx;
								height: 133rpx;
								position: absolute;
								top: 0;
								left: 0;
								right: 0;
								bottom: 0;
							}

							.content_main_box_1_left_img {
								width: 117rpx;
								height: 117rpx;
								position: absolute;
								top: 50%;
								left: 50%;
								transform: translate(-50%, -50%);

								image {
									border-radius: 50%;
								}
							}

							.content_main_box_1_left_LV {
								position: absolute;
								bottom: -16rpx;
								left: 50%;
								transform: translateX(-50%);
								font-size: 22rpx;
								color: #05B6F6;
								background-color: #F1FBFF;
								padding: 6rpx 20rpx;
								box-sizing: border-box;
								border-radius: 30rpx;
							}
						}


					}

					.content_main_box_1_left_title {
						// margin-left: 20rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-evenly;

						.content_main_box_1_left_title_1 {
							.content_main_box_1_left_title_1_text {
								font-weight: 500;
								font-size: 34rpx;
								color: #FFFFFF;
								max-width: 300rpx;
							}

							text:nth-child(2) {
								height: 32rpx;
								background: #F1FBFF;
								border-radius: 10rpx 10rpx 10rpx 0rpx;
								font-size: 24rpx;
								font-weight: 400;
								color: #00CCFF;
								padding: 5rpx 10rpx;
								margin-left: 10rpx;
							}
						}

						.content_main_box_1_left_title_1_btn {
							width: 88rpx;
							height: 41rpx;
							background: rgba(255, 255, 255, 0.35);
							border-radius: 424rpx 424rpx 424rpx 424rpx;
							font-size: 24rpx;
							color: #FFFFFF;
							line-height: 41rpx;
							text-align: center;
							// margin-left: 10rpx;
							margin-top: 6rpx;

						}

						.content_main_box_1_left_title_2 {
							font-size: 22rpx;
							color: #FFFFFF;
						}

						.content_main_box_1_left_title_3 {
							font-size: 22rpx;
							color: #FFFFFF;

							.content_main_box_1_left_title_3_line {
								width: 230rpx;
								height: 8rpx;
								padding-top: 12rpx;
								margin-right: 10rpx;
							}
						}
					}
				}

				.content_main_box_1_right {
					width: 60rpx;
					height: 60rpx;
					margin-right: 10rpx;
				}

				.content_main_box_2 {
					width: 645rpx;
					height: 70rpx;
					background: #F1FBFF;
					border-radius: 8rpx;
					margin: 0 auto;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.content_main_box_2_left {
						font-size: 26rpx;
						font-weight: 400;
						color: #00C8FF;
						margin-left: 10rpx;
					}

					.content_main_box_2_right {
						width: 120rpx;
						height: 54rpx;
						background: #00CCFF;
						border-radius: 27rpx;

						font-size: 24rpx;
						font-weight: 400;
						color: #F1FCFF;
						line-height: 54rpx;
						text-align: center;
						margin-right: 10rpx;
					}
				}
			}


		}

		.card-box {
			width: 690rpx;
			background: #FFFFFF;
			border-radius: 15rpx 15rpx 15rpx 15rpx;
			margin: -50rpx auto 0;
			padding: 23rpx 33rpx;
			box-sizing: border-box;

			.card-box_title {
				font-weight: bold;
				font-size: 30rpx;
				color: #343434;
				margin-bottom: 23rpx;
			}

			.card-box_title_content {
				display: flex;
				align-items: center;
				justify-content: space-evenly;

				.card-box_title_content_item {

					.card-box_title_content_item_top {
						width: 100%;
						text-align: center;
						margin-bottom: 15rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						.img_1 {
							width: 56rpx;
							height: 56rpx;
							margin-right: 10rpx;
						}

						.img_2 {
							width: 58rpx;
							height: 44rpx;
							margin-right: 10rpx;
						}

						.img_3 {
							width: 40rpx;
							height: 33rpx;
							margin-right: 10rpx;
						}


						.text_1 {
							font-weight: bold;
							font-size: 26rpx;
							color: #343434;
						}
					}

					.card-box_title_content_item_bom {
						text-align: center;
						font-weight: 400;
						font-size: 20rpx;

						span:nth-child(1) {
							color: #00C2A0;
							// margin-right: 10rpx;
						}

						span:nth-child(2) {
							color: #C3C3C3;
						}
					}

				}

			}


		}

		.card {
			width: 690rpx;
			margin: 30rpx auto;
			padding: 23rpx 0;
			box-sizing: border-box;
			background: #FFFFFF;
			border-radius: 15rpx 15rpx 15rpx 15rpx;

			&:last-child {
				margin-bottom: 50rpx;
			}

			.card_title {
				font-weight: bold;
				font-size: 30rpx;
				color: #343434;
				padding: 0 33rpx;
				box-sizing: border-box;
			}

			.card_groud {
				padding: 0 33rpx;
				box-sizing: border-box;
				display: flex;
				flex-wrap: nowrap;
				align-items: center;
				justify-content: space-between;

				.card_groud_item {
					// width: 25%;
					height: 100rpx;
					text-align: center;
					margin-top: 43rpx;
					font-weight: 400;
					font-size: 22rpx;
					color: #343434;

					image {
						width: 49rpx;
						height: 39rpx;
						margin-bottom: 10rpx;
					}

					.svgImg {
						width: 60rpx;
						height: 50rpx;
					}
				}

				.border {
					margin-top: 30rpx;
					width: 38rpx;
					height: 110rpx;
					// border-radius: 10rpx;
					// background-color: #ecfaf8;
				}
			}

			.lastflex {
				padding: 0 33rpx;
				box-sizing: border-box;
				display: flex;
				flex-wrap: nowrap;
				align-items: center;
				justify-content: flex-start;

				.card_groud_item {
					// width: 25%;
					height: 100rpx;
					text-align: center;
					margin-top: 43rpx;
					font-weight: 400;
					font-size: 22rpx;
					color: #343434;
					margin-right: 60rpx;
				}
			}
		}

	}
</style>