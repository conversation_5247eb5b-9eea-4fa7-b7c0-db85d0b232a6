import
request
from "../utils/request.js"

// 专业
// 学院

// 上传任务图片
export function getCosStsAuth(filePath) {
	return uni.http.get(`/stu/upload/getCosStsAuth?type=dxt.stu_upload&fileName=${filePath}`)
}
// 学生完成任务和相关上传
export function subTaskDone(data) {
	return uni.http.post(`/stu/studentTask/subTaskDone`, data, {
		showLoading: false
	})
}
// 获取下载文件码
export function getEncryptedAttachment(id) {
	return request({
		url: `/stu/studentTask/getEncryptedAttachment?id=${id}`,
		method: 'GET',
		data
	})
}

// 获取报告详情
export function getReport() {
	return request({
		url: `/stu/report/get`,
		method: 'GET',
	})
}
//获取预科建议
export const getAdviceList = async (data) => {
	return request({
		url: `/stu/advice/getListBy?fo=13`,
		method: 'GET',
		data
	})
}
// 保存报告
export function saveReport(id, data) {
	return request({
		url: `/stu/report/update/${id}`,
		method: 'POST',
		data
	})
}

// 生成PDF
export function generatePDF(id) {
	return request({
		url: `/stu/report/genPdf/${id}`,
		method: 'GET',
	})
}

export const prepareReport = async (data) => {
	return request({
		url: `/stu/report/reqReport?fo=13`,
		method: 'POST',
		data
	})
}
// 已登录学生激活码激活报告 
export const reportActivate = async (data) => {
	return request({
		url: `/stu/student/reportActivate`,
		method: 'POST',
		data
	})
}
// 信息采集获取验证码 
export const getCollectCode = async (data) => {
	return request({
		url: `/stu/passport/smsSendForStuCollect`,
		method: 'POST',
		data
	})
}
// 
export const getActivecode = async (data) => {
	return request({
		url: `/stu/passport/smsRegSend`,
		method: 'POST',
		data
	})
}
// 提交激活激活码  
export const smsRegLogin = async (data) => {
	return request({
		url: `/stu/passport/smsRegLogin`,
		method: 'POST',
		data
	})
}
//获取用户详细信息 
export function getInfo(data) {
	return request({
		url: `/dxt_mini/guest/getInfo`,
		method: 'GET',
		data
	})
}
//小程序获取微信用户手机号并绑定用户 
export function getWxPhone(data) {
	return request({
		url: `/dxt_mini/guest/getWxPhone?code=${data.code}`,
		method: 'GET',
		data
	})
}
// // 小程序登录权限
export function wxlogin(data) {
	return request({
		url: `/dxt_mini/guest/wxMiniLogin?code=${data.code}`,
		method: 'GET',
		data
	})
}

// 发送验证码
export function send(data) {
	return request({
		url: 'sms/send',
		method: 'POST',
		data
	})
}
// 检测手机验证码
export function check(data) {
	return request({
		url: 'sms/check',
		method: 'POST',
		data
	})
}
// 注册/登录接口
export function login(data) {
	return request({
		url: 'user/login',
		method: 'POST',
		data
	})
}
// 绑定微信账号
export function binding(data) {
	return request({
		url: 'suser/binding',
		method: 'POST',
		data
	})
}
// 解绑微信账号
export function remove(data) {
	return request({
		url: 'user/remove',
		method: 'POST',
		data
	})
}
// 退出登录
export function logout(data) {
	return request({
		url: 'user/logout',
		method: 'POST',
		data
	})
}
// 获取用户协议
export function pact(data) {
	return request({
		url: 'user/pact',
		method: 'POST',
		data
	})
}
// 修改订阅
export function subscribe(data) {
	return request({
		url: 'user/subscribe',
		method: 'POST',
		data
	})
}
// 消息推送列表
export function message(data) {
	return request({
		url: 'user/message',
		method: 'POST',
		data
	})
}
// 获取会员码
export function user_code(data) {
	return request({
		url: 'user/code',
		method: 'POST',
		data
	})
}


// 解绑微信账号
export function user_remove(data) {
	return request({
		url: 'user/remove',
		method: 'POST',
		data
	})
}
// 绑定微信账号
export function user_binding(data) {
	return request({
		url: 'user/binding',
		method: 'POST',
		data
	})
}
//消息详情
export function message_detail(data) {
	return request({
		url: 'user/message_detail',
		method: 'POST',
		data
	})
}


// 授权登录
export function wx_login(data) {
	return request({
		url: 'user/wx_login',
		method: 'POST',
		data
	})
}

//退款(已完成的订单可以发起退款)
export function refund(data) {
	return request({
		url: 'order/refund',
		method: 'POST',
		data
	})
}


// 静默登录
export function auto_login(data) {
	return request({
		url: `/dxt_mini/WxPassport/miniLogin?code=${data.code}`,
		method: 'GET',
		data
	})
}

// 获取openid
export function get_openid(data) {
	return request({
		url: 'user/get_openid',
		method: 'POST',
		data
	})
}