<template>
	<view class="content">
		<height :hg='System_height'></height>
		<view class="content_header">
			<view class="nav-title" :style="{'top':System_height+'rpx'}">
				<text>AI考研择校报告</text>
			</view>
			<uni-icons type="left" size="24" color="#2D2D2D" class="back-left" :style="{'top':System_height+'rpx'}"
				@tap="back"></uni-icons>
		</view>
		
		<!-- 通知栏 -->
		<view class="notification">
			<image src="/static/select_school/notification_icon-56586a.png" class="notification-icon"></image>
			<text class="notification-text">请认真完善信息，以便于精准生成报告！</text>
		</view>
		
		<!-- 步骤指示器 -->
		<view class="step-indicator">
			<view class="step-item">
				<view class="step-number">1</view>
			</view>
			<view class="step-item">
				<view class="step-number">2</view>
			</view>
			<view class="step-item">
				<view class="step-number">3</view>
			</view>
			<view class="step-item active">
				<view class="step-number">4</view>
			</view>
			<view class="step-item">
				<view class="step-number">5</view>
			</view>
		</view>
		
		<!-- 表单标题 -->
		<view class="form-title">
			<view class="title-bg"></view>
			<text class="title-text">目标院校倾向</text>
		</view>
		
		<!-- 表单内容 -->
		<view class="form-content">
			<view class="form-item">
				<view class="form-label">地区倾向</view>
				<input class="form-input" v-model="formData.regionPreference" placeholder="请选择地区" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">省份选择</view>
				<input class="form-input" v-model="formData.provinceSelection" placeholder="请选择省份" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">梦校</view>
				<input class="form-input" v-model="formData.dreamSchool" placeholder="请输入梦想院校" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">院校层次</view>
				<input class="form-input" v-model="formData.schoolLevel" placeholder="请选择院校层次" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item large">
				<view class="form-label">专业课制定参考书:</view>
				<textarea class="form-textarea" v-model="formData.referenceBooks" placeholder="请输入专业课参考书目"></textarea>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-buttons">
			<view class="prev-btn" @click="prevStep">
				<text>上一步</text>
			</view>
			<view class="next-btn" @click="nextStep">
				<text>下一步</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				formData: {
					regionPreference: 'A区',
					provinceSelection: '安徽省、上海市、浙江省、江苏省',
					dreamSchool: '中国科学技术大学',
					schoolLevel: '985',
					referenceBooks: '马克思主义理论书籍'
				}
			}
		},
		computed: {
			...mapState(['System_height'])
		},
		onLoad() {
			// 加载之前保存的数据
			const savedData = uni.getStorageSync('selectSchoolStep4')
			if (savedData) {
				this.formData = { ...this.formData, ...savedData }
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			prevStep() {
				// 返回上一步
				uni.navigateBack()
			},
			nextStep() {
				// 保存当前步骤数据
				uni.setStorageSync('selectSchoolStep4', this.formData)
				
				// 跳转到下一步
				uni.navigateTo({
					url: '/subpkg/select_school/form_step5/form_step5'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		min-height: 100vh;
		background: linear-gradient(180deg, #CBF2E0 0%, #F6F7FB 43.3%);
		padding-bottom: 160rpx;
	}
	
	.content_header {
		position: relative;
		height: 140rpx;
		background: #00C2A0;
		
		.nav-title {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			z-index: 10;
			
			text {
				font-size: 34rpx;
				color: #2D2D2D;
				font-weight: 400;
			}
		}
		
		.back-left {
			position: absolute;
			left: 30rpx;
			z-index: 10;
		}
	}
	
	.notification {
		display: flex;
		align-items: center;
		margin: 30rpx;
		padding: 14rpx 28rpx;
		background: #F5FFFD;
		border-radius: 31rpx;
		
		.notification-icon {
			width: 49rpx;
			height: 49rpx;
			margin-right: 28rpx;
		}
		
		.notification-text {
			font-size: 28rpx;
			color: #5A5A5A;
			flex: 1;
		}
	}
	
	.step-indicator {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 30rpx 0;
		
		.step-item {
			margin: 0 81rpx;
			
			.step-number {
				width: 43rpx;
				height: 43rpx;
				border-radius: 50%;
				background: #FF9B3A;
				color: #FFFFFF;
				font-size: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			&.active .step-number {
				background: #FF9B3A;
			}
		}
	}
	
	.form-title {
		position: relative;
		margin: 30rpx;
		
		.title-bg {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 183rpx;
			height: 19rpx;
			background: #DBFF9C;
		}
		
		.title-text {
			font-size: 30rpx;
			color: #060606;
			font-weight: 400;
		}
	}
	
	.form-content {
		padding: 0 30rpx;
		
		.form-item {
			display: flex;
			align-items: center;
			background: #FFFFFF;
			border-radius: 16rpx;
			padding: 31rpx 28rpx;
			margin-bottom: 30rpx;
			position: relative;
			
			&.large {
				align-items: flex-start;
				min-height: 156rpx;
				
				.form-label {
					margin-top: 10rpx;
				}
			}
			
			.form-label {
				font-size: 30rpx;
				color: #504E4E;
				width: 240rpx;
				flex-shrink: 0;
			}
			
			.form-input {
				flex: 1;
				font-size: 28rpx;
				color: #989898;
				text-align: right;
				margin-right: 20rpx;
			}
			
			.form-textarea {
				flex: 1;
				font-size: 28rpx;
				color: #989898;
				min-height: 80rpx;
				margin-top: 10rpx;
			}
			
			.form-arrow {
				flex-shrink: 0;
			}
		}
	}
	
	.bottom-buttons {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #F6F7FB;
		padding: 30rpx;
		display: flex;
		gap: 30rpx;
		
		.prev-btn {
			flex: 1;
			height: 80rpx;
			background: #FFFFFF;
			border: 2rpx solid #5A5A5A;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			text {
				font-size: 30rpx;
				color: #5A5A5A;
				font-weight: 400;
			}
		}
		
		.next-btn {
			flex: 1;
			height: 80rpx;
			background: linear-gradient(135deg, #26C8AC 0%, #19C990 100%);
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			text {
				font-size: 30rpx;
				color: #FFFFFF;
				font-weight: 400;
			}
		}
	}
</style>
