@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content.data-v-b292597c {
  min-height: 100vh;
  background: linear-gradient(180deg, #CBF2E0 0%, #F6F7FB 43.3%);
}
.content_header.data-v-b292597c {
  position: relative;
  height: 140rpx;
  background: #00C2A0;
}
.content_header .nav-title.data-v-b292597c {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 10;
}
.content_header .nav-title text.data-v-b292597c {
  font-size: 34rpx;
  color: #2D2D2D;
  font-weight: 400;
}
.content_header .back-left.data-v-b292597c {
  position: absolute;
  left: 30rpx;
  z-index: 10;
}
.generating-section.data-v-b292597c {
  padding: 60rpx 30rpx;
  text-align: center;
}
.generating-section .ai-container.data-v-b292597c {
  position: relative;
  height: 400rpx;
  margin-bottom: 60rpx;
}
.generating-section .ai-container .ai-images.data-v-b292597c {
  position: relative;
  height: 100%;
}
.generating-section .ai-container .ai-images .ai-bg-4.data-v-b292597c {
  position: absolute;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 300rpx;
  height: 300rpx;
  z-index: 1;
}
.generating-section .ai-container .ai-images .ai-bg-4.rotating.data-v-b292597c {
  -webkit-animation: rotate-data-v-b292597c 3s linear infinite;
          animation: rotate-data-v-b292597c 3s linear infinite;
}
.generating-section .ai-container .ai-images .ai-bg-1.data-v-b292597c {
  position: absolute;
  top: 20rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 280rpx;
  height: 280rpx;
  z-index: 2;
}
.generating-section .ai-container .ai-images .ai-bg-1.rotating.data-v-b292597c {
  animation: rotate-data-v-b292597c 2s linear infinite reverse;
}
.generating-section .ai-container .ai-images .ai-bg-2.data-v-b292597c {
  position: absolute;
  top: 40rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 260rpx;
  height: 220rpx;
  z-index: 3;
}
.generating-section .ai-container .ai-images .ai-bg-2.rotating.data-v-b292597c {
  -webkit-animation: rotate-data-v-b292597c 4s linear infinite;
          animation: rotate-data-v-b292597c 4s linear infinite;
}
.generating-section .ai-container .ai-images .ai-center.data-v-b292597c {
  position: absolute;
  top: 80rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 180rpx;
  height: 180rpx;
  z-index: 4;
}
.generating-section .ai-container .ai-images .ai-center .ai-main.data-v-b292597c {
  width: 100%;
  height: 100%;
}
.generating-section .ai-container .ai-images .ai-center .ai-main.pulsing.data-v-b292597c {
  -webkit-animation: pulse-data-v-b292597c 2s ease-in-out infinite;
          animation: pulse-data-v-b292597c 2s ease-in-out infinite;
}
.generating-section .ai-container .ai-images .ai-center .ai-text.data-v-b292597c {
  position: absolute;
  top: 50rpx;
  left: 25rpx;
  width: 130rpx;
  height: 88rpx;
}
.generating-section .generating-text.data-v-b292597c {
  font-size: 32rpx;
  color: #2D2D2D;
  margin-bottom: 40rpx;
}
.generating-section .progress-bar.data-v-b292597c {
  width: 80%;
  height: 8rpx;
  background: #E0E0E0;
  border-radius: 4rpx;
  margin: 0 auto 20rpx;
  overflow: hidden;
}
.generating-section .progress-bar .progress-fill.data-v-b292597c {
  height: 100%;
  background: linear-gradient(90deg, #26C8AC 0%, #19C990 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}
.generating-section .progress-text.data-v-b292597c {
  font-size: 28rpx;
  color: #00C2A0;
  font-weight: 500;
}
.success-section.data-v-b292597c {
  padding: 60rpx 30rpx;
  text-align: center;
}
.success-section .success-icon.data-v-b292597c {
  width: 120rpx;
  height: 120rpx;
  background: #E8F5E8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
}
.success-section .success-title.data-v-b292597c {
  font-size: 36rpx;
  color: #2D2D2D;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.success-section .success-desc.data-v-b292597c {
  font-size: 28rpx;
  color: #5A5A5A;
  margin-bottom: 60rpx;
}
.success-section .report-preview.data-v-b292597c {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 60rpx;
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.1);
}
.success-section .report-preview .report-name.data-v-b292597c {
  font-size: 32rpx;
  color: #00C2A0;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.success-section .report-preview .report-time.data-v-b292597c {
  font-size: 24rpx;
  color: #818181;
  margin-bottom: 30rpx;
}
.success-section .report-preview .report-content.data-v-b292597c {
  font-size: 28rpx;
  color: #5A5A5A;
  line-height: 1.6;
}
.success-section .action-buttons.data-v-b292597c {
  display: flex;
  gap: 30rpx;
}
.success-section .action-buttons .view-btn.data-v-b292597c {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #26C8AC 0%, #19C990 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.success-section .action-buttons .view-btn text.data-v-b292597c {
  font-size: 30rpx;
  color: #FFFFFF;
  font-weight: 400;
}
.success-section .action-buttons .back-btn.data-v-b292597c {
  flex: 1;
  height: 80rpx;
  background: #FFFFFF;
  border: 2rpx solid #00C2A0;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.success-section .action-buttons .back-btn text.data-v-b292597c {
  font-size: 30rpx;
  color: #00C2A0;
  font-weight: 400;
}
@-webkit-keyframes rotate-data-v-b292597c {
from {
    -webkit-transform: translateX(-50%) rotate(0deg);
            transform: translateX(-50%) rotate(0deg);
}
to {
    -webkit-transform: translateX(-50%) rotate(360deg);
            transform: translateX(-50%) rotate(360deg);
}
}
@keyframes rotate-data-v-b292597c {
from {
    -webkit-transform: translateX(-50%) rotate(0deg);
            transform: translateX(-50%) rotate(0deg);
}
to {
    -webkit-transform: translateX(-50%) rotate(360deg);
            transform: translateX(-50%) rotate(360deg);
}
}
@-webkit-keyframes pulse-data-v-b292597c {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
}
}
@keyframes pulse-data-v-b292597c {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
}
}

