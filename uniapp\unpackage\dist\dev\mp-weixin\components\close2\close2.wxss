@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
 .u-picker__view .u-picker__view__column text {
  height: 68rpx !important;
  line-height: 68rpx !important;
}
.box {
  width: 700rpx;
  height: 101rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 3rpx 15rpx 0rpx rgba(191, 202, 211, 0.46);
  border-radius: 51rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  bottom: 20rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 12;
}
.box .box_content {
  display: flex;
  align-items: center;
}
.box .box_img {
  width: 70rpx;
  height: 96rpx;
  margin: 0 30rpx;
  margin-left: 50rpx;
  position: relative;
}
.box .box_img text {
  width: 40rpx;
  height: 40rpx;
  background-color: #05B6F6;
  border-radius: 50%;
  text-align: center;
  line-height: 40rpx;
  color: #fff;
  position: absolute;
  right: -20rpx;
  font-size: 24rpx;
}
.box .box_price text:nth-child(1) {
  width: 124rpx;
  font-size: 32rpx;
  font-weight: 400;
  color: #101010;
  line-height: 101rpx;
}
.box .box_price text:nth-child(2) {
  font-size: 32rpx;
  font-weight: 500;
  color: #E45F3A;
  line-height: 101rpx;
  margin-left: 10rpx;
}
.box .box_price text:nth-child(3) {
  font-size: 30rpx;
  font-weight: 500;
  color: #E45F3A;
  line-height: 101rpx;
  margin-left: 10rpx;
}
.box .box_close {
  width: 173rpx;
  height: 100%;
  background: #05B6F6;
  border-radius: 0rpx 89rpx 89rpx 0rpx;
  line-height: 101rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #FDFEFF;
}
.upcart {
  width: 100%;
  height: 700rpx;
}
.upcart .upcart_top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
}
.upcart .upcart_top .upcart_top_left {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
}
.upcart .upcart_top .upcart_top_right {
  font-size: 24rpx;
  font-weight: 400;
  color: #676767;
  display: flex;
  align-items: center;
}
.upcart .upcart_order_content {
  display: flex;
  align-items: center;
  padding: 0rpx 20rpx;
  height: 135rpx;
  margin-bottom: 30rpx;
}
.upcart .upcart_order_content .upcart_order_content_img {
  width: 136rpx;
  height: 135rpx;
  border-radius: 7rpx;
  overflow: hidden;
}
.upcart .upcart_order_content .upcart_order_content_title {
  width: 540rpx;
  margin-left: 20rpx;
}
.upcart .upcart_order_content .upcart_order_content_title view:nth-child(1) {
  font-size: 28rpx;
  font-weight: 400;
  color: #353535;
}
.upcart .upcart_order_content .upcart_order_content_title view:nth-child(2) {
  font-size: 24rpx;
  font-weight: 400;
  color: #676767;
  padding: 15rpx 0;
}
.upcart .upcart_order_content .upcart_order_content_title view:nth-child(3) {
  height: 45rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #E45F3A;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.minus {
  width: 22px;
  height: 22px;
  border-width: 1px;
  border-color: #05B6F6;
  border-style: solid;
  border-top-left-radius: 100px;
  border-top-right-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.plus {
  width: 22px;
  height: 22px;
  background-color: #05B6F6;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.popup_box {
  padding: 32rpx;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 20rpx;
}
.popup_box .popup_box_title {
  font-size: 32rpx;
  text-align: center;
}
.popup_box .popup_box_ipt {
  font-size: 26rpx;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}
.popup_box .popup_box_ipt .popup_box_ipt_right {
  width: 80%;
  font-size: 24rpx;
  color: #707070;
}
.popup_box .popup_box_btns {
  margin-top: 30rpx;
}
.popup_box .popup_box_btns .popup_box_btn {
  width: 48%;
  height: 80rpx;
  border-radius: 40rpx;
  box-sizing: border-box;
  background-color: #05B6F6;
  color: #fff;
  font-size: 30rpx;
  text-align: center;
  line-height: 80rpx;
}

