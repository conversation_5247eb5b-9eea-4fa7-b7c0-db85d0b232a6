<view class="content data-v-59397042"><height vue-id="6415cdf8-1" hg="{{System_height}}" class="data-v-59397042" bind:__l="__l"></height><view class="content_header data-v-59397042"><view class="nav-title data-v-59397042" style="{{'top:'+(System_height+'rpx')+';'}}"><text class="data-v-59397042">AI考研择校报告</text></view><uni-icons class="back-left data-v-59397042" style="{{'top:'+(System_height+'rpx')+';'}}" vue-id="6415cdf8-2" type="left" size="24" color="#2D2D2D" data-event-opts="{{[['^tap',[['back']]]]}}" bind:tap="__e" bind:__l="__l"></uni-icons></view><view class="notification data-v-59397042"><image class="notification-icon data-v-59397042" src="/static/select_school/notification_icon-56586a.png"></image><text class="notification-text data-v-59397042">请认真完善信息，以便于精准生成报告！</text></view><view class="step-indicator data-v-59397042"><view class="step-item active data-v-59397042"><view class="step-number data-v-59397042">1</view></view><view class="step-item data-v-59397042"><view class="step-number data-v-59397042">2</view></view><view class="step-item data-v-59397042"><view class="step-number data-v-59397042">3</view></view><view class="step-item data-v-59397042"><view class="step-number data-v-59397042">4</view></view><view class="step-item data-v-59397042"><view class="step-number data-v-59397042">5</view></view></view><view class="form-title data-v-59397042"><view class="title-bg data-v-59397042"></view><text class="title-text data-v-59397042">个人基础信息</text></view><view class="form-content data-v-59397042"><view class="form-item data-v-59397042"><view class="form-label data-v-59397042">学生姓名</view><input class="form-input data-v-59397042" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','studentName','$event',[]],['formData']]]]]}}" value="{{formData.studentName}}" bindinput="__e"/><uni-icons class="form-arrow data-v-59397042" vue-id="6415cdf8-3" type="right" size="16" color="#C3C3C3" bind:__l="__l"></uni-icons></view><view class="form-item data-v-59397042"><view class="form-label data-v-59397042">性别</view><input class="form-input data-v-59397042" placeholder="请选择性别" data-event-opts="{{[['input',[['__set_model',['$0','gender','$event',[]],['formData']]]]]}}" value="{{formData.gender}}" bindinput="__e"/><uni-icons class="form-arrow data-v-59397042" vue-id="6415cdf8-4" type="right" size="16" color="#C3C3C3" bind:__l="__l"></uni-icons></view><view class="form-item data-v-59397042"><view class="form-label data-v-59397042">本科院校</view><input class="form-input data-v-59397042" placeholder="请输入本科院校" data-event-opts="{{[['input',[['__set_model',['$0','undergraduateSchool','$event',[]],['formData']]]]]}}" value="{{formData.undergraduateSchool}}" bindinput="__e"/><uni-icons class="form-arrow data-v-59397042" vue-id="6415cdf8-5" type="right" size="16" color="#C3C3C3" bind:__l="__l"></uni-icons></view><view class="form-item data-v-59397042"><view class="form-label data-v-59397042">本科专业</view><input class="form-input data-v-59397042" placeholder="请输入本科专业" data-event-opts="{{[['input',[['__set_model',['$0','undergraduateMajor','$event',[]],['formData']]]]]}}" value="{{formData.undergraduateMajor}}" bindinput="__e"/><uni-icons class="form-arrow data-v-59397042" vue-id="6415cdf8-6" type="right" size="16" color="#C3C3C3" bind:__l="__l"></uni-icons></view><view class="form-item data-v-59397042"><view class="form-label data-v-59397042">目标专业</view><input class="form-input data-v-59397042" placeholder="请输入目标专业" data-event-opts="{{[['input',[['__set_model',['$0','targetMajor','$event',[]],['formData']]]]]}}" value="{{formData.targetMajor}}" bindinput="__e"/><uni-icons class="form-arrow data-v-59397042" vue-id="6415cdf8-7" type="right" size="16" color="#C3C3C3" bind:__l="__l"></uni-icons></view><view class="form-item data-v-59397042"><view class="form-label data-v-59397042">专业代码</view><input class="form-input data-v-59397042" placeholder="请输入专业代码" data-event-opts="{{[['input',[['__set_model',['$0','majorCode','$event',[]],['formData']]]]]}}" value="{{formData.majorCode}}" bindinput="__e"/><uni-icons class="form-arrow data-v-59397042" vue-id="6415cdf8-8" type="right" size="16" color="#C3C3C3" bind:__l="__l"></uni-icons></view><view class="form-item data-v-59397042"><view class="form-label data-v-59397042">联系方式</view><input class="form-input data-v-59397042" placeholder="请输入联系方式" data-event-opts="{{[['input',[['__set_model',['$0','contactInfo','$event',[]],['formData']]]]]}}" value="{{formData.contactInfo}}" bindinput="__e"/><uni-icons class="form-arrow data-v-59397042" vue-id="6415cdf8-9" type="right" size="16" color="#C3C3C3" bind:__l="__l"></uni-icons></view><view class="form-item data-v-59397042"><view class="form-label data-v-59397042">本科年份</view><input class="form-input data-v-59397042" placeholder="请输入本科年份" data-event-opts="{{[['input',[['__set_model',['$0','graduationYear','$event',[]],['formData']]]]]}}" value="{{formData.graduationYear}}" bindinput="__e"/><uni-icons class="form-arrow data-v-59397042" vue-id="6415cdf8-10" type="right" size="16" color="#C3C3C3" bind:__l="__l"></uni-icons></view><view class="form-item data-v-59397042"><view class="form-label data-v-59397042">跨专业</view><input class="form-input data-v-59397042" placeholder="是否跨专业" data-event-opts="{{[['input',[['__set_model',['$0','crossMajor','$event',[]],['formData']]]]]}}" value="{{formData.crossMajor}}" bindinput="__e"/><uni-icons class="form-arrow data-v-59397042" vue-id="6415cdf8-11" type="right" size="16" color="#C3C3C3" bind:__l="__l"></uni-icons></view></view><view class="bottom-buttons data-v-59397042"><view data-event-opts="{{[['tap',[['nextStep',['$event']]]]]}}" class="next-btn data-v-59397042" bindtap="__e"><text class="data-v-59397042">下一步</text></view></view></view>