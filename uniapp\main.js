import App from './App'
// #ifndef VUE3
import uView from 'uview-ui'
import Vue from 'vue'
import vuePrototype from "./utils/wxApi.js"
import fnc from '@/utils/function.js'
import http from '@/utils/request.js'
import store from "@/store/index.js"
Vue.prototype.$way = fnc
// 过滤器
import * as filters from './utils/filter.js'
Object.keys(filters).forEach(key => {
	Vue.filter(key, filters[key])
})
uni.http = http
uni.tip = (title) => uni.showToast({
	title,
	duration: 1500,
	icon: 'none'
})
// 全局分享
const $x = {};
Vue.prototype.$x = $x;
let share = require('./utils/share');
Vue.mixin(share);

import {
	router,
	RouterMount
} from './router/router.js' //路径换成自己的

// 如此配置即可
Vue.use(router)
Vue.use(uView)
uni.$u.config.unit = 'rpx'
Vue.config.productionTip = false
App.mpType = 'app'
uni.$showMsg = function(title = '数据加载失败！', duration = 2000) {
	uni.showToast({
		title,
		duration,
		icon: 'none',
	})
}
// 扩展vue原型方法
Vue.prototype.$utils = {}
Object.keys(vuePrototype).forEach(k => {
	Vue.prototype[k] = vuePrototype[k]
})
try {
	function isPromise(obj) {
		return (
			!!obj &&
			(typeof obj === "object" || typeof obj === "function") &&
			typeof obj.then === "function"
		);
	}
	// 统一 vue2 API Promise 化返回格式与 vue3 保持一致
	uni.addInterceptor({
		returnValue(res) {
			if (!isPromise(res)) {
				return res;
			}
			return new Promise((resolve, reject) => {
				res.then((res) => {
					if (res[0]) {
						reject(res[0]);
					} else {
						resolve(res[1]);
					}
				});
			});
		},
	});
} catch (error) {}
const app = new Vue({
	...App,
	store,
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
export function createApp() {
	const app = createSSRApp(App)
	return {
		app
	}
}
// #endif