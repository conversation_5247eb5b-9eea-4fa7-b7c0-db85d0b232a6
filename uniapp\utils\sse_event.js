import store from "@/store/index.js"

// 封装的 WebSocket 请求替代 SSE
const sseEvent = (url, type, cb, options = {}) => {
	return new Promise((resolve, reject) => {
		// 将 HTTP URL 转换为 WebSocket URL
		const wsUrl = url.replace(/^http/, 'ws');
		const socketTask = uni.connectSocket({
			url: wsUrl,
			success: () => {
				console.log('WebSocket 连接成功');
			}
		});

		// 如果有 AbortSignal，监听 abort 事件
		if (options.signal) {
			options.signal.addEventListener('abort', () => {
				socketTask.close();
				reject(new Error('请求被中止'));
			});
		}

		socketTask.onOpen(() => {
			store.commit('addEventSource', type);
		});

		socketTask.onError((error) => {
			socketTask.close();
			reject(error);
		});

		socketTask.onMessage((res) => {
			try {
				const data = JSON.parse(res.data);
				switch (data.type) {
					case 'say':
						cb(data.w, "say", data.t, data.f);
						break;
					case 'reply':
						let buffer = data.w.replace(/\n{1,2}/g, "<br>");
						buffer = buffer.replace(/\*\*([^*]+)\*\*/g, "<b>$1</b>");
						cb(buffer, "reply");
						break;
					case 'end':
						store.commit('removeEventSource', data['t']);
						cb(data.w, "end", data.t, data.f);
						socketTask.close();
						resolve('done');
						break;
				}
			} catch (e) {
				console.error('数据解析错误:', e);
			}
		});

		socketTask.onClose(() => {
			console.log('WebSocket 连接关闭');
		});
	});
};

export default sseEvent;