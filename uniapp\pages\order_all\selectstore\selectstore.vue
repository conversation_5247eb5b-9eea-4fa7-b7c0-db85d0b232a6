<template>
	<view>
		<view class="hade">
			<view class="hade_left">
				<view @tap="index_select=0,store_listApi()" :style="{color:index_select==0?'#05B6F6':'' }">
					附近校区
				</view>
				<!-- <view @tap="index_select=1,store_listApi()" :style="{color:index_select==1?'#05B6F6':'' }">
					常去/收藏门店
				</view> -->
				<view class="right selectCity">
					<select-city :type="type" :districtCode="stuInfo.pcaCode" @confirm="handleConfirm" />
					<uni-icons class="icon-right" type="right" size="16" color="#C3C3C3"></uni-icons>
				</view>
			</view>
			<!-- <view class="hade_right" @tap="position_selection">
				<view>
					<u-icon name="search" color="#B6B6B6" size="40"></u-icon>
				</view>
				<view>
					搜索
				</view>
			</view> -->
		</view>
		<view class="content">
			<view class="content_hade" @tap="position_selection">
				<!-- <view class="content_hade_1">
					<u-icon name="map" color="#5A5A5A " size="30"></u-icon>
				</view>
				<view class="content_hade_2">
					{{locationname}}
				</view>
				<view class="content_hade_3">
					<u-icon name="arrow-right" color="#5A5A5A " size="30"></u-icon>
				</view> -->
			</view>
			<view class="box">
				<map class="map" :style="{height: mapshow==0?'399rpx':'100rpx'}" :latitude="location.latitude"
					:longitude="location.longitude" :markers="covers" :show-location='true' @markertap="markertap"
					@tap="tap" @updated="updated"></map>
				<view class="display" @tap="mapdisplay">
					<view class="display_text">
						<view class="display_text_content" v-if="mapshow==0">
							<text>收起地图 </text>
							<u-icon name="arrow-up" color="#252525" size="28"></u-icon>
						</view>
						<view class="display_text_content" v-if="mapshow==1">
							<text>展开地图 </text>
							<u-icon name="arrow-down" color="#252525" size="28"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<view style="margin-top: 150rpx;" v-if="shoplist.length<1">
				<u-empty mode="data" :iconSize='150' :textSize='24' text='暂无内容' icon=""></u-empty>
			</view>
			<view class="position_box" v-for="(item,index) in shoplist" :key="item">
				<view class="chosen_position">
					<view class="chosen_position_left">
						<view class="chosen_position_left_1">
							{{item.name}}
						</view>
						<view class="chosen_position_left_2">
							<u-icon name="map" color="#5A5A5A " size="28"></u-icon>
							<text style="margin-left: 5rpx;"> {{item.addr||item.selectedPlace}}</text>

						</view>
						<!-- <view class="chosen_position_left_3">
							<u-icon name="clock" color="#5A5A5A " size="28"></u-icon>
							<text style="margin-left: 5rpx;"> {{item.start_times}}-{{item.end_times}}</text>
						</view> -->
						<view class="chosen_position_left_4">
							<view v-if="item.isOpen==1">营业中</view>
							<view v-else>已歇业</view>
							<!-- <view v-if="item.subscribe_switch==1&&item.is_business==1">接受预定</view>
							<view v-if="item.takeaway_switch==1&&item.is_business==1">可外卖</view>
							<view v-if="item.delivery_switch==1&&item.is_business==1">可自提</view> -->
						</view>
					</view>
					<view class="chosen_position_right">
						<view class="chosen_position_right_1">
							<span @tap="goplace(item,1)">去下单</span>
							<!-- <span v-if="item.subscribe_switch==1&&item.is_business==1">/</span> -->
							<!-- <span @tap="goplace(item.id,2)"
								v-if="item.subscribe_switch==1&&item.is_business==1">去预约</span> -->
						</view>
						<view class="chosen_position_right_2" v-if="item.distanceText">
							距离{{ item.distanceText}}
						</view>
						<view class="chosen_position_right_3">
							<view class="chosen_position_right_3_1" @tap="openTel(item.tel)">
								<image src="@/static/Project_drawing 15.png" mode=""></image>
							</view>
							<view class="chosen_position_right_3_2" @tap="getopenLocation(item)">
								<image src="@/static/Project_drawing 13.png" mode=""></image>
							</view>
						</view>
					</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	import img from "@/static/Project_drawing 10.png"
	import {
		// store_list,
		// store_collectionList,
		// order_empty,
		wxCampusList
	} from "@/api/comm.js"
	import selectCity from '@/components/select_city/select_city.vue';
	import wApi from "@/utils/wxApi.js"
	import {
		forEach
	} from "lodash";
	export default {
		components: {
			selectCity
		},
		data() {
			return {
				index_select: 0,
				mapshow: 0,
				title: '百度地图',
				latitude: 34.7586,
				longitude: 113.672307,
				covers: [], //标记点地图数据
				shoplist: [],
				location: {}, //当前页面展示的小地图经纬度
				locationname: '', //当前位置名字
				page: 1, //下拉加载,
				pcaCode: '',
			};
		},
		onLoad() {
			this.$forceUpdate();
			this.getlocation()

		},
		onShow() {

		},
		methods: {
			getopenLocation(item) {
				console.log(item)
				uni.openLocation({
					latitude: item.latitude * 1, // 纬度
					longitude: item.longitude * 1, // 经度
					name: item.name, // 可选：地点名称
					address: item.selectedPlace, // 可选：详细地址
					success: () => {
						console.log('地图打开成功');
					},
					fail: (err) => {
						console.log(err)
						uni.showToast({
							title: '打开地图失败',
							icon: 'none'
						});
					}
				});
			},
			handleConfirm(result) {
				this.index_select = 1
				this.pcaCode = result

				this.location = {}
				this.getList()
			},
			async getList() {
				// console.log(this.location)
				// console.log([this.location.longitude, this.location.latitude].join(','))
				const {
					data,
					errCode,
					msg
				} = await wxCampusList({
					page: 1,
					limit: 20,
					coordinate: [this.location.longitude, this.location.latitude].join(','),
					// coordinate: '',
					pcaCode: this.pcaCode || '',

				})
				if (errCode == 0) {
					this.shoplist = data.data
				}
			},
			// 点击附近位置
			store_listApi() {
				this.getlocation()
			},
			async getlocation() {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						console.log('res', res)
						this.location = res;
						uni.showToast({
							title: `纬度:${res.latitude}, 经度:${res.longitude}`,
							icon: 'none'
						});
						this.getList()
					},

				})
			},
			// 点击拨打电话
			callCustomerService(phone) {
				// 1. 先弹窗确认
				uni.showModal({
					title: '提示',
					content: `确定拨打电话：${phone}吗？`,
					confirmText: '确定',
					cancelText: '取消',
					success: (res) => {
						// 2. 用户点击“确定”后直接拨号
						if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber: phone,
								success: () => {
									console.log('拨号成功');
								},
								fail: (err) => {
									console.error('拨号失败:', err);
									uni.showToast({
										title: '拨号失败，请稍后再试',
										icon: 'none'
									});
								}
							});
						}
					}
				});
			},
			init() {
				let that = this;

				//发起网络请求获取数据
				//用uni.request(OBJECT)方法
				//我这里模拟下数据
				var data = [{
					id: 1,
					name: '雷军',
					imgUrl: '../../static/Project_drawing 1.png',
					lat: "34.7586",
					lng: "113.672307"
				}, {
					id: 2,
					name: '李彦宏',
					imgUrl: '../../static/Project_drawing 2.png',
					lat: "34.763466",
					lng: "113.686285"
				}, {
					id: 3,
					name: '马化腾',
					imgUrl: '../../static/Project_drawing 3.png',
					lat: "34.763412",
					lng: "113.680185"
				}, ];
				that.MapData(that, data)
			},
			//地图数据初始化
			MapData(that, data) {
				console.log(data.length)
				console.log(data)
				let arrayData = [];
				for (var i = 0; i < data.length; i++) {
					arrayData.push({
						id: data[i].id, //marker点击事件回调会返回此id。建议为每个marker设置上Number类型id，保证更新marker时有更好的性能。
						latitude: data[i].lat, //纬度
						longitude: data[i].lng, //经度
						title: data[i].name, //点击时显示，callout存在时将被忽略
						iconPath: data[i].imgUrl, //项目目录下的图片路径，支持相对路径写法，以'/'开头则表示相对小程序根目录；也支持临时路径
						width: 60,
						height: 60,
						callout: {
							//自定义标记点上方的气泡窗口
							content: data[i].name,
							color: '', //文本颜色
							fontSize: 16, //文字大小
							borderRadius: 20, //callout边框圆角
							bgColor: '', //背景色
							padding: 6, //文本边缘留白
							display: 'BYCLICK', //'BYCLICK':点击显示; 'ALWAYS':常显
							textAlign: 'left', //文本对齐方式。有效值: left, right, center
						},
						label: {
							//为标记点旁边增加标签
							content: '', //标记点旁边的文字
							color: '#ff6600', //文本颜色
							fontSize: 16, //文字大小
							x: 0, //label的坐标，原点是 marker 对应的经纬度
							y: 0, //label的坐标，原点是 marker 对应的经纬度
							borderWidth: 1, //边框宽度
							borderColor: '', //边框颜色
							borderRadius: 10, //边框圆角
							bgColor: 'red',
							padding: 6, //	文本边缘留白
							textAlign: 'left', //文本对齐方式。有效值: left, right, center
						},
						anchor: {
							//经纬度在标注图标的锚点，默认底边中点      {x, y}，x表示横向(0-1)，y表示竖向(0-1)。{x: .5, y: 1} 表示底边中点
							x: .5,
							y: 1
						}
					});
				}
				console.log(arrayData.length)
				console.log(arrayData)
				//重新给地图数据赋值covers 
				that.covers = arrayData;
			},

			//地图点击事件
			markertap(e) {
				console.log("===你点击了标记点===")
				console.log("你点击的标记点ID是:" + e.detail.markerId)
				//console.log(e)
			},
			//点击地图时触发; App-nuve、微信小程序2.9支持返回经纬度
			tap(e) {
				uni.showToast({
					title: '选中附近位置',
					icon: "success"
				})
				console.log("===你点击了地图点===")
				console.log(e)
				uni.setStorageSync('location', e.detail)
				this.store_listApi()
			},
			//在地图渲染更新完成时触发
			updated(e) {
				console.log("===在地图渲染更新完成时触发===")
				console.log(e)
			},
			mapdisplay() {
				if (this.mapshow) {
					this.mapshow = 0
				} else {
					this.mapshow = 1
				}
			},
			// async store_listApi() {
			// 	if (this.index_select == 0) {
			// 		let data = await store_list({
			// 			page: 1,
			// 			limit: 10,
			// 			search: ''
			// 		})
			// 		console.log(data, '门店列表');
			// 		this.shoplist = []
			// 		this.locationname = data.data.address
			// 		if (!data?.data?.rows?.length) return
			// 		data?.data?.rows?.forEach(res => {
			// 			res.distance = wApi.mkm(res.distance)
			// 		})
			// 		this.shoplist.push(...data.data.rows)
			// 	} else {
			// 		let data = await store_collectionList({
			// 			page: 1,
			// 			limit: 10,
			// 			search: ''
			// 		})
			// 		console.log(data, '门店列表');
			// 		this.shoplist = []
			// 		if (data.data) {
			// 			data.data.rows.forEach(res => {
			// 				res.distance = wApi.mkm(res.distance)
			// 			})
			// 			this.locationname = data.data.address
			// 			this.shoplist.push(...data.data.rows)
			// 		}
			// 	}
			// },
			// async store_collectionListApi() {
			// 	let data = await store_collectionList({
			// 		page: '',
			// 		limit: '',
			// 	})
			// },
			// async position_selection() {
			// 	let data = await wApi.getchooseLocation()
			// 	console.log(data);
			// 	uni.setStorageSync('location', data)
			// 	this.store_listApi()
			// },
			goplace(item, e) {
				// uni.setStorageSync('shop', item)
				console.log(item)
				// order_empty()
				uni.reLaunch({
					url: `/pages/order/order?item=${JSON.stringify(item)}`
				})


			},
			// // 下拉加载
			// async store_listApi_down() {
			// 	if (this.index_select == 0) {
			// 		let data = await store_list({
			// 			page: this.page,
			// 			limit: 10,
			// 			search: ''
			// 		})
			// 		console.log(data, '门店列表');
			// 		data.data.rows.forEach(res => {
			// 			res.distance = wApi.mkm(res.distance)
			// 		})
			// 		this.locationname = data.data.address
			// 		this.shoplist.push(...data.data.rows)
			// 	} else {
			// 		let data = await store_collectionList({
			// 			page: this.page,
			// 			limit: 10,
			// 			search: ''
			// 		})
			// 		console.log(data, '门店列表');
			// 		if (data.data) {
			// 			data.data.rows.forEach(res => {
			// 				res.distance = wApi.mkm(res.distance)
			// 			})
			// 			this.locationname = data.data.address
			// 			this.shoplist.push(...data.data.rows)
			// 		}
			// 	}
			// },
		},
		onReachBottom() {
			this.page++
			// this.store_listApi_down()
		}
	}
</script>

<style lang="scss">
	.hade {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;
		padding: 20rpx 0;

		.hade_left {
			display: flex;
			align-items: center;
			font-size: 28rpx;
			font-weight: 400;
			color: #323232;

			view:nth-child(1) {
				padding: 0 30rpx;
			}

			view:nth-child(2) {
				// padding: 0 30rpx;
			}

			.selectCity {
				display: flex;
				align-items: center;
				padding: 10rpx 15rpx;
				box-sizing: border-box;
				border-radius: 12rpx;
				border: 1rpx solid #C3C3C3;
			}

		}

		.hade_right {
			width: 141rpx;
			height: 66rpx;
			background: #F7F7F7;
			border-radius: 33rpx;
			display: flex;
			align-items: center;
			text-align: center;
			font-size: 28rpx;
			font-weight: 400;
			color: #7A7A7A;
			margin-right: 20rpx;

			view:nth-child(1) {
				margin-left: 10rpx;
			}
		}
	}

	.content {
		width: 704rpx;
		margin: 0 auto;

		.content_hade {
			display: flex;
			align-items: center;
			padding: 30rpx 0;

			.content_hade_2 {
				font-size: 28rpx;
				font-weight: 400;
				color: #373737;
				padding: 0 10rpx;
			}
		}

		.map {
			width: 100%;
			height: 399rpx;
		}

		.display {
			height: 67rpx;
			background: #FFFFFF;
			border-radius: 0rpx 0rpx 6rpx 6rpx;

			.display_text {
				width: 150rpx;
				margin: 0 auto;

				.display_text_content {
					display: flex;
					align-items: center;
					padding: 15rpx 0;
					font-size: 24rpx;
					font-weight: 400;
					color: #373737;

					text {
						margin-right: 10rpx;
					}
				}
			}
		}

		.position_box {
			// height: 273rpx;
			background: #FFFFFF;
			border-radius: 6rpx;
			margin: 20rpx 0;

			.chosen_position {
				padding: 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.chosen_position_left {
					width: 70%;

					.chosen_position_left_1 {
						font-size: 32rpx;
						font-weight: 600;
						color: #060606;
					}

					.chosen_position_left_2 {
						display: flex;
						font-size: 24rpx;
						font-weight: 400;
						color: #676767;
						margin-top: 15rpx;
					}

					.chosen_position_left_3 {
						display: flex;
						font-size: 24rpx;
						font-weight: 400;
						color: #676767;
						margin-top: 15rpx;

					}

					.chosen_position_left_4 {
						display: flex;
						align-items: center;
						margin-top: 15rpx;
						// margin-left: 10rpx;

						view:nth-child(1) {
							height: 39rpx;
							border-radius: 4rpx;
							border: 1rpx solid #05B6F6;
							font-size: 24rpx;
							font-weight: 400;
							color: #05B6F6;
							text-align: center;
							box-sizing: border-box;
							padding: 0 10rpx;
							// margin-right: 10rpx;
							margin: 0 10rpx;
							line-height: 37rpx;
						}

						view:nth-child(2) {
							height: 39rpx;
							background: #05B6F6;
							border-radius: 4rpx;
							text-align: center;
							font-size: 24rpx;
							font-weight: 400;
							color: #F3F8FF;
							margin-right: 10rpx;
							padding: 0 10rpx;
							line-height: 39rpx;
						}

						view:nth-child(3) {
							height: 39rpx;
							background: #05B6F6;
							border-radius: 4rpx;
							text-align: center;
							font-size: 24rpx;
							font-weight: 400;
							color: #F3F8FF;
							margin-right: 10rpx;
							padding: 0 10rpx;
							line-height: 39rpx;
						}

						view:nth-child(4) {
							height: 39rpx;
							background: #05B6F6;
							border-radius: 4rpx;
							text-align: center;
							font-size: 24rpx;
							font-weight: 400;
							color: #F3F8FF;
							padding: 0 10rpx;
							line-height: 39rpx;
						}
					}
				}

				.chosen_position_right {
					width: 180rpx;
					text-align: center;
					border-left: 1rpx solid #F0F0F0;
					padding-left: 20rpx;

					.chosen_position_right_1 {
						font-size: 26rpx;
						font-weight: 400;
						color: #05B6F6;
						padding: 20rpx 0;
					}

					.chosen_position_right_2 {
						font-size: 24rpx;
						font-weight: 400;
						color: #666666;
						margin-bottom: 20rpx;
					}

					.chosen_position_right_3 {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.chosen_position_right_3_1 {
							width: 66rpx;
							height: 66rpx;
						}

						.chosen_position_right_3_2 {
							width: 66rpx;
							height: 66rpx;
						}
					}
				}
			}
		}
	}
</style>