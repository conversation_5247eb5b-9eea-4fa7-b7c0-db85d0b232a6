<view class="container data-v-02831c15"><view class="content data-v-02831c15"><view class="form data-v-02831c15"><view class="form-item data-v-02831c15"><text class="label data-v-02831c15"><label class="red _span data-v-02831c15">*</label>收货人</text><input class="value data-v-02831c15" style="text-align:left;" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',['trim']],['stuInfo']]]],['blur',[['$forceUpdate']]]]}}" value="{{stuInfo.name}}" bindinput="__e" bindblur="__e"/></view><view class="form-item data-v-02831c15"><text class="label data-v-02831c15"><label class="red _span data-v-02831c15">*</label>联系电话</text><input class="value data-v-02831c15" style="text-align:left;" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',['trim']],['stuInfo']]]],['blur',[['$forceUpdate']]]]}}" value="{{stuInfo.phone}}" bindinput="__e" bindblur="__e"/></view><view class="form-item data-v-02831c15"><text class="label data-v-02831c15"><label class="red _span data-v-02831c15">*</label>所在地区</text><view class="right selectCity data-v-02831c15"><select-city vue-id="4de0e791-1" type="{{type}}" districtCode="{{stuInfo.pcaCode}}" data-event-opts="{{[['^confirm',[['handleConfirm']]]]}}" bind:confirm="__e" class="data-v-02831c15" bind:__l="__l"></select-city><uni-icons class="icon-right data-v-02831c15" vue-id="4de0e791-2" type="right" size="20" color="#C3C3C3" bind:__l="__l"></uni-icons></view></view><view class="form-item-step-three data-v-02831c15"><text class="label data-v-02831c15"><label class="red _span data-v-02831c15">*</label>详细地址：</text><view class="value-container data-v-02831c15"><textarea class="value-input data-v-02831c15" maxlength="100" placeholder="请输入街道门牌信息" data-event-opts="{{[['input',[['__set_model',['$0','detailPlace','$event',[]],['stuInfo']]]]]}}" value="{{stuInfo.detailPlace}}" bindinput="__e"></textarea></view></view><view class="form-item last-item data-v-02831c15"><text class="data-v-02831c15">设为默认地址</text><u-switch bind:input="__e" vue-id="4de0e791-3" activeColor="#22c8a3" size="40" value="{{stuInfo.isDefault}}" data-event-opts="{{[['^input',[['__set_model',['$0','isDefault','$event',[]],['stuInfo']]]]]}}" class="data-v-02831c15" bind:__l="__l"></u-switch></view></view><view class="toolbar data-v-02831c15"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="pay-btn data-v-02831c15" bindtap="__e">提交</view></view></view></view>