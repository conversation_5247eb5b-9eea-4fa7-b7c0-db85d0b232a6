<view><u-popup vue-id="692f7a2e-1" show="{{show}}" round="{{20}}" safeAreaInsetTop="{{true}}" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="login"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="close" bindtap="__e"><u-icon vue-id="{{('692f7a2e-2')+','+('692f7a2e-1')}}" name="close" size="40" bind:__l="__l"></u-icon></view><view class="hadr"><view>手机号登录/注册</view><view>欢迎登录</view></view><view class="input"><view class="input_1"><view class="input_1_left">手机号</view><input type="text" maxlength="11" placeholder="请输入手机号" data-event-opts="{{[['input',[['__set_model',['','cell','$event',[]]]]]]}}" value="{{cell}}" bindinput="__e"/><block wx:if="{{$root.g0!=11}}"><view class="input_1_right">获取验证码</view></block><block wx:else><view data-event-opts="{{[['tap',[['timeevent',['$event']]]]]}}" class="input_1_right" style="background:#05B6F6;" bindtap="__e"><block wx:if="{{time==60}}"><text>获取验证码</text></block><block wx:else><text>{{time+"s"}}</text></block></view></block></view><view class="input_2"><view class="input_2_left">验证码</view><input type="text" maxlength="4" placeholder="请输入验证码" data-event-opts="{{[['input',[['__set_model',['','code','$event',[]]]]]]}}" value="{{code}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['checkApi',['$event']]]]]}}" class="input_2_right" style="{{'background:'+($root.g1==4?'#05B6F6':'')+';'}}" bindtap="__e">登录</view></view></view><view class="wechat"><block wx:if="{{common.is_shlogin!==1}}"><view class="wechat_division"><u-divider vue-id="{{('692f7a2e-3')+','+('692f7a2e-1')}}" text="手机号快捷登录" textSize="24rpx" bind:__l="__l"></u-divider></view></block><view><block wx:if="{{select}}"><button open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getphonenumber',['$event']]]]]}}" bindgetphonenumber="__e"><view class="wechat_img"><image src="/static/phone.png" mode></image></view></button></block><block wx:else><view data-event-opts="{{[['tap',[['loginClick',['$event']]]]]}}" class="wechat_img" bindtap="__e"><image src="/static/phone.png" mode></image></view></block></view><view class="agreement"><label data-event-opts="{{[['tap',[['selectevent',['$event']]]]]}}" class="radio" bindtap="__e"><radio style="transform:scale(0.7);" value="r1" color="#05B6F6" checked="{{select}}"></radio></label><view><text>登录即代表同意</text><text data-event-opts="{{[['tap',[['routerTo',['/pages/order_all/login_protocol/login_protocol?name='+'用户协议'+'&state='+0]]]]]}}" bindtap="__e">《用户协议》,</text><text data-event-opts="{{[['tap',[['routerTo',['/pages/order_all/login_protocol/login_protocol?name='+'隐私政策'+'&state='+0]]]]]}}" bindtap="__e">《隐私政策》</text><text>及</text><text data-event-opts="{{[['tap',[['routerTo',['/pages/order_all/login_protocol/login_protocol?name='+'第三方SDK类服务商说明'+'&state='+0]]]]]}}" bindtap="__e">《第三方SDK类服务商说明》</text></view></view></view></view></u-popup><u-popup vue-id="692f7a2e-4" closeOnClickOverlay="{{false}}" show="{{usershow}}" round="{{10}}" mode="center" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="user_box"><view class="user_box_title"><view style="margin-left:30rpx;"></view><view>登录后可享受更多功能</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e"><u-icon vue-id="{{('692f7a2e-5')+','+('692f7a2e-4')}}" name="close" size="40" bind:__l="__l"></u-icon></view></view><button open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['setimg',['$event']]]]]}}" bindchooseavatar="__e"><view class="user_avatar"><block wx:if="{{loginavatar}}"><image src="{{loginavatar}}" mode></image></block><block wx:else><image src="../../static/avatar.png" mode></image></block></view></button><view class="user_ipt"><view>昵称</view><input type="nickname" placeholder="请输入昵称" data-event-opts="{{[['change',[['change',['$event']]]],['input',[['__set_model',['','login_name','$event',[]]]]]]}}" value="{{login_name}}" bindchange="__e" bindinput="__e"/></view><view class="user_tag">99%+的用户使用选择微信头像和微信昵
				称，便于订单发货和售后沟通</view><view data-event-opts="{{[['tap',[['wxlogin']]]]}}" class="user_btn" bindtap="__e">立即登录</view></view></u-popup></view>