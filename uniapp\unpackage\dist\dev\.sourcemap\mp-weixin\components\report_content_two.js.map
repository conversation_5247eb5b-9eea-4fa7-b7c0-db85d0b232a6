{"version": 3, "sources": ["webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/report_content_two.vue?6c6b", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/report_content_two.vue?33e9", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/report_content_two.vue?8850", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/report_content_two.vue?390a", "uni-app:///components/report_content_two.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/report_content_two.vue?d547", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/report_content_two.vue?2f1e"], "names": ["props", "list", "type", "required", "default", "computed", "planningList", "console", "created", "filters", "formatName"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2I;AAC3I;AACsE;AACL;AACsC;;;AAGvG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,yGAAM;AACR,EAAE,kHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA4mB,CAAgB,soBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmChoB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAGA;EACAA;IACAC;MACAC;MACAC;MACAC;QAAA;MAAA;IACA;EACA;EACAC;IACAC;MACAC;MACA;IACA;EACA;EACAC,6BAEA;EACAC;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,qpCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/report_content_two.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./report_content_two.vue?vue&type=template&id=4e0955f6&scoped=true&\"\nvar renderjs\nimport script from \"./report_content_two.vue?vue&type=script&lang=js&\"\nexport * from \"./report_content_two.vue?vue&type=script&lang=js&\"\nimport style0 from \"./report_content_two.vue?vue&type=style&index=0&id=4e0955f6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4e0955f6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/report_content_two.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report_content_two.vue?vue&type=template&id=4e0955f6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l2 = _vm.__map(_vm.planningList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l1 = _vm.__map(item.content, function (obj, key) {\n      var $orig = _vm.__get_orig(obj)\n      var l0 = _vm.__map(obj.list, function (value, k) {\n        var $orig = _vm.__get_orig(value)\n        var f0 = _vm._f(\"formatName\")(value.name)\n        return {\n          $orig: $orig,\n          f0: f0,\n        }\n      })\n      return {\n        $orig: $orig,\n        l0: l0,\n      }\n    })\n    return {\n      $orig: $orig,\n      l1: l1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report_content_two.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report_content_two.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"info\" v-for=\"(item, index) in planningList\" :key=\"index\">\r\n\t\t\t<view class=\"info-title\">\r\n\t\t\t\t<image class=\"logo\" src=\"@/static/rhomb.png\" mode=\"\"></image>\r\n\t\t\t\t<text class=\"title-text\">\r\n\t\t\t\t\t{{ item.title }}\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-content\" v-for=\"(obj, key) in item.content\" :key=\"key\">\r\n\t\t\t\t<view class=\"content-title\">\r\n\t\t\t\t\t<view class=\"cricle\">\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content-title-text\">\r\n\t\t\t\t\t\t{{ obj.title }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"subtitle-content-container\">\r\n\t\t\t\t\t<view class=\"subtitle-content\" v-for=\"(value, k) in obj.list\" :key=\"k\">\r\n\t\t\t\t\t\t<view class=\"subtitle-text\">\r\n\t\t\t\t\t\t\t{{ value.name | formatName }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"subtitle-content-text\">\r\n\t\t\t\t\t\t\t<rich-text :nodes=\"value.content\"></rich-text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport sseEvent from \"@/utils/sse_event.js\"\r\n\timport {\r\n\t\tgetReportTagList\r\n\t} from \"@/api/user.js\"\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\trequired: true,\r\n\t\t\t\tdefault: () => []\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tplanningList() {\r\n\t\t\t\tconsole.log(\"list\", this.list)\r\n\t\t\t\treturn this.list\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\r\n\t\t},\r\n\t\tfilters: {\r\n\t\t\tformatName(name) {\r\n\t\t\t\tif (name == \"jyzb_zonghe\") {\r\n\t\t\t\t\treturn \"综合描述\"\r\n\t\t\t\t}\r\n\t\t\t\tif (name == \"zzsh_zonghe\") {\r\n\t\t\t\t\treturn \"综合推荐\"\r\n\t\t\t\t}\r\n\t\t\t\treturn name\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t\tbackground: linear-gradient(180deg, rgba(203, 242, 224, 0.2) 0%, rgba(255, 255, 255, 0) 20%);\r\n\t\tborder-radius: 14rpx;\r\n\t\tborder: 2rpx dashed #1BB394;\r\n\t\tpadding: 24rpx 28rpx;\r\n\r\n\t\t.info {\r\n\t\t\tpadding-top: 30rpx;\r\n\r\n\t\t\t.info-title {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t.logo {\r\n\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.title-text {\r\n\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\tcolor: #4C5370;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.info-content {\r\n\t\t\t\tmargin-top: 20rpx;\r\n\r\n\t\t\t\t.content-title {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t.cricle {\r\n\t\t\t\t\t\twidth: 6rpx;\r\n\t\t\t\t\t\theight: 6rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tbackground-color: #AEE8CD;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.content-title-text {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\tcolor: #5A5A5A;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.subtitle-content {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.subtitle-content-container {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tborder-radius: 9rpx;\r\n\t\t\t\t\tborder: 1rpx solid #A0E4C4;\r\n\t\t\t\t\tpadding: 28rpx;\r\n\t\t\t\t\tpadding-top: 0;\r\n\r\n\t\t\t\t\t.subtitle-text {\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\tpadding: 0 6rpx;\r\n\t\t\t\t\t\tborder-radius: 4rpx 4rpx 4rpx 4rpx;\r\n\t\t\t\t\t\tborder: 1rpx solid #FF9B3A;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\tline-height: 32rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tcolor: #FF9B3A;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.subtitle-content-text {\r\n\t\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\tcolor: #504E4E;\r\n\t\t\t\t\t\tline-height: 24rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report_content_two.vue?vue&type=style&index=0&id=4e0955f6&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report_content_two.vue?vue&type=style&index=0&id=4e0955f6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557569743\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}