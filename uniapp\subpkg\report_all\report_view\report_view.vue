<template>
	<view class="container">
		<!-- 无报告信息时的展示 -->
		<view v-if="!reportData.done" class="no-report">
			<!-- 添加头部导航栏 -->
			<view class="header">
				<view class="nav-bar">
					<uni-icons @tap="back" class="back-icon" type="left" size="20" color="#fff"></uni-icons>
					<text class="title">报告详情</text>
				</view>

			</view>
			<image class="no-data-img"
				src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/no_data.png" mode="widthFix">
			</image>
			<view class="no-data-tip">
				<uni-icons type="trash" size="44" color="#999"></uni-icons>
				<text class="no-data-text">尚无报告信息</text>
			</view>
		</view>

		<!-- 有报告信息时的展示 -->
		<template v-else>
			<view class="header">
				<button class="transparent-button" @click="back">
					<uni-icons class="left-icon" type="left" size="20"></uni-icons>
				</button>
			</view>

			<view class="header">

				<image class="header-img"
					src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/report_header_bg.png"
					mode=" widthFix"></image>
				<text class="teacher-info">学生：{{ reportUserInfo.name }}</text>
			</view>
			<view class="content">
				<view class="base-info">
					<blue-title :title="'第一部分：个人基础信息'"></blue-title>
					<view class="base-info-item-container">
						<view class="base-info-item width-25">
							<text class="item-title">学员姓名：</text>
							<text class="item">{{ reportUserInfo.name }}</text>
						</view>
						<view class="base-info-item">
							<text class="item-title">性别：</text>
							<view class="item" v-if="reportUserInfo.gender ==0">
								未知
							</view>
							<view class="item" v-if="reportUserInfo.gender == 2">
								女
							</view>
							<view class="item" v-if="reportUserInfo.gender == 1">
								男
							</view>
						</view>
						<view class="base-info-item">
							<text class="item-title">本科入学年份：</text>
							<text class="item">{{ reportUserInfo.joinYear }}</text>
						</view>
						<view class="base-info-item  width-25">
							<text class="item-title">本科院校：</text>
							<text class="item">{{ reportUserInfo.schoolName }}</text>
						</view>
						<view class="base-info-item">
							<text class="item-title">学院：</text>
							<text class="item">{{ reportUserInfo.collegeName }}</text>
						</view>
						<view class="base-info-item">
							<view class="item-title">专业：</view>
							<view class="item major">{{ reportUserInfo.majorName }}</view>
						</view>
						<view class="base-info-item  width-25">
							<text class="item-title">学员性格：</text>
							<text class="item">{{ reportUserInfo.personality == 1 ? '内向' : '外向' }}</text>
						</view>
						<view class="base-info-item">
							<text class="item-title">毕业发展：</text>
							<text class="item">{{ reportUserInfo.postGraduationLabel }}</text>
						</view>
					</view>


					<view class="hobby">
						<view class="hobby-item">
							<text class="hobby-title">体育特长：</text>
							<text class="hobby-info">{{ reportUserInfo.sportsInterest }}</text>
						</view>
						<view class="hobby-item">
							<text class="hobby-title">艺术特长：</text>
							<text class="hobby-info">{{ reportUserInfo.artInterest }}</text>
						</view>
						<view class="hobby-item">
							<text class="hobby-title">其它特长：</text>
							<text class="hobby-info">{{ reportUserInfo.academicInterest }}</text>
						</view>
						<view class="hobby-item">
							<text class="hobby-title">综合描述：</text>
							<text class="hobby-info">{{ reportUserInfo.collegePlan }}</text>
						</view>
					</view>

				</view>



				<!-- 高考信息 -->
				<view class="exam-info">
					<view class="base-info">
						<blue-title :title="'第二部分：高考基础信息'"></blue-title>
					</view>

					<view class="table">
						<view class="header">
							<view class="title">
								总分
							</view>
							<view class="title">
								排名
							</view>
							<view class="title">
								位次
							</view>
							<view class="title">
								语文
							</view>
							<view class="title">
								数学
							</view>
							<view class="title">
								外语
							</view>
							<view class="title">
								物理
							</view>
							<view class="title">
								化学
							</view>
							<view class="title">
								生物
							</view>
							<view class="title">
								政治
							</view>
							<view class="title">
								历史
							</view>
							<view class="title">
								地理
							</view>



						</view>
						<view class="table-line">
							<view class="table-line-item">
								{{ reportUserInfo.totalScore }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.rank }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.position }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.chineseScore }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.mathScore }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.foreignLangScore }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.physicsScore ? reportUserInfo.physicsScore : '-' }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.chemistryScore ? reportUserInfo.chemistryScore : '-' }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.biologyScore ? reportUserInfo.biologyScore : '-' }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.politicsScore ? reportUserInfo.politicsScore : '-' }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.historyScore ? reportUserInfo.historyScore : '-' }}
							</view>
							<view class="table-line-item">
								{{ reportUserInfo.geographyScore ? reportUserInfo.geographyScore : '-' }}
							</view>
						</view>
					</view>

				</view>



				<!-- 本科院校信息 -->
				<view class="university-info" v-if="reportInfo.school">
					<blue-title :title="'第三部分：院校基本信息'"></blue-title>
					<view class="university-tag">
						<image class="logo" :src="reportInfo.school.logo" mode=""></image>
						<view class="tag">
							<text class="name">
								{{ reportInfo.school.name }}
							</text>
							<view class="tag-list">
								<view class="tag-list-item" v-for="(tag, index) in reportInfo.school.tags" :key="index">
									{{ tag }}
								</view>
							</view>
						</view>
					</view>
					<report-content :msgtype="schoolInfo"></report-content>
				</view>
				<!-- 就业方向 -->
				<view class="plan">
					<blue-title :title="'第四部分：就业方向'"></blue-title>
					<report-content style='margin-top: 28rpx;' :msgtype="careerDirection"></report-content>
				</view>
				<!-- 升学规划 -->
				<view class="plan">
					<blue-title :title="'第五部分：升学规划'"></blue-title>
					<report-content style='margin-top: 28rpx;' :msgtype="educationPlanning"></report-content>
				</view>

				<view class="ability">
					<blue-title :title="'第六部分：学术能力提升'"></blue-title>
					<report-content style='margin-top: 28rpx;' :msgtype="academicAbilityEnhancement"></report-content>
				</view>

				<view class="university-plan">
					<blue-title :title="'第七部分：大学规划'"></blue-title>
					<report-content-two :list="planningList" :zzsh_zonghe="zzsh_zonghe"
						:jyzb_zonghe="jyzb_zonghe"></report-content-two>
				</view>

				<!-- 预科建议 -->
				<view class="ability">
					<blue-title :title="'第八部分：预科推荐'"></blue-title>
					<report-content style='margin-top: 28rpx;' :msgtype="adviceList"></report-content>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
	import blueTitle from "@/components/blue_title.vue"
	import reportContent from "@/components/report_content.vue"
	import reportContentTwo from "@/components/report_content_two.vue"

	import {
		getReport,
		getUserInfo
	} from "@/api/user.js"
	// import {
	// 	getUserInfo,
	// 	prepareReport
	// } from "@/api/user.js"
	import {
		getToken,
	} from "@/utils/storage.js";
	import {
		mapState
	} from "vuex"
	export default {
		data() {
			return {
				zzsh_zonghe: '',
				jyzb_zonghe: '',
				schoolInfo: [{
						type: 'schoolInfo',
						label: '学校简介',
						content: ''
					},
					{
						type: 'majorInfo',
						label: '专业简介',
						content: ''
					},
					{
						type: 'majorPeople',
						label: '专业招生人数',
						content: ''
					},
					{
						type: 'byl',
						label: '专业保研率',
						content: ''
					},
					{
						type: 'kaoyan',
						label: '专业考研情况',
						content: ''
					}
				],

				//就业方向
				careerDirection: [{
						type: 'bk_jiuye',
						label: '专业本科就业路径'
					},
					{
						type: 'ss_jiuye',
						label: '专业硕士就业路径'
					},
					{
						type: 'bs_jiuye',
						label: '专业博士就业路径'
					}
				],
				educationPlanning: [{
						type: 'gh_zzy',
						label: '转专业'
					},
					{
						type: 'gh_baoyan',
						label: '保研'
					},
					{
						type: 'gh_kaoyan',
						label: '考研'
					},
					{
						type: 'gh_liuxue',
						label: '留学'
					}
				],
				academicAbilityEnhancement: [{
						type: 'ts_z_js',
						label: '专业相关竞赛'
					},
					{
						type: 'ts_n_js',
						label: '非专业相关竞赛'
					},
					{
						type: 'ts_z_ky',
						label: '专业相关科研'
					},
					{
						type: 'ts_n_ky',
						label: '可跨专业的相关科研'
					},
					{
						type: 'ts_z_zs',
						label: '专业相关证书'
					},
					{
						type: 'ts_n_zs',
						label: '非专业相关证书'
					}
				],
				universityPlanning: [

				],
				organizationalLife: [{
						type: 'zzsh_sport',
						label: '体育特长',
						key: 'sportsInterest'
					},
					{
						type: 'zzsh_art',
						label: '艺术特长',
						key: 'artInterest'
					},
					{
						type: 'zzsh_qita',
						label: '其他特长',
						key: 'academicInterest'
					},
					{
						type: 'zzsh_zonghe',
						label: '综合描述',
						key: 'collegePlan'
					}
				],
				adviceList: [],
				reportData: {
					done: true
				},
				planningList: [{
					title: "大一期间核心学业规划",
					content: {
						zzsh: {
							title: '组织生活',
							list: []
						},
						xygh: {
							title: '学业规划',
							list: []
						},
						sxbk: {
							title: '升学准备',
							list: []
						},
						jyzb: {
							title: '就业指导',
							list: []
						}
					}
				}],
				data: {},
				from: 0, //0：客户录入  1：激活码注册
				reportStatus: 0, //0: 未激活   1：服务已激活  2. 服务已使用
			};
		},
		computed: {
			...mapState('user', ['reportInfo', 'reportUserInfo'])
		},
		components: {
			blueTitle,
			reportContent,
			reportContentTwo
		},
		onLoad() {
			const userInfo = uni.getStorageSync('user') || {};
		},
		created() {
			this.getReportData()
		},
		methods: {
			// 获取学员信息
			async getUserDetails() {
				try {
					const res = await getUserInfo()
					if (res.errCode === 0) {
						this.data = res.data
						this.from = res.data.from
						// 如果等于1代表使用激活码激活已经生成报告，没保存，跳转到生成报告页面
						if (res.data.reportStatus == 1) {
							this.getReports()
						} else {
							this.getReportData()
						}
					} else {
						uni.tip(res.msg)
					}
				} catch (e) {
					uni.removeStorageSync('USER-TOKEN')
					this.$store.commit('clearToken');

				}
			},
			async getReports() {
				try {
					const result = await prepareReport(this.data)
					if (result.errCode == 0) {
						//记录报告信息
						this.$store.commit('user/setReportInfo', result.data);
						//记录报告的用户信息
						// this.$store.commit('user/setReportUserInfo', this.stuInfo);
						this.$store.commit('user/setReportUserInfo', this.data);
						uni.navigateTo({
							url: '/subpkg/plan/plan'
						})
					} else {
						uni.tip(result.msg)
					}

				} catch (e) {
					console.error(e)
				} finally {
					this.showAnimation = false
				}
			},
			async getReportData() {
				// if (!getToken()) {

				// 	uni.navigateTo({
				// 		url: '/subpkg/login/login'
				// 	})
				// 	return
				// }
				try {
					const res = await getReport()
					if (res.errCode === 0) {
						// 检查是否有报告数据
						// if (!res.data || !res.data.school) {
						// 	this.reportData.done = false
						// 	return
						// }
						this.reportData.done = res.data.id ? true : false
						if (!res.data.id) {
							return
						}
						// 处理基础用户信息
						const userInfo = {
							name: res.data.name,
							gender: res.data.gender,
							joinYear: res.data.joinYear,
							schoolName: res.data.schoolName,
							collegeName: res.data.collegeName,
							majorName: res.data.majorName,
							personality: res.data.personality,
							postGraduationLabel: this.getPostGraduationLabel(res.data.postGraduation),
							sportsInterest: res.data.sportsInterest || '',
							artInterest: res.data.artInterest || '',
							academicInterest: res.data.academicInterest || '',
							collegePlan: res.data.collegePlan || '',
							// 高考成绩信息
							totalScore: res.data.totalScore,
							rank: res.data.rank,
							position: res.data.position,
							chineseScore: res.data.chineseScore,
							mathScore: res.data.mathScore,
							foreignLangScore: res.data.foreignLangScore,
							physicsScore: res.data.physicsScore,
							chemistryScore: res.data.chemistryScore,
							biologyScore: res.data.biologyScore,
							politicsScore: res.data.politicsScore,
							historyScore: res.data.historyScore,
							geographyScore: res.data.geographyScore
						}

						// 处理学校信息
						const schoolInfo = {
							school: {
								logo: res.data.school?.logo || '',
								name: res.data.school?.name || '',
								tags: res.data.school?.tags || []
							}
						}

						// 更新学校相关信息内容
						this.schoolInfo = this.schoolInfo.map(item => ({
							...item,
							content: res.data[item.type] || ''
						}))

						// 更新就业方向内容
						this.careerDirection = this.careerDirection.map(item => ({
							...item,
							content: res.data[item.type] || ''
						}))

						// 更新升学规划内容
						this.educationPlanning = this.educationPlanning.map(item => ({
							...item,
							content: res.data[item.type] || ''
						}))

						// 更新学术能力提升内容
						this.academicAbilityEnhancement = this.academicAbilityEnhancement.map(item => ({
							...item,
							content: res.data[item.type] || ''
						}))

						// 处理预科建议数据
						if (res.data.advice && Array.isArray(res.data.advice)) {
							this.adviceList = res.data.advice.map(item => ({
								type: item.id,
								label: item.name,
								content: item.content
							}))
						}

						// 更新大学规划内容
						if (res.data.grade_1) {
							const grade1 = res.data.grade_1
							Object.keys(grade1).forEach(key => {
								if (this.planningList[0].content[key]) {
									this.planningList[0].content[key].list = grade1[key]
								}
							})
						} else {
							Object.keys(res.data).forEach(key => {
								if (this.planningList[0].content[key]) {
									this.planningList[0].content[key].list = res.data[key]
								}
							})
						}
						if (res.data.zzsh_zonghe) {
							this.planningList[0].content.zzsh.list.push({
								name: "综合描述推荐",
								content: res.data.zzsh_zonghe
							})
						}
						if (res.data.jyzb_zonghe) {
							this.planningList[0].content.jyzb.list.push({
								name: "综合描述推荐",
								content: res.data.jyzb_zonghe
							})
						}
						// 存储到 vuex
						this.$store.commit('user/setReportUserInfo', userInfo)
						this.$store.commit('user/setReportInfo', schoolInfo)

						this.reportData.done = true
					}
				} catch (e) {
					console.error('获取报告失败:', e)
					uni.showToast({
						title: '获取报告失败',
						icon: 'none'
					})
					this.reportData.done = false
				}
			},
			// 处理毕业发展选项
			getPostGraduationLabel(postGraduation) {
				if (!postGraduation) return ''
				const labels = []
				if (postGraduation.includes('1')) labels.push('考研')
				if (postGraduation.includes('2')) labels.push('保研')
				if (postGraduation.includes('3')) labels.push('就业')
				if (postGraduation.includes('4')) labels.push('留学')
				return labels.join('、')
			},
			// 返回上一页
			back() {
				const pages = getCurrentPages(); // 获取页面栈
				if (pages.length < 2) {
					uni.switchTab({
						url: '/pages/admission_report/admission_report'
					})
					return;
				}

				const prevPage = pages[pages.length - 2]; // 上一个页面实例
				const prevRoute = prevPage.route; // 上一个页面的路径，如 "/pages/home/<USER>"
				uni.switchTab({
					url: '/' + prevRoute
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding-bottom: 20px;
	}

	.back {
		position: absolute;
		top: 30rpx;
		left: 30rpx;
	}

	.header {

		.header-img {
			width: 100%;
			height: 1070rpx;
		}

		.teacher-info {
			position: absolute;
			top: 396rpx;
			left: 520rpx;
			width: 200rpx;
			height: 50rpx;
			line-height: 50rpx;
			text-align: center;
			color: #000;
		}
	}

	.content {
		padding-top: 28rpx;
		padding: 0 30rpx;

		.base-info-item-container {
			width: 100%;
			margin-top: 18rpx;
			font-size: $primary-font-size;
			display: flex;
			align-content: space-between;
			flex-wrap: wrap;
			justify-content: flex-start;

			.base-info-item {
				width: 33%;
				display: flex;
				align-items: start !important;
				margin-right: 10rpx;
				justify-content: flex-start;

				::v-deep .uni-body {
					line-height: 0 !important;
				}

				/* 显示省略号 */
				.item-title {
					font-weight: 400;
					font-size: $primary-font-size;
					color: #5A5A5A;
				}

				.item {
					color: #5A5A5A;

				}

				::v-deep .major {
					width: 80%;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					/* 限制显示行数 */
					-webkit-box-orient: vertical;
					overflow: hidden;
					text-overflow: ellipsis;
					line-height: 25rpx !important;
					margin-top: 10rpx;
				}

				.wrapItem {
					width: 80%;
					white-space: nowrap;
					overflow: hidden;
					/* 隐藏溢出内容 */
					text-overflow: ellipsis;
				}

			}

			.width-25 {
				width: 28%;
			}


		}

		/*爱好*/
		.hobby {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: flex-start;

		}

		.hobby-item {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			border: 1rpx solid #A0E4C4;
			border-radius: 4rpx;
			padding: 8rpx 12rpx;
			margin-top: 18rpx;
		}

		.hobby-title {
			font-weight: bold;
			font-size: $primary-font-size;
			color: #5A5A5A;
			width: 100rpx;
		}

		.hobby-info {
			font-weight: 400;
			font-size: $primary-font-size;
			color: #5A5A5A;
			margin-left: 14rpx;
			width: 80%;
		}


		.exam-info {
			margin-top: 20rpx;

			.table {
				margin-top: 20rpx;
				height: 48rpx;
				border-radius: 8rpx;
				border: 1rpx solid #1BB394;
				font-size: 12rpx;

				.header,
				.table-line {
					height: 24rpx;
					border-bottom: 1rpx solid #1BB394;
					display: flex;
					align-items: flex-start;
					align-items: center;

					.title,
					.table-line-item {
						height: 24rpx;
						line-height: 24rpx;
						flex: 1;
						border-right: 1rpx solid #1BB394;
						text-align: center;

						&:last-child {
							border: 0;

						}

					}
				}

				.table-line {
					border: 0;
				}
			}
		}


		.university-info {
			margin-top: 32rpx;

			.university-tag {
				margin-top: 18rpx;
				margin-bottom: 32rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.logo {
					width: 70rpx;
					height: 70rpx;
					margin-right: 24rpx;
				}

				.tag {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: flex-start;

					.name {
						font-weight: bold;
						font-size: 20rpx;
						color: #5A5A5A;
						width: 100%;
						text-align: left;
					}

					.tag-list {
						margin-top: 10rpx;
						width: 100%;
						display: flex;
						align-items: center;
						// justify-content: space-between;

						.tag-list-item {
							width: 68rpx;
							height: 30rpx;
							background: #FFB975;
							border-radius: 6rpx;
							font-weight: 400;
							font-size: 14rpx;
							color: #FFFFFF;
							line-height: 30rpx;
							text-align: center;
							margin-right: 15rpx;
						}
					}
				}
			}
		}

		/* 升学规划*/
		.plan,
		.ability,
		.university-plan {
			margin-top: 32rpx;

		}
	}

	.no-report {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;

		.no-data-img {
			width: 360rpx;
			margin-bottom: 40rpx;
		}

		.no-data-tip {
			display: flex;
			align-items: center;
			flex-direction: column;
			justify-content: center;
			gap: 44rpx;
		}

		.no-data-text {
			font-size: 32rpx;
			color: #999;
			font-weight: 500;
		}
	}

	/* 添加头部导航栏样式 */
	.header {
		background-color: #fff;

		.nav-bar {
			width: 100vw;
			height: 88rpx;
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			position: relative;
			background-color: #1BB394;
			color: #fff;

			.back-icon {
				position: absolute;
				left: 30rpx;
			}

			.title {
				flex: 1;
				text-align: center;
				font-size: 32rpx;
				font-weight: 500;
				color: #fff;
			}
		}
	}

	.headers {
		position: fixed;
		background-color: transparent !important;

		.nav-bar {
			width: 100vw;
			height: 88rpx;
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			position: relative;
			background-color: transparent !important;
			color: #888;
		}
	}

	uni-button:after {
		content: none !important;
		border: none !important;
	}

	.transparent-button {
		position: absolute;
		top: 30rpx;
		left: 30rpx;
		z-index: 99999;
		padding: 0;
		margin: 0;
		background: transparent;
		border: none;
		outline: none;
	}
</style>