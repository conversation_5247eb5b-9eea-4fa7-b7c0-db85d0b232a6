@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container .top.data-v-d2166d5e {
  width: 690rpx;
  margin: 30rpx auto;
  padding: 30rpx 30rpx 45rpx;
  box-sizing: border-box;
  background-color: #fff;
}
.container .top .tip .tip-text.data-v-d2166d5e {
  position: relative;
  display: inline-block;
  font-weight: 800;
  font-size: 30rpx;
  color: #060606;
  z-index: 100;
}
.container .top .tip .tip-text.data-v-d2166d5e::after {
  content: "";
  position: absolute;
  bottom: -6rpx;
  left: 0;
  width: 104%;
  height: 20rpx;
  /* 指定高度 */
  background-color: #DBFF9C;
  /* 底部背景颜色 */
  z-index: -1;
}
.container .top .ma.data-v-d2166d5e {
  width: 360rpx;
  height: 360rpx;
  font-weight: 400;
  font-size: 30rpx;
  color: #FFFFFF;
  margin: 30rpx auto 0;
  position: relative;
}
.container .top .ma image.data-v-d2166d5e {
  position: absolute;
  width: 360rpx;
  height: 360rpx;
}
.container .top .ma text.data-v-d2166d5e {
  position: absolute;
  top: 220rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 9;
}
.container .top input.data-v-d2166d5e {
  width: 563rpx;
  height: 106rpx;
  text-align: center;
  font-size: 32rpx;
  color: #5A5A5A;
  margin: 50rpx auto 80rpx;
  background: #F6F7FB;
  border-radius: 16rpx;
}
.container .top .btns.data-v-d2166d5e {
  width: 550rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: bold;
  font-size: 30rpx;
  color: #FFFFFF;
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  border-radius: 40rpx;
  margin: 0 auto;
}
.container.data-v-d2166d5e  .u-popup__content {
  background-color: transparent !important;
}
.container .popupContent.data-v-d2166d5e {
  width: 555rpx;
  height: 587rpx;
  padding: 40rpx 30rpx 50rpx;
  box-sizing: border-box;
  background: url(https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/ticket_bg.png) no-repeat;
  background-size: 100%;
}
.container .content.data-v-d2166d5e {
  height: 250rpx;
}
.container .content .title.data-v-d2166d5e {
  font-weight: bold;
  font-size: 26rpx;
  color: #060606;
  line-height: 1.4;
}
.container .content .detail.data-v-d2166d5e {
  margin-top: 12rpx;
  display: flex;
  align-items: center;
  color: #A4A4A4;
}
.container .content .detail text.data-v-d2166d5e {
  font-weight: 500;
  font-size: 24rpx;
  color: #A4A4A4;
}
.container .content .detail .subject.data-v-d2166d5e {
  font-weight: bold;
  font-size: 22rpx;
  color: #09CC8C;
  padding: 9rpx 7rpx;
  background: #EEFAF6;
  border-radius: 10rpx;
}
.container .content .detail .endTime.data-v-d2166d5e {
  margin: 0 12rpx;
  position: relative;
}
.container .content .detail .endTime.data-v-d2166d5e::after {
  content: '';
  position: absolute;
  width: 1rpx;
  height: 25rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: -12rpx;
  background-color: #A4A4A4;
}
.container .content .detail .num.data-v-d2166d5e {
  margin-left: 12rpx;
}
.container .content .bottom.data-v-d2166d5e {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .content .bottom .teacher-list.data-v-d2166d5e {
  padding-left: 10rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.container .content .bottom .teacher-list .teacher-info.data-v-d2166d5e {
  margin-right: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #818181;
  margin-bottom: 20rpx;
}
.container .content .bottom .teacher-list .teacher-info.data-v-d2166d5e:last-child {
  margin-right: 0;
}
.container .content .bottom .teacher-list .teacher-info .avatar.data-v-d2166d5e {
  width: 60rpx;
  height: 60rpx;
  border-radius: 100%;
  margin-bottom: 2rpx;
}
.container .btn.data-v-d2166d5e {
  width: 390rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: bold;
  font-size: 30rpx;
  color: #FFFFFF;
  margin: 50rpx auto 25rpx;
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  border-radius: 40rpx;
}
.container .btn.data-v-d2166d5e:last-child {
  color: #989898;
  margin: 0 auto 0;
  background: #F6F7FB;
}

