<view class="container"><view class="top-banner"><image src="{{detail.course_cover}}" mode="widthFix"></image></view><view class="title"><text class="title-name">{{detail.name}}</text><view class="center"><text>{{"开课时间："+(detail.start_time||'')+"-"+(detail.end_time||'')}}</text><text class="u-border-left">{{"课时："+(detail.hours||'-')+"节"}}</text></view><text class="money u-border-bottom">{{"￥"+detail.checkout_price}}</text></view><view class="tabs"><view class="tabsBox"><u-tabs vue-id="6329a682-1" list="{{type==1?list:lists}}" lineWidth="50" lineHeight="10" lineColor="url(https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/wan.png) 100% 100%" activeStyle="{{({color:'#26C8AC',fontWeight:'bold',transform:'scale(1.1)'})}}" inactiveStyle="{{({fontSize:'28rpx',color:'#777777',fontWeight:'bold',transform:'scale(1)'})}}" itemStyle=" height: 82rpx;" data-event-opts="{{[['^click',[['chooseItem']]]]}}" bind:click="__e" bind:__l="__l"></u-tabs></view><view hidden="{{!(currentIndex==0)}}" class="course-detail"><block wx:if="{{$root.g0>1}}"><view class="dir-tab"><block wx:for="{{detail_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['checkDetail',['$0',index],[[['detail_list','',index,'detail']]]]]]]}}" class="{{[listIndex==index?'sel':'']}}" bindtap="__e">{{item.cate}}</text></block></view></block><view class="copy-content"><rich-text nodes="{{detailImg}}"></rich-text></view></view><view hidden="{{!(currentIndex==1)}}" class="course-dir padding-dir"><block wx:if="{{$root.g1}}"><view class="dir-tab dir-tab-index"><block wx:for="{{listenList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['listenClick',['$0',index],[[['listenList','',index]]]]]]]}}" class="{{[listenIndex==index?'sel':'']}}" bindtap="__e">{{item.cate}}</text></block></view><view class="live-info"><view class="live-title"><text class="green-block"></text><text>试听课</text></view><block wx:for="{{videoList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="live-date"><view class="live-name"><text>{{item.title}}</text></view><view data-event-opts="{{[['tap',[['studyClick',['$0'],[[['videoList','',index]]]]]]]}}" class="btn-enter" bindtap="__e">进入试听</view></view></block></view></block><view class="dir-tab m-t-30 dir-tab-index"><block wx:for="{{index_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['listClick',['$0',index],[[['index_list','',index]]]]]]]}}" class="{{[indexActive==index?'sel':'']}}" bindtap="__e">{{item.cate}}</text></block></view><view class="dir-tabs"><u-tabs vue-id="6329a682-2" list="{{moduleDetail}}" lineWidth="40" lineHeight="10" lineColor="url(https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/wan.png) 100% 100%" activeStyle="{{({color:'#26C8AC',fontWeight:'bold',transform:'scale(1.1)'})}}" inactiveStyle="{{({fontSize:'26rpx',color:'#777777',fontWeight:'bold',transform:'scale(1)'})}}" itemStyle="box-sizing: border-box; padding-right: 20rpx; height: 82rpx;" current="{{activeTabIndex}}" data-event-opts="{{[['^click',[['chooseDirItem']]]]}}" bind:click="__e" bind:__l="__l"></u-tabs></view><view class="course-container"><block wx:for="{{classList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><uni-collapse vue-id="{{'6329a682-3-'+index}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="course-radius"><uni-collapse-item vue-id="{{('6329a682-4-'+index)+','+('6329a682-3-'+index)}}" title="{{item.name}}" show-animation="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{item.indexes}}" wx:for-item="items" wx:for-index="k" wx:key="k"><view class="content"><text class="text">{{items.name}}</text></view></block></uni-collapse-item></view></uni-collapse></block></view></view></view><view class="bottom-opt"><view class="left"><text class="price">{{"￥"+detail.checkout_price}}</text></view><block wx:if="{{!user.user}}"><view class="btnBox"><view data-event-opts="{{[['tap',[['carClick',['$event']]]]]}}" class="car" bindtap="__e">加入购物车</view></view></block></view><u-popup vue-id="6329a682-5" show="{{showCoupon}}" mode="bottom" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="coupon-conainer"><view class="title-img"><image src="/static/good.png" mode></image><text>加入购物车成功</text></view><view class="btn-bottom"><view data-event-opts="{{[['tap',[['closeMask',['$event']]]]]}}" class="btn" bindtap="__e">继续逛</view><view data-event-opts="{{[['tap',[['goToCart',['$event']]]]]}}" class="btn" bindtap="__e">去购物车</view></view></view></u-popup></view>