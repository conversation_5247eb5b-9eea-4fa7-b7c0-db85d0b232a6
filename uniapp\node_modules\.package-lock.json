{"name": "uniapp", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/@dcloudio/uni-ui": {"version": "1.5.10", "resolved": "https://registry.npmmirror.com/@dcloudio/uni-ui/-/uni-ui-1.5.10.tgz", "integrity": "sha512-v6ylkGSUF6hhgSerm8aVEQE9SBkKz3oNDzorkVC0KLHfulMbkacJQ92YFSQ0kCGeudupVqXuPwNTFjKJF5Qolw=="}, "node_modules/cos-js-sdk-v5": {"version": "1.10.1", "resolved": "https://registry.npmmirror.com/cos-js-sdk-v5/-/cos-js-sdk-v5-1.10.1.tgz", "integrity": "sha512-a4SRfCY5g6Z35C7OWe9te/S1zk77rVQzfpvZ33gmTdJQzKxbNbEG7Aw/v453XwVMsQB352FIf7KRMm5Ya/wlZQ==", "hasInstallScript": true, "dependencies": {"fast-xml-parser": "4.5.0"}}, "node_modules/fast-xml-parser": {"version": "4.5.0", "resolved": "https://registry.npmmirror.com/fast-xml-parser/-/fast-xml-parser-4.5.0.tgz", "integrity": "sha512-/PlTQCI96+fZMAOLMZK4CWG1ItCbfZ/0jx7UIJFChPNrx7tcEgerUgWbeieCM9MfHInUDyK8DWYZ+YrywDJuTg==", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}, {"type": "paypal", "url": "https://paypal.me/naturalintelligence"}], "dependencies": {"strnum": "^1.0.5"}, "bin": {"fxparser": "src/cli/cli.js"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "node_modules/moment": {"version": "2.30.1", "resolved": "https://registry.npmmirror.com/moment/-/moment-2.30.1.tgz", "integrity": "sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==", "engines": {"node": "*"}}, "node_modules/strnum": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/strnum/-/strnum-1.1.2.tgz", "integrity": "sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}]}, "node_modules/uni-read-pages": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/uni-read-pages/-/uni-read-pages-1.0.5.tgz", "integrity": "sha512-GkrrZ0LX0vn9R5k6RKEi0Ez3Q3e2vUpjXQ8Z6/K/d28KudI9ajqgt8WEjQFlG5EPm1K6uTArN8LlqmZTEixDUA==", "dev": true, "hasInstallScript": true}, "node_modules/uni-simple-router": {"version": "2.0.8-beta.4", "resolved": "https://registry.npmmirror.com/uni-simple-router/-/uni-simple-router-2.0.8-beta.4.tgz", "integrity": "sha512-ipTHhOaRvjV8qrt3HosX5pNMhwFYBnFOuKyV5joH0evfXubjrGI5tjdwpmwzfW5h3VBth3iAqScv+pW/QmIJXw=="}, "node_modules/uview-ui": {"version": "2.0.31", "resolved": "https://registry.npmmirror.com/uview-ui/-/uview-ui-2.0.31.tgz", "integrity": "sha512-I/0fGuvtiKHH/mBb864SGYk+SJ7WaF32tsBgYgeBOsxlUp+Th+Ac2tgz2cTvsQJl6eZYWsKZ3ixiSXCAcxZ8Sw==", "engines": {"HBuilderX": "^3.1.0"}}}}