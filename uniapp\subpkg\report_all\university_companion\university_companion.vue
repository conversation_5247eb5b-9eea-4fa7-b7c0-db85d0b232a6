<template>
	<view class="container">

		<!-- 当月任务 -->
		<view class="task-section">
			<view class="task-header">
				<view class="tip" :style="{ visibility: showMask ? 'hidden' : 'visible' }">
					<text class="tip-text">{{ isCurrentMonth ? '当月任务' : formatDisplayDate(currentDate) + '任务' }}</text>
				</view>
				<picker mode="date" fields="month" :value="currentDate" @change="handleDateChange">
					<image src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/calendar.png"
						style="height: 60rpx; width: 60rpx;"></image>
				</picker>
			</view>
			<view class="title">
				<view class="tag">
					接收率
				</view>
				<view class="progress">
					<view class="overlay" :style="{
						width: readRate + '%',
						backgroundImage: `url(${progressImg})`,
					}">

					</view>
				</view>
				<view class="title-info">
					{{ readRate }}%
				</view>
			</view>
			<view class="title">
				<view class="tag">
					完成率
				</view>
				<view class="progress">
					<view class="overlay" :style="{
						width: doneRate + '%',
						backgroundImage: `url(${progressPink})`
					}">

					</view>
				</view>
				<view class="title-info">
					{{ doneRate }}%
				</view>
			</view>
			<!-- 任务列表 -->
			<view class="task-list">
				<view v-for="(task, index) in tasks" :key="index" class="task-item">
					<text class="task-index">{{ index + 1 | padNum }}</text>
					<view class="task-title">
						<text>{{ task.name }}</text>
						<view class="btnBox">
							<button @click="upload(task)" class="detail" v-if="task.status==0">上传</button>
							<button @click="showDetail(task)" class="detail">详情</button>
						</view>

					</view>
					<view class="tagBox">
						<view class="tag" v-if="task.isRead">已接收</view>
						<view class="tag tags" v-if="task.status==1">已完成</view>
					</view>

				</view>
			</view>
		</view>

		<!-- 综合建议 -->
		<view class="suggestion-section">
			<view class="task-header">
				<view class="tip">
					<text class="tip-text">综合建议</text>
				</view>
				<image @click="showAllSugesstion"
					src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/university_companion_records.png"
					style="height: 60rpx; width: 60rpx;"></image>
			</view>

			<view v-if="lastSuggestion.length>0" class="suggestion-content">
				<view v-for="(item, index) in lastSuggestion" :key="index">
					<rich-text :nodes="item.content"></rich-text>
					<view class="imgs" v-if="item.image">
						<image class="img" :src="item.image" mode="aspectFill">
						</image>
					</view>
				</view>


			</view>
			<view v-else class="suggestion-content">
				<text class="line">暂无建议</text>
			</view>
		</view>

		<view class="mask" v-if="showMask" @tap.self="close">
			<view class="content">
				<view class="content-scroll">
					<template v-if="flag == 0">
						<view class="title">
							详细内容
						</view>
						<view class="task-content">
							<view class="tip">
								<text class="tip-text">任务主题</text>
							</view>
							<view class="task-content-text">
								{{ taskDetail.list.content }}
							</view>
						</view>

						<view class="task-content" v-if="taskDetail.list.suggestion">
							<view class="tip">
								<text class="tip-text">完成建议</text>
							</view>
							<view class="task-content-list-text" v-if="suggestionList.length">
								<text v-for="(text, i) in suggestionList" :key="i">
									{{ text }}
								</text>
							</view>
							<view class="task-content-text" v-else>
								{{ taskDetail.list.suggestion }}
							</view>
						</view>

						<view class="task-content" v-if="taskDetail.list.purpose">
							<view class="tip">
								<text class="tip-text">任务作用</text>
							</view>
							<view class="task-content-list-text" v-if="purposeList.length">
								<text v-for="(text, i) in purposeList" :key="i">
									{{ text }}
								</text>
							</view>
							<view class="task-content-text" v-else>
								{{ taskDetail.list.purpose }}
							</view>
						</view>

						<view class="task-content" v-if="taskDetail.list.image">
							<view class="task-content-image">
								<image :src="taskDetail.list.image" mode="widthFix"></image>
							</view>
						</view>

						<view class="down-load" v-if="taskDetail.list.attachment">
							<view class="down-load-icon">
								<image class="icon" src="../../static/imgs/zip_icon.png" mode=""></image>
								<text class="text">{{ taskDetail.downLoadName }}</text>
							</view>
							<button class="down-load-btn" @click="downLoad(taskDetail.list)">下载</button>
						</view>
					</template>
					<template v-if="flag == 1">
						<view class="history-list">
							<view v-if="historySugestionList.length === 0" class="history-item">
								<view class="history-content">
									暂无历史建议记录
								</view>
							</view>
							<view v-else class="history-item" v-for="(item, i) in historySugestionList" :key="i">
								<view class="history-date">
									<text class="blue-block">

									</text>
									<text>{{ item.createTime }}</text>
								</view>

								<rich-text :nodes="item.content"></rich-text>

								<image :src="item.image"></image>
							</view>
						</view>
					</template>
				</view>

				<view v-if="flag == 0" class="bottom-btn-container">
					<button class="bottom-btn" :class="{ 'bottom-btn-disabled': currentMaterial.isRead === true }"
						@click="handleTaskDone">
						{{ currentMaterial.isRead === true ? '已知晓' : '已知晓阅读' }}
					</button>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import userTitle from "@/components/user_title.vue"
	// import http from '@/http/index.js'
	import {
		getEncryptedAttachment
	} from '@/api/user.js'
	export default {
		data() {
			return {
				currentDate: this.getCurrentYearMonth(), // 当前选择的年月
				tasks: [], // 当月任务列表
				readRate: 0, //已接收百分比
				doneRate: 0, //已完成百分比
				progressImg: "data:image/png;base64,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",
				progressPink: "data:image/png;base64,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",
				flag: 0, //详情还是综合建议 0,是详情 1,综合建议
				taskDetail: {
					downLoadName: '',
					list: {
						title: '',
						content: '',
						image: ''
					}
				},
				showMask: false,
				sugestionText: '', // 当前建议文本
				historySugestionList: [], // 历史建议列表
				isCurrentMonth: true, // 是否是当前月份
				currentSuggestion: null, // 当前显示的建议
				currentTaskId: '', // 当前任务ID
				currentMaterial: null, // 当前查看的材料
				purposeList: [], // 任务作用列表
				suggestionList: [], // 完成建议列表
				lastSuggestion: [], //最后一条完成建议
			};
		},
		methods: {
			// 关闭弹框
			close() {
				if (this.flag == 1) {
					this.showMask = false
				}
			},
			// 在页面中检测环境
			isWeixinBrowser() {
				const ua = navigator.userAgent.toLowerCase();
				return ua.includes('micromessenger');
			},
			// 点击下载
			downLoad(item) {
				if (this.isWeixinBrowser()) {
					// 微信环境下显示提示并引导浏览器打开
					// 关键步骤：对URL进行编码
					// const encodedUrl = encodeURIComponent(fileUrl.attachment);
					this.getDownloadUrl(item.id)
				} else {
					// 非微信环境直接下载
					this.directDownload(item);
				}
			},
			async getDownloadUrl(id) {
				try {
					const {
						data,
						errCode,
						msg
					} = await getEncryptedAttachment(id)
					if (errCode == 0) {
						uni.navigateTo({
							url: `/subpkg/download/download?urlPath=${data}`
						});
					} else {
						uni.showToast({
							title: msg,
							icon: 'none'
						})
					}
				} catch (e) {
					uni.showToast({
						title: msg,
						icon: 'none'
					})
				}
			},
			// 直接下载方法
			directDownload(url) {
				const link = document.createElement('a');
				link.href = url.attachment;
				link.download = url.fileName; // 设置默认文件名
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);
			},
			// 点击上传
			upload(item) {
				if (!item.isRead) {
					this.showDetail(item)
				} else {
					uni.navigateTo({
						url: `/subpkg/upload_content/upload_content?id=${item.id}&taskId=${item.taskId}`
					})
				}

			},
			showDetail(material) {
				this.flag = 0
				// 保存当前查看的材料
				this.currentMaterial = material
				// 处理任务作用文本，如果包含换行符则转为数组
				this.purposeList = material.purpose ?
					material.purpose.split('\n').filter(item => item.trim()) : []

				// 处理完成建议文本
				this.suggestionList = material.suggestion ?
					material.suggestion.split('\n').filter(item => item.trim()) : []

				// 设置任务详情
				this.taskDetail = {
					downLoadName: material.fileName ?
						(material.fileName.length > 10 ? material.fileName.slice(0, 10) + '...' : material
							.fileName) : '附件',
					list: {
						content: material.name,
						suggestion: material.suggestion,
						purpose: material.purpose,
						image: material.image || '',
						attachment: material.attachment,
						fileName: material.fileName,
						id: material.id
					}
				}
				this.showMask = true
			},
			showAllSugesstion() {
				this.flag = 1
				this.showMask = true
			},
			// 获取当前年月
			getCurrentYearMonth() {
				const date = new Date()
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
			},
			// 格式化显示日期
			formatDisplayDate(dateStr) {
				const [year, month] = dateStr.split('-')
				return `${year}年${month}月`
			},
			// 检查是否是当前月份
			checkIsCurrentMonth(dateStr) {
				const currentDate = this.getCurrentYearMonth()
				return currentDate === dateStr
			},
			// 处理日期选择变化
			handleDateChange(e) {
				this.currentDate = e.detail.value
				this.isCurrentMonth = this.checkIsCurrentMonth(this.currentDate)
				// 获取选中月份的数据
				this.fetchMonthData(this.currentDate)
			},
			// 获取指定月份的数据
			async fetchMonthData(yearMonth) {
				// 检查登录状态，未登录直接返回
				if (!this.$store.getters.token) {
					return
				}

				try {
					const res = await uni.http.get('/stu/studentTask/listByMonth', {
						yearMonth
					})

					if (res.errCode === 0) {
						// 初始化数据为空
						this.tasks = []
						this.completionRate = 0
						this.currentTaskId = ''
						this.sugestionText = ''
						this.historySugestionList = []
						this.currentSuggestion = null

						// const taskData = res.data.data[0]
						if (res.data.data.length > 0) {
							// 保存当前任务ID
							// this.currentTaskId = taskData.id
							// 设置任务列表
							this.tasks = res.data.data.map(item => ({
								id: item.id,
								name: item.name,
								status: item.status,
								fileName: item.fileName,
								image: item.image,
								suggestion: item.suggestion,
								purpose: item.purpose,
								attachment: item.attachment,
								taskId: item.taskId,
								isRead: item.isRead
							}))
							this.doneRate = res.data.stats.doneRate
							this.readRate = res.data.stats.readRate
						}
						this.lastSuggestion = []
						// 处理建议数据
						if (res.data.suggestions && res.data.suggestions.length > 0) {
							this.sugestionText = res.data.suggestions[0].suggestion
							this.currentSuggestion = res.data.suggestions[0]
							this.historySugestionList = res.data.suggestions.map(item => ({
								createTime: item.createTime || '',
								content: item.suggestion.replace(/\n/g, "<br>"),
								image: item.suggestImage
							}))
							console.log(this.historySugestionList)
							this.lastSuggestion.push(this.historySugestionList[this.historySugestionList
								.length - 1])
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				} catch (e) {
					console.error('获取月度数据失败:', e)
					uni.showToast({
						title: '获取数据失败',
						icon: 'none'
					})
				}
			},
			// 处理任务完成
			async handleTaskDone() {
				if (this.currentMaterial.isRead == true) {
					// 关闭弹窗
					this.showMask = false
					// 重新获取任务列表
					this.fetchMonthData(this.currentDate)
				}
				try {
					const res = await uni.http.post('/stu/studentTask/subTaskRead', {
						taskId: this.currentMaterial.taskId,
						taskMaterialId: this.currentMaterial.id
					})

					if (res.errCode === 0) {

						// 关闭弹窗
						this.showMask = false
						// 重新获取任务列表
						this.fetchMonthData(this.currentDate)
					}
				} catch (e) {
					console.error('提交任务完成失败:', e)
					uni.showToast({
						title: '提交失败',
						icon: 'none'
					})
				}
			},
		},
		filters: {
			padNum(val) {
				if (parseInt(val) < 10) {
					return '0' + val
				}
				return val;
			}
		},
		computed: {
			// textList() {
			// 	let list = []
			// 	while (this.sugestionText.length > 20) {
			// 		const text = this.sugestionText.slice(0, 20)
			// 		list.push(text)
			// 		this.sugestionText = text
			// 	}
			// 	if (this.sugestionText.length > 0) {
			// 		list.push(this.sugestionText)
			// 	}
			// 	return list;
			// }
		},
		components: {
			userTitle
		},
		created() {
			console.log('un', uni.getStorageSync('TOKEN'))
			this.isCurrentMonth = this.checkIsCurrentMonth(this.currentDate)
			// 只在登录状态下获取数据
			if (uni.getStorageSync('TOKEN')) {
				this.fetchMonthData(this.currentDate)
			}
		},
		onShow() {
			// if (!uni.getStorageSync('TOKEN')) {
			// 	// 保存当前页面路径
			// 	// this.$store.commit('user/setRedirectPath', '/pages/university_companion/university_companion')
			// 	uni.navigateTo({
			// 		url: '/subpkg/login/login'
			// 	})
			// 	return
			// }
			// 已登录则获取数据
			this.fetchMonthData(this.currentDate)
		}
	};
</script>
<style lang="scss" scoped>
	.container {
		background-color: #fff;
		background-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/university_companion_bg.png');
		background-size: contain;
		background-repeat: no-repeat;
		padding-top: 320rpx;
		min-height: 100vh;
		padding-bottom: 90rpx;
	}

	.title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.subtitle {
		font-size: 24rpx;
		color: #666;
	}

	.task-section {
		background-color: #fff;
		padding: 20rpx;
		border-radius: 40rpx;
		margin-bottom: 20rpx;
		padding: 46rpx 30rpx;
	}

	.task-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.tip {
		.tip-text {
			position: relative;
			display: inline-block;
			font-weight: 800;
			font-size: 30rpx;
			color: #060606;
			z-index: 100;
		}

		.tip-text::after {
			content: "";
			position: absolute;
			bottom: -6rpx;
			left: 0;
			width: 100%;
			height: 20rpx;
			/* 指定高度 */
			background-color: #DBFF9C;
			/* 底部背景颜色 */
			z-index: -1;
		}
	}

	.task-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;

		text {
			flex: 1.5;
		}
	}

	.title {
		display: flex;
		align-items: center;
		justify-content: flex-start;

		.tag {
			height: 48rpx;
			line-height: 48rpx;
			font-weight: 500;
			font-size: 26rpx;
			color: #060606;
			margin-right: 20rpx;
		}

		.progress {
			width: 470rpx;
			height: 20rpx;
			background: #E6E6E6;
			border-radius: 20rpx;
			margin-right: 20rpx;
			flex: 1;

			.overlay {
				width: 100%;
				height: 100%;
				border-radius: 20rpx;
				background-repeat: repeat-x;

			}
		}

		.title-info {
			font-size: 26rpx;
			color: #414141;
		}
	}

	.completion-text {
		font-size: 24rpx;
		color: #666;
	}

	.task-list {
		margin-top: 20rpx;
	}

	.task-item {
		position: relative;
		margin-bottom: 46rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.task-index {
			font-weight: 800;
			font-size: 30rpx;
			color: #060606;
			margin-right: 24rpx;
		}

		.tagBox {
			position: absolute;
			top: -14rpx;
			left: 60rpx;
			display: flex;
			align-items: center;

			.tag {
				width: 94rpx;
				height: 34rpx;
				background-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/yellow_flg.png');
				background-size: contain;
				background-repeat: no-repeat;
				font-weight: 400;
				font-size: 20rpx;
				color: #FFFFFF;
				text-align: center;
				padding-right: 12rpx;
				line-height: 34rpx;
			}

			.tags {
				background-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/red_flag.png');
				background-size: contain;
				background-repeat: no-repeat;
			}
		}



		.task-title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			background: #FFFFFF;
			box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(158, 158, 158, 0.16);
			border-radius: 12rpx;
			border: 1rpx solid #01997A;
			width: 630rpx;
			height: 100rpx;
			padding: 0 26rpx;
			flex: 1;

			.detail {
				width: 110rpx;
				height: 50rpx;
				background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
				border-radius: 16rpx;
				font-weight: bold;
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 50rpx;
				text-align: center;
				margin: 0;

				&:last-child {
					margin-left: 20rpx;
				}
			}
		}
	}

	.btnBox {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		flex: 1;
	}

	.suggestion-section {
		padding: 0 30rpx;

		.suggestion-content {
			padding: 30rpx;
			background: #F5FFFD;
			box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(136, 133, 133, 0.16);
			border-radius: 20rpx;
			min-height: 400rpx;

			.line {
				height: 70rpx;
				font-weight: bold;
				font-size: 28rpx;
				color: #4A4A4C;
				line-height: 70rpx;
				text-align: center;
				border-bottom: 1rpx dashed #01997A;
				padding-bottom: 10rpx;
			}

			.imgs {
				margin-top: 34rpx;
				display: flex;
				flex-wrap: wrap;
				align-items: center;

				.img {
					margin-top: 10rpx;
					width: 280rpx;
					height: 280rpx;
					border-radius: 15rpx;
				}
			}
		}
	}

	.mask {
		position: fixed;
		top: 0;
		height: 100vh;
		width: 100vw;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999;
		padding: 0;

		.content {
			position: relative;
			width: 632rpx;
			height: 936rpx;
			background: linear-gradient(181deg, #CBF2E0 0%, #FFFFFF 35%);
			border-radius: 32rpx;
			display: flex;
			flex-direction: column;

			.content-scroll {
				flex: 1;
				overflow-y: auto;
				padding: 36rpx 40rpx;
				padding-bottom: 120rpx; // 为底部按钮留出空间
				height: calc(100% - 120rpx); // 减去底部按钮的高度
			}

			.title {
				font-weight: 800;
				font-size: 30rpx;
				color: #504E4E;
				text-align: center;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 10rpx;
			}

			.task-content,
			.task-content-only-one {
				margin-bottom: 28rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: #4A4A4C;
				line-height: 46rpx;

				.tip {
					margin-bottom: 30rpx;
				}

				.task-content-list-text {
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: space-between;
				}
			}

			.task-content-only-one {
				image {
					margin-top: 40rpx;
					width: 400rpx;
					height: 400rpx;
				}
			}

			.task-content-image {
				display: flex;
				align-items: center;
				padding: 20rpx 0;

				image {
					width: 390rpx;
					border-radius: 16rpx;
				}
			}

			.down-load {
				margin-top: 24rpx;
				width: 539rpx;
				height: 85rpx;
				background: #FFFFFF;
				border-radius: 12rpx;
				border: 1rpx solid #2FC293;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 18rpx;

				.down-load-icon {
					display: flex;
					align-items: center;
					justify-content: flex-start;

					.icon {
						height: 24rpx;
						width: 24rpx;
					}

					.text {
						margin-left: 4rpx;
						font-weight: 400;
						font-size: 28rpx;
						color: #4A4A4C;
						max-width: 300rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}

				.down-load-btn {
					margin: 0;
					width: 120rpx;
					height: 44rpx;
					background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
					border-radius: 16rpx;
					font-weight: bold;
					font-size: 24rpx;
					color: #FFFFFF;
					text-align: center;
					line-height: 44rpx;
				}
			}

			.history-list {
				height: 100%;
				overflow-y: scroll;

				.history-item {
					margin-bottom: 40rpx;

					.history-date {
						display: flex;
						align-items: center;
						justify-content: flex-start;

						.blue-block {
							width: 8rpx;
							height: 30rpx;
							background: #2FC293;
							border-radius: 12rpx 12rpx;
							margin-right: 20rpx;
						}

						text {
							font-weight: 800;
							font-size: 30rpx;
							color: #060606;
						}
					}

					.history-content {
						margin-top: 26rpx;
						font-weight: bold;
						font-size: 28rpx;
						color: #4A4A4C;
						line-height: 50rpx;
						margin-bottom: 16rpx;
					}

					image {
						height: 230rpx;
						width: 230rpx;
						border-radius: 20rpx;
					}
				}
			}

			.bottom-btn-container {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				width: 100%;
				height: 120rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #fff;
				border-radius: 0 0 32rpx 32rpx;

				.bottom-btn {
					width: 470rpx;
					height: 80rpx;
					background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
					border-radius: 40rpx;
					font-weight: bold;
					font-size: 30rpx;
					color: #FFFFFF;
					text-align: center;
					line-height: 80rpx;
				}

				.bottom-btn-disabled {
					background: #CCCCCC;
					cursor: not-allowed;
				}
			}
		}
	}
</style>