@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-8e603e4a {
  background-color: #fff;
  background-image: url("https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/university_companion_bg.png");
  background-size: contain;
  background-repeat: no-repeat;
  padding-top: 320rpx;
  min-height: 100vh;
  padding-bottom: 90rpx;
}
.title.data-v-8e603e4a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.subtitle.data-v-8e603e4a {
  font-size: 24rpx;
  color: #666;
}
.task-section.data-v-8e603e4a {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 40rpx;
  margin-bottom: 20rpx;
  padding: 46rpx 30rpx;
}
.task-header.data-v-8e603e4a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.tip .tip-text.data-v-8e603e4a {
  position: relative;
  display: inline-block;
  font-weight: 800;
  font-size: 30rpx;
  color: #060606;
  z-index: 100;
}
.tip .tip-text.data-v-8e603e4a::after {
  content: "";
  position: absolute;
  bottom: -6rpx;
  left: 0;
  width: 100%;
  height: 20rpx;
  /* 指定高度 */
  background-color: #DBFF9C;
  /* 底部背景颜色 */
  z-index: -1;
}
.task-title.data-v-8e603e4a {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.task-title text.data-v-8e603e4a {
  flex: 1.5;
}
.title.data-v-8e603e4a {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.title .tag.data-v-8e603e4a {
  height: 48rpx;
  line-height: 48rpx;
  font-weight: 500;
  font-size: 26rpx;
  color: #060606;
  margin-right: 20rpx;
}
.title .progress.data-v-8e603e4a {
  width: 470rpx;
  height: 20rpx;
  background: #E6E6E6;
  border-radius: 20rpx;
  margin-right: 20rpx;
  flex: 1;
}
.title .progress .overlay.data-v-8e603e4a {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  background-repeat: repeat-x;
}
.title .title-info.data-v-8e603e4a {
  font-size: 26rpx;
  color: #414141;
}
.completion-text.data-v-8e603e4a {
  font-size: 24rpx;
  color: #666;
}
.task-list.data-v-8e603e4a {
  margin-top: 20rpx;
}
.task-item.data-v-8e603e4a {
  position: relative;
  margin-bottom: 46rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.task-item .task-index.data-v-8e603e4a {
  font-weight: 800;
  font-size: 30rpx;
  color: #060606;
  margin-right: 24rpx;
}
.task-item .tagBox.data-v-8e603e4a {
  position: absolute;
  top: -14rpx;
  left: 60rpx;
  display: flex;
  align-items: center;
}
.task-item .tagBox .tag.data-v-8e603e4a {
  width: 94rpx;
  height: 34rpx;
  background-image: url("https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/yellow_flg.png");
  background-size: contain;
  background-repeat: no-repeat;
  font-weight: 400;
  font-size: 20rpx;
  color: #FFFFFF;
  text-align: center;
  padding-right: 12rpx;
  line-height: 34rpx;
}
.task-item .tagBox .tags.data-v-8e603e4a {
  background-image: url("https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/red_flag.png");
  background-size: contain;
  background-repeat: no-repeat;
}
.task-item .task-title.data-v-8e603e4a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFFFFF;
  box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(158, 158, 158, 0.16);
  border-radius: 12rpx;
  border: 1rpx solid #01997A;
  width: 630rpx;
  height: 100rpx;
  padding: 0 26rpx;
  flex: 1;
}
.task-item .task-title .detail.data-v-8e603e4a {
  width: 110rpx;
  height: 50rpx;
  background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
  border-radius: 16rpx;
  font-weight: bold;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 50rpx;
  text-align: center;
  margin: 0;
}
.task-item .task-title .detail.data-v-8e603e4a:last-child {
  margin-left: 20rpx;
}
.btnBox.data-v-8e603e4a {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}
.suggestion-section.data-v-8e603e4a {
  padding: 0 30rpx;
}
.suggestion-section .suggestion-content.data-v-8e603e4a {
  padding: 30rpx;
  background: #F5FFFD;
  box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(136, 133, 133, 0.16);
  border-radius: 20rpx;
  min-height: 400rpx;
}
.suggestion-section .suggestion-content .line.data-v-8e603e4a {
  height: 70rpx;
  font-weight: bold;
  font-size: 28rpx;
  color: #4A4A4C;
  line-height: 70rpx;
  text-align: center;
  border-bottom: 1rpx dashed #01997A;
  padding-bottom: 10rpx;
}
.suggestion-section .suggestion-content .imgs.data-v-8e603e4a {
  margin-top: 34rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.suggestion-section .suggestion-content .imgs .img.data-v-8e603e4a {
  margin-top: 10rpx;
  width: 280rpx;
  height: 280rpx;
  border-radius: 15rpx;
}
.mask.data-v-8e603e4a {
  position: fixed;
  top: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  padding: 0;
}
.mask .content.data-v-8e603e4a {
  position: relative;
  width: 632rpx;
  height: 936rpx;
  background: linear-gradient(181deg, #CBF2E0 0%, #FFFFFF 35%);
  border-radius: 32rpx;
  display: flex;
  flex-direction: column;
}
.mask .content .content-scroll.data-v-8e603e4a {
  flex: 1;
  overflow-y: auto;
  padding: 36rpx 40rpx;
  padding-bottom: 120rpx;
  height: calc(100% - 120rpx);
}
.mask .content .title.data-v-8e603e4a {
  font-weight: 800;
  font-size: 30rpx;
  color: #504E4E;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}
.mask .content .task-content.data-v-8e603e4a,
.mask .content .task-content-only-one.data-v-8e603e4a {
  margin-bottom: 28rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #4A4A4C;
  line-height: 46rpx;
}
.mask .content .task-content .tip.data-v-8e603e4a,
.mask .content .task-content-only-one .tip.data-v-8e603e4a {
  margin-bottom: 30rpx;
}
.mask .content .task-content .task-content-list-text.data-v-8e603e4a,
.mask .content .task-content-only-one .task-content-list-text.data-v-8e603e4a {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
.mask .content .task-content-only-one image.data-v-8e603e4a {
  margin-top: 40rpx;
  width: 400rpx;
  height: 400rpx;
}
.mask .content .task-content-image.data-v-8e603e4a {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}
.mask .content .task-content-image image.data-v-8e603e4a {
  width: 390rpx;
  border-radius: 16rpx;
}
.mask .content .down-load.data-v-8e603e4a {
  margin-top: 24rpx;
  width: 539rpx;
  height: 85rpx;
  background: #FFFFFF;
  border-radius: 12rpx;
  border: 1rpx solid #2FC293;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 18rpx;
}
.mask .content .down-load .down-load-icon.data-v-8e603e4a {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.mask .content .down-load .down-load-icon .icon.data-v-8e603e4a {
  height: 24rpx;
  width: 24rpx;
}
.mask .content .down-load .down-load-icon .text.data-v-8e603e4a {
  margin-left: 4rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #4A4A4C;
  max-width: 300rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mask .content .down-load .down-load-btn.data-v-8e603e4a {
  margin: 0;
  width: 120rpx;
  height: 44rpx;
  background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
  border-radius: 16rpx;
  font-weight: bold;
  font-size: 24rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 44rpx;
}
.mask .content .history-list.data-v-8e603e4a {
  height: 100%;
  overflow-y: scroll;
}
.mask .content .history-list .history-item.data-v-8e603e4a {
  margin-bottom: 40rpx;
}
.mask .content .history-list .history-item .history-date.data-v-8e603e4a {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.mask .content .history-list .history-item .history-date .blue-block.data-v-8e603e4a {
  width: 8rpx;
  height: 30rpx;
  background: #2FC293;
  border-radius: 12rpx 12rpx;
  margin-right: 20rpx;
}
.mask .content .history-list .history-item .history-date text.data-v-8e603e4a {
  font-weight: 800;
  font-size: 30rpx;
  color: #060606;
}
.mask .content .history-list .history-item .history-content.data-v-8e603e4a {
  margin-top: 26rpx;
  font-weight: bold;
  font-size: 28rpx;
  color: #4A4A4C;
  line-height: 50rpx;
  margin-bottom: 16rpx;
}
.mask .content .history-list .history-item image.data-v-8e603e4a {
  height: 230rpx;
  width: 230rpx;
  border-radius: 20rpx;
}
.mask .content .bottom-btn-container.data-v-8e603e4a {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 0 0 32rpx 32rpx;
}
.mask .content .bottom-btn-container .bottom-btn.data-v-8e603e4a {
  width: 470rpx;
  height: 80rpx;
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  border-radius: 40rpx;
  font-weight: bold;
  font-size: 30rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 80rpx;
}
.mask .content .bottom-btn-container .bottom-btn-disabled.data-v-8e603e4a {
  background: #CCCCCC;
  cursor: not-allowed;
}

