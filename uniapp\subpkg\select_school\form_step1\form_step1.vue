<template>
	<view class="content">
		<height :hg='System_height'></height>
		<view class="content_header">
			<view class="nav-title" :style="{'top':System_height+'rpx'}">
				<text>AI考研择校报告</text>
			</view>
			<uni-icons type="left" size="24" color="#2D2D2D" class="back-left" :style="{'top':System_height+'rpx'}"
				@tap="back"></uni-icons>
		</view>
		
		<!-- 通知栏 -->
		<view class="notification">
			<image src="/static/select_school/notification_icon-56586a.png" class="notification-icon"></image>
			<text class="notification-text">请认真完善信息，以便于精准生成报告！</text>
		</view>
		
		<!-- 步骤指示器 -->
		<view class="step-indicator">
			<view class="step-item active">
				<view class="step-number">1</view>
			</view>
			<view class="step-item">
				<view class="step-number">2</view>
			</view>
			<view class="step-item">
				<view class="step-number">3</view>
			</view>
			<view class="step-item">
				<view class="step-number">4</view>
			</view>
			<view class="step-item">
				<view class="step-number">5</view>
			</view>
		</view>
		
		<!-- 表单标题 -->
		<view class="form-title">
			<view class="title-bg"></view>
			<text class="title-text">个人基础信息</text>
		</view>
		
		<!-- 表单内容 -->
		<view class="form-content">
			<view class="form-item">
				<view class="form-label">学生姓名</view>
				<input class="form-input" v-model="formData.studentName" placeholder="请输入姓名" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">性别</view>
				<input class="form-input" v-model="formData.gender" placeholder="请选择性别" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">本科院校</view>
				<input class="form-input" v-model="formData.undergraduateSchool" placeholder="请输入本科院校" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">本科专业</view>
				<input class="form-input" v-model="formData.undergraduateMajor" placeholder="请输入本科专业" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">目标专业</view>
				<input class="form-input" v-model="formData.targetMajor" placeholder="请输入目标专业" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">专业代码</view>
				<input class="form-input" v-model="formData.majorCode" placeholder="请输入专业代码" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">联系方式</view>
				<input class="form-input" v-model="formData.contactInfo" placeholder="请输入联系方式" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">本科年份</view>
				<input class="form-input" v-model="formData.graduationYear" placeholder="请输入本科年份" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
			
			<view class="form-item">
				<view class="form-label">跨专业</view>
				<input class="form-input" v-model="formData.crossMajor" placeholder="是否跨专业" />
				<uni-icons type="right" size="16" color="#C3C3C3" class="form-arrow"></uni-icons>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-buttons">
			<view class="next-btn" @click="nextStep">
				<text>下一步</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				formData: {
					studentName: '吴三胖',
					gender: '女',
					undergraduateSchool: '中国科学技术大学',
					undergraduateMajor: '物联网',
					targetMajor: '大数据技术',
					majorCode: '10358',
					contactInfo: '18756599654',
					graduationYear: '2027',
					crossMajor: '是'
				}
			}
		},
		computed: {
			...mapState(['System_height'])
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			nextStep() {
				// 保存当前步骤数据
				uni.setStorageSync('selectSchoolStep1', this.formData)
				
				// 跳转到下一步
				uni.navigateTo({
					url: '/subpkg/select_school/form_step2/form_step2'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		min-height: 100vh;
		background: linear-gradient(180deg, #CBF2E0 0%, #F6F7FB 43.3%);
	}
	
	.content_header {
		position: relative;
		height: 140rpx;
		background: #00C2A0;
		
		.nav-title {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			z-index: 10;
			
			text {
				font-size: 34rpx;
				color: #2D2D2D;
				font-weight: 400;
			}
		}
		
		.back-left {
			position: absolute;
			left: 30rpx;
			z-index: 10;
		}
	}
	
	.notification {
		display: flex;
		align-items: center;
		margin: 30rpx;
		padding: 14rpx 28rpx;
		background: #F5FFFD;
		border-radius: 31rpx;
		
		.notification-icon {
			width: 49rpx;
			height: 49rpx;
			margin-right: 28rpx;
		}
		
		.notification-text {
			font-size: 28rpx;
			color: #5A5A5A;
			flex: 1;
		}
	}
	
	.step-indicator {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 30rpx 0;
		
		.step-item {
			margin: 0 81rpx;
			
			.step-number {
				width: 43rpx;
				height: 43rpx;
				border-radius: 50%;
				background: #FF9B3A;
				color: #FFFFFF;
				font-size: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			&.active .step-number {
				background: #FF9B3A;
			}
		}
	}
	
	.form-title {
		position: relative;
		margin: 30rpx;
		
		.title-bg {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 182rpx;
			height: 19rpx;
			background: #DBFF9C;
		}
		
		.title-text {
			font-size: 30rpx;
			color: #060606;
			font-weight: 400;
		}
	}
	
	.form-content {
		padding: 0 30rpx;
		
		.form-item {
			display: flex;
			align-items: center;
			background: #FFFFFF;
			border-radius: 16rpx;
			padding: 31rpx 28rpx;
			margin-bottom: 30rpx;
			position: relative;
			
			.form-label {
				font-size: 30rpx;
				color: #504E4E;
				width: 180rpx;
				flex-shrink: 0;
			}
			
			.form-input {
				flex: 1;
				font-size: 28rpx;
				color: #989898;
				text-align: right;
				margin-right: 20rpx;
			}
			
			.form-arrow {
				flex-shrink: 0;
			}
		}
	}
	
	.bottom-buttons {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #F6F7FB;
		padding: 30rpx 100rpx 60rpx;
		
		.next-btn {
			width: 100%;
			height: 80rpx;
			background: linear-gradient(135deg, #26C8AC 0%, #19C990 100%);
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			text {
				font-size: 30rpx;
				color: #FFFFFF;
				font-weight: 400;
			}
		}
	}
</style>
