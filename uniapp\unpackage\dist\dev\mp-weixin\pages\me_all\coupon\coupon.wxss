@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container .tabs.data-v-4b5dc253 {
  height: 94rpx;
  border-bottom: 1rpx solid #D6D6D6;
}
.container.data-v-4b5dc253  .u-tabs__wrapper__nav__line {
  width: 100rpx !important;
  height: 6rpx !important;
}
.container view.data-v-4b5dc253 {
  box-sizing: border-box;
}
.container .content-container.data-v-4b5dc253 {
  padding: 30rpx;
}
.container .content-container .exchange.data-v-4b5dc253 {
  height: 84rpx;
  border-radius: 40rpx;
  background-color: #F6F7FB;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 20rpx;
}
.container .content-container .exchange .input-code.data-v-4b5dc253 {
  flex: 4;
}
.container .content-container .exchange text.data-v-4b5dc253 {
  flex: 1.5;
  height: 100%;
  border-radius: 0 40rpx 40rpx 0;
  background-color: #00C2A0;
  color: #FFF;
  line-height: 84rpx;
  text-align: center;
}
.container .content-container .wait-for-use.data-v-4b5dc253 {
  height: 120rpx;
  border-radius: 40rpx;
  border: 1rpx solid #AFAFAF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 28rpx;
}
.container .content-container .wait-for-use .tip.data-v-4b5dc253 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .content-container .wait-for-use .tip text.data-v-4b5dc253 {
  color: #5A5A5A;
  font-size: 28rpx;
  margin-left: 16rpx;
}
.container .content-container .wait-for-use .more.data-v-4b5dc253 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.container .content-container .wait-for-use .more text.data-v-4b5dc253 {
  color: #EE7878;
  font-size: 24rpx;
  margin-right: 10rpx;
}
.container .content-container .coupon-container.data-v-4b5dc253 {
  margin-top: 36rpx;
}
.container .content-container .coupon-container .coupon.data-v-4b5dc253 {
  margin-bottom: 28rpx;
  height: 200rpx;
  background-size: contain;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.container .content-container .coupon-container .coupon .left.data-v-4b5dc253 {
  width: 192rpx;
  height: 200rpx;
  background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-left.png);
  background-repeat: no-repeat;
  background-size: 192rpx 200rpx;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.container .content-container .coupon-container .coupon .left text.data-v-4b5dc253:first-child {
  font-weight: bold;
  font-size: 40rpx;
}
.container .content-container .coupon-container .coupon .left text.data-v-4b5dc253:last-child {
  color: #CDF3E7;
  font-size: 26rpx;
}
.container .content-container .coupon-container .coupon .right.data-v-4b5dc253 {
  background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-right.png);
  background-repeat: no-repeat;
  background-size: 500rpx 200rpx;
  width: 500rpx;
  height: 100%;
  padding: 15rpx 25rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.container .content-container .coupon-container .coupon .right .name.data-v-4b5dc253 {
  color: #4C5370;
  font-size: 30rpx;
  font-weight: bold;
}
.container .content-container .coupon-container .coupon .right .date.data-v-4b5dc253 {
  color: #AFAFAF;
  font-size: 24rpx;
}
.container .content-container .coupon-container .coupon .right .date text.data-v-4b5dc253 {
  font-size: 20rpx;
}
.container .content-container .coupon-container .coupon .right .detail-info .detail-left .bottom-rule.data-v-4b5dc253 {
  display: flex;
  align-items: center;
  position: relative;
  justify-content: space-between;
}
.container .content-container .coupon-container .coupon .right .detail-info .detail-left .bottom-rule .rule.data-v-4b5dc253 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.container .content-container .coupon-container .coupon .right .detail-info .detail-left .bottom-rule .rule text.data-v-4b5dc253 {
  font-size: 24rpx;
}
.container .content-container .coupon-container .coupon .right .detail-info .detail-left .bottom-rule .use-btn.data-v-4b5dc253 {
  width: 132rpx;
  height: 54rpx;
  border-radius: 32rpx;
  border: 1rpx solid #01997A;
  color: #01997A;
  font-weight: 500;
  line-height: 54rpx;
  text-align: center;
  font-size: 26rpx;
}
.container .content-container .coupon-container .coupon .right .detail-info .detail-left .bottom-rule .use-btns.data-v-4b5dc253 {
  width: 132rpx;
  height: 54rpx;
  border-radius: 32rpx;
  border: 1rpx solid #bcbbbb;
  background-color: #bcbbbb;
  color: #fff;
  font-weight: 500;
  line-height: 54rpx;
  text-align: center;
  font-size: 26rpx;
}
.container .content-container .coupon-container .coupon .rules.data-v-4b5dc253 {
  width: 299rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(0, 0, 0, 0.13);
  border-radius: 8rpx;
  position: absolute;
  top: 50rpx;
  left: -20rpx;
  padding: 20rpx 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #A0A0A0;
  z-index: 99999;
}

