{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/index/index.vue?3995", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/index/index.vue?9d74", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/index/index.vue?575b", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/index/index.vue?88ef", "uni-app:///pages/index/index.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/index/index.vue?f686", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/index/index.vue?942e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "enter", "title", "list1", "list2", "gridList", "menuItems", "activeId", "activeIndex", "list", "pageInfo", "total", "page", "loadMoreStatus", "contentText", "contentdown", "contentrefresh", "contentnomore", "userall", "public", "display", "uni", "onLoad", "onShow", "clearInterval", "methods", "<PERSON><PERSON><PERSON><PERSON>", "appId", "url", "getDetails", "errCode", "msg", "item", "image", "console", "routergo", "closepage", "getCategory", "getList", "categoryId", "campusld", "limit", "setActiveIndex", "getMore"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,kUAEN;AACP,KAAK;AACL;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACyMnnB;AAKA;AAIA;AAAA;AAAA;AAAA,eACA;EACAC;IAAA;IACA;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IAAA,gDACA,sDACA,qDACA,kDACAC;EAEA;EACAC;;IAIA;EAAA,CACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;MACA;QACA;MACA;IACA;MACA;MACA;QACA;UACAC;UACA;YACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MAEA;QACA/B;UACAgC;QACA;MACA;QACA;UACAN;YACAO;UACA;QACA;UACA;UACA;QACA;MACA;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAEA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAA;gBAHA7B;gBACA8B;gBACAC;gBAEA;kBACA;oBACA,uCACAC;sBACAC;oBAAA;kBAEA;kBACAC;kBACA;oBACA,uCACAF;sBACAC;oBAAA;kBAEA;kBACA;oBACA,uCACAD;sBACAC;oBAAA;kBAEA;gBACA,QAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAE;MACA;QACAd;UACAO;QACA;QACA;MACA;QACA;QACA;QACA;UACA;YACAP;cACAO;YACA;UACA;QAEA;UACA;UACAM;QACA;MACA;IAGA;IACAE;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAA;gBAHArC;gBACA8B;gBACAC;gBAEA;kBACA;kBACAG;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA;kBACAC;kBACAC;kBACA5B;kBACA6B;gBACA;cAAA;gBAAA;gBARAzC;gBACA8B;gBACAC;gBAOA;kBACA;kBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAW;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnbA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper/u-swiper\" */ \"uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-load-more/uni-load-more\" */ \"@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    login: function () {\n      return import(\n        /* webpackChunkName: \"components/login/login\" */ \"@/components/login/login.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var g1 = _vm.list.length\n  var g2 = _vm.list.length\n  var g3 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"header\">\r\n\t\t\t<!-- add高度组件 -->\r\n\t\t\t<view class=\"img_box\">\r\n\t\t\t\t<view class=\"img_box\">\r\n\t\t\t\t\t<u-swiper :list=\"banner\" :indicator='true' keyName=\"image\" indicatorMode='dot' height='455rpx'\r\n\t\t\t\t\t\t@click=\"hadeswp\"></u-swiper>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"user\">\r\n\t\t\t\t\t<!-- @tap=\"routergo('/pages/me_all/personage/personage')\" -->\r\n\t\t\t\t\t<view class=\"user_head_portrait\">\r\n\t\t\t\t\t\t<image v-if=\"userall.avatar\" :src=\"userall.avatar\" mode=\"\"></image>\r\n\t\t\t\t\t\t<image v-else src=\"../../static/wa.png\" mode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- '/pages/me_all/personage/personage' -->\r\n\t\t\t\t\t<view class=\"user_2\" @tap=\"routergo()\">\r\n\t\t\t\t\t\t<view class=\"user_2_1\">\r\n\t\t\t\t\t\t\t{{userall.nickname||'请登录'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"user_2_2\">\r\n\t\t\t\t\t\t\t蝌蚪币:<text class=\"num\">\r\n\t\t\t\t\t\t\t\t<!-- {{userall.score||0}} -->\r\n\t\t\t\t\t\t\t\t{{asset.point || 0}}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t<text class=\"left\">优惠券:<text class=\"num\">{{asset.couponCnt || 0}}</text></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"user_2_3\">\r\n\t\t\t\t\t\t\t<u-line-progress :percentage=\"userall.score/userall.upgrade*100\" activeColor=\"#05B6F6\"\r\n\t\t\t\t\t\t\t\t:showText=\"false\"></u-line-progress>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"user_3\" @tap=\"routergo('/pages/me_all/codeqr/codeqr')\">\r\n\t\t\t\t\t\t<view class=\"user_code\">\r\n\t\t\t\t\t\t\t<image src=\"@/static/Project_drawing 23.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text>会员码</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"grid\">\r\n\t\t\t\t<view class=\"grid-item\" v-for=\"item in  gridList\" :key=\"item.id\" @click=\"toTarget(item)\">\r\n\t\t\t\t\t<image mode=\"widthFix\" :src=\"item.image\"></image>\r\n\t\t\t\t\t<text>{{item.name}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- <view class=\"card\">\r\n\t\t\t\t<view class=\"card_1 flexw\">\r\n\t\t\t\t\t<view class=\"card_1_left\" @tap=\"router(1)\" v-if=\"common.iszq==1\">\r\n\t\t\t\t\t\t<view class=\"card_1_left_title\">\r\n\t\t\t\t\t\t\t<view>自取</view>\r\n\t\t\t\t\t\t\t<view>下单免排队</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card_1_left_img\">\r\n\t\t\t\t\t\t\t<image src=\"@/static/Project_drawing 28.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"card_1_right\" @tap=\"router(2)\" v-if=\"common.iswm==1\">\r\n\t\t\t\t\t\t<view class=\"card_1_left_title\">\r\n\t\t\t\t\t\t\t<view>外卖</view>\r\n\t\t\t\t\t\t\t<view>美食送到家</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card_1_left_img\">\r\n\t\t\t\t\t\t\t<image src=\"@/static/Project_drawing 29.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"card_1_right\" @tap=\"scanCode(3)\" v-if=\"common.ists==1\"\r\n\t\t\t\t\t\tstyle=\"margin-top: 16rpx;background-color: #00acbb;color: #fff;\">\r\n\t\t\t\t\t\t<view class=\"card_1_left_title\">\r\n\t\t\t\t\t\t\t<view>扫码点餐</view>\r\n\t\t\t\t\t\t\t<view>扫码座号二维码即可点餐</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card_1_left_img\">\r\n\t\t\t\t\t\t\t<image src=\"@/static/saoma_bg.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"card_1_left\" v-if=\"common.isyy==1\" style=\"margin-top: 16rpx;background-color: #adeae6;\"\r\n\t\t\t\t\t\t@tap=\"routerTo('/pages/reserved/reserved')\">\r\n\t\t\t\t\t\t<view class=\"card_1_left_title\">\r\n\t\t\t\t\t\t\t<view>提前预约</view>\r\n\t\t\t\t\t\t\t<view>提前预约，到店直接就餐</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card_1_left_img\">\r\n\t\t\t\t\t\t\t<image src=\"@/static/yuyue_bg.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card_2\">\r\n\t\t\t\t\t<button open-type=\"contact\" @contact=\"contact\">\r\n\t\t\t\t\t\t<view class=\"card_2_content\">\r\n\t\t\t\t\t\t\t<view class=\"card_2_content_left\">\r\n\t\t\t\t\t\t\t\t<view>加盟咨询</view>\r\n\t\t\t\t\t\t\t\t<view>{{public.tel}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"card_2_content_right\">\r\n\t\t\t\t\t\t\t\t<image src=\"../../static/Project_drawing 3.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<view class=\"card_2_content\" @tap=\"routerTo('/pages/me_all/integral/integral')\">\r\n\t\t\t\t\t\t<view class=\"card_2_content_left\">\r\n\t\t\t\t\t\t\t<view>积分商城</view>\r\n\t\t\t\t\t\t\t<view>礼券宝物全都有</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card_2_content_right\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/Project_drawing 4.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view> -->\r\n\t\t\t<!-- \t<view class=\"card_2\">\r\n\t\t\t\t\t<view class=\"card_2_content\" @tap=\"routergo('/pages/wallet/wallet')\">\r\n\t\t\t\t\t\t<view class=\"card_2_content_left\">\r\n\t\t\t\t\t\t\t<view>我的钱包</view>\r\n\t\t\t\t\t\t\t<view>礼券宝物全都有</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card_2_content_right\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/Project_drawing 4.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t<!-- <view class=\"card_3\">\r\n\t\t\t\t\t<u-swiper :list=\"list2\" keyName=\"image\" :indicator='true' indicatorMode='dot' :height='314'\r\n\t\t\t\t\t\t@click=\"swp\"></u-swiper>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"beianhao\" v-if=\"common.mini_filings\">\r\n\t\t\t\t\t备案号：{{common.mini_filings || ''}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<login :show=\"enter\" @closepage='closepage'></login>\r\n\t\t</view>\r\n\t\t<height :hg='40'></height>\r\n\t\t<barcode id='1' class=\"barcode\" autostart=\"true\" ref=\"barcode\" background=\"rgb(0,0,0)\" frameColor=\"#1C86EE\"\r\n\t\t\tscanbarColor=\"#1C86EE\" :filters=\"fil\" @marked=\"success1\" @error=\"fail1\"></barcode> -->\r\n\t\t</view>\r\n\t\t<view class=\"scrollBox\">\r\n\t\t\t<scroll-view class=\"menu\" scroll-x>\r\n\t\t\t\t<!-- 修改后的菜单项 -->\r\n\t\t\t\t<view :class=\"['menu-item',activeIndex === index ? 'active' : '']\" v-for=\"(item, index) in menuItems\"\r\n\t\t\t\t\t:key=\"item.id\" @tap=\"setActiveIndex(index,item.id)\">\r\n\t\t\t\t\t{{ item.name }}\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t\t<view class=\"productList\">\r\n\t\t\t<view class=\"product\" v-if=\"list.length>0\">\r\n\t\t\t\t<image class=\"da\" :src=\"activity[0].image\" alt=\"\" />\r\n\t\t\t\t<view class=\"productItem\" v-for=\"(item,index) in list\" :key=\"item.id\"\r\n\t\t\t\t\t@tap=\"routergo('/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id,1)\">\r\n\t\t\t\t\t<template v-if=\"index%2!=0\">\r\n\t\t\t\t\t\t<image class=\"head\" :src=\"item.course_cover\" alt=\"\" />\r\n\t\t\t\t\t\t<view class=\"bottom\">\r\n\t\t\t\t\t\t\t<view class=\"title\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"desc\">{{item.handout_type_name}} {{item.teaching_mode_name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t\t\t<text>￥{{item.checkout_price}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"add\" v-if=\"!userall.user\">添加</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product\" v-if=\"list.length>0\">\r\n\t\t\t\t<view class=\"productItem\" v-for=\"(item,index) in list\" :key=\"item.id\"\r\n\t\t\t\t\t@tap=\"routergo('/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id,1)\">\r\n\t\t\t\t\t<template v-if=\"index%2==0\">\r\n\t\t\t\t\t\t<image class=\"head\" :src=\"item.course_cover\" alt=\"\" />\r\n\t\t\t\t\t\t<view class=\"bottom\">\r\n\t\t\t\t\t\t\t<view class=\"title\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"desc\">{{item.handout_type_name}} {{item.teaching_mode_name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"money\">\r\n\t\t\t\t\t\t\t\t<text>￥{{item.checkout_price}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"add\" v-if=\"!userall.user\">添加</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t\t<view class=\"noenList\" v-if=\"!list.length\">暂无产品内容</view>\r\n\t\t<uni-load-more v-if=\"list.length>0\" @clickLoadMore=\"getMore\" :contentText='contentText'\r\n\t\t\t:status=\"loadMoreStatus\"></uni-load-more>\r\n\t\t<login :show=\"enter\" @closepage='closepage'></login>\r\n\t</view>\r\n</template>\r\n<script>\r\n\t// import {\r\n\t// \tuserInfo,\r\n\t// \tcommon\r\n\t// } from \"@/api/public.js\"\r\n\timport {\r\n\t\tgetMainCategory,\r\n\t\tgetProductList,\r\n\t\tgetIndexContent\r\n\t} from \"@/api/comm.js\"\r\n\timport {\r\n\t\tassign,\r\n\t\tforEach\r\n\t} from \"lodash\"\r\n\timport route from 'uview-ui/libs/util/route'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tenter: false,\r\n\t\t\t\ttitle: 'Hello',\r\n\t\t\t\t// 轮播图\r\n\t\t\t\tlist1: [],\r\n\t\t\t\tlist2: [],\r\n\t\t\t\tgridList: [],\r\n\t\t\t\tmenuItems: [],\r\n\t\t\t\tactiveId: '',\r\n\t\t\t\tactiveIndex: 0,\r\n\t\t\t\tlist: [],\r\n\t\t\t\tpageInfo: {\r\n\t\t\t\t\ttotal: 0,\r\n\t\t\t\t\tpage: 1\r\n\t\t\t\t},\r\n\t\t\t\tloadMoreStatus: 'more',\r\n\t\t\t\tcontentText: {\r\n\t\t\t\t\tcontentdown: \"点击加载更多\",\r\n\t\t\t\t\tcontentrefresh: \"正在加载...\",\r\n\t\t\t\t\tcontentnomore: \"没有更多数据了\"\r\n\t\t\t\t},\r\n\t\t\t\tuserall: uni.getStorageSync('user'),\r\n\t\t\t\tpublic: {},\r\n\t\t\t\tdisplay: false,\r\n\t\t\t\tenter: false,\r\n\t\t\t\tbanner: [], //轮播\r\n\t\t\t\tactivity: {}, //抽奖\r\n\t\t\t\tasset: uni.getStorageSync('ASSET') || {}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\r\n\r\n\r\n\t\t\t// this.getDetails()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.list = []\r\n\t\t\tthis.getDetails()\r\n\t\t\tthis.asset = uni.getStorageSync(\"ASSET\") || {}\r\n\t\t\t// this.userInfoApi()\r\n\t\t\t// this.enter = false\r\n\t\t\t// this.commonApi()\r\n\t\t\t// this.getUser()\r\n\t\t\tthis.userall = uni.getStorageSync('user') || {}\r\n\t\t\t// if () {\r\n\r\n\t\t\t// }\r\n\t\t\tif (uni.getStorageSync('TOKEN')) {\r\n\t\t\t\tthis.getCategory().then(() => {\r\n\t\t\t\t\tthis.getList()\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\t// 轮询检查token\r\n\t\t\t\tconst timer = setInterval(() => {\r\n\t\t\t\t\tif (uni.getStorageSync('TOKEN')) {\r\n\t\t\t\t\t\tclearInterval(timer);\r\n\t\t\t\t\t\tthis.getCategory().then(() => {\r\n\t\t\t\t\t\t\tthis.getList()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 500);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击入口菜单\r\n\t\t\ttoTarget(item) {\r\n\t\t\t\tthis.userall = uni.getStorageSync('user')\r\n\r\n\t\t\t\tif (item.appid) {\r\n\t\t\t\t\twx.navigateToMiniProgram({\r\n\t\t\t\t\t\tappId: item.appid, // 必填\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (item.link) {\r\n\t\t\t\t\tif (uni.getStorageSync('TOKEN') && uni.getStorageSync('user')) {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: item.link\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.enter = true\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t\t// if (item.name == \"入学报告\") {\r\n\t\t\t\t// \tif (this.userall.student.reportStatus == 0) {\r\n\t\t\t\t// \t\tuni.navigateTo({\r\n\t\t\t\t// \t\t\turl: '/subpkg/report_all/active/active'\r\n\t\t\t\t// \t\t})\r\n\t\t\t\t// \t} else if (this.userall.student.reportStatus == 1) {\r\n\t\t\t\t// \t\tuni.navigateTo({\r\n\t\t\t\t// \t\t\turl: '/subpkg/report_all/plan/plan'\r\n\t\t\t\t// \t\t})\r\n\t\t\t\t// \t} else if (this.userall.student.reportStatus == 2) {\r\n\t\t\t\t// \t\tuni.navigateTo({\r\n\t\t\t\t// \t\t\turl: '/subpkg/report_all/report_view/report_view'\r\n\t\t\t\t// \t\t})\r\n\t\t\t\t// \t}\r\n\t\t\t\t// }\r\n\t\t\t\t// if (item.name == '大学陪跑') {\r\n\t\t\t\t// \tuni.navigateTo({\r\n\t\t\t\t// \t\turl: '/subpkg/report_all/university_companion/university_companion'\r\n\t\t\t\t// \t})\r\n\t\t\t\t// }\r\n\t\t\t\t// } else {\r\n\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t// \turl: item.path\r\n\t\t\t\t// })\r\n\t\t\t\t// }\r\n\r\n\t\t\t},\r\n\t\t\t//获取轮播\r\n\t\t\tasync getDetails() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await getIndexContent()\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tthis.banner = data.banner.map(item => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\timage: 'https://' + item.image\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tconsole.log(this.banner)\r\n\t\t\t\t\tthis.gridList = data.menu.map(item => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\timage: 'https://' + item.image\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.activity = data.activity.map(item => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\timage: 'https://' + item.image\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\troutergo(url, num) {\r\n\t\t\t\tif (num == 1) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet token = uni.getStorageSync('TOKEN')\r\n\t\t\t\t\tlet user = uni.getStorageSync('user')\r\n\t\t\t\t\tif (token && user) {\r\n\t\t\t\t\t\tif (url) {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: url\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.enter = true\r\n\t\t\t\t\t\tconsole.log('243343443', this.enter);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t},\r\n\t\t\tclosepage(data) {\r\n\t\t\t\tthis.enter = false\r\n\t\t\t\tthis.userall = uni.getStorageSync('user')\r\n\t\t\t\tthis.asset = data\r\n\t\t\t},\r\n\t\t\t// 获取首页产品列表分类\r\n\t\t\tasync getCategory() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await getMainCategory()\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tthis.menuItems = data\r\n\t\t\t\t\tconsole.log(this.menuItems)\r\n\t\t\t\t\tthis.activeId = this.menuItems[0].id\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getList() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await getProductList({\r\n\t\t\t\t\tcategoryId: this.activeId || '',\r\n\t\t\t\t\tcampusld: '',\r\n\t\t\t\t\tpage: this.pageInfo.page,\r\n\t\t\t\t\tlimit: 10,\r\n\t\t\t\t})\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tthis.list = [...this.list, ...data.data]\r\n\t\t\t\t\tthis.pageInfo.total = data.total\r\n\t\t\t\t\tif (this.list.length >= this.pageInfo.total) {\r\n\t\t\t\t\t\tthis.loadMoreStatus = \"noMore\"\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.loadMoreStatus = \"more\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetActiveIndex(index, id) {\r\n\t\t\t\tthis.pageInfo.page = 1\r\n\t\t\t\tthis.list = []\r\n\t\t\t\tthis.activeIndex = index\r\n\t\t\t\tthis.activeId = id\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\tgetMore() {\r\n\t\t\t\tif (this.list.length >= this.pageInfo.total) {\r\n\t\t\t\t\tthis.loadMoreStatus = \"noMore\"\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.pageInfo.page += 1\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.beianhao {\r\n\t\tmargin: 20px 0;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #b5b5b5;\r\n\t}\r\n\r\n\t.scrollBox {\r\n\t\tpadding: 0 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\r\n\t\t.menu {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t// white-space: nowrap;\r\n\t\t\tscrollbar-width: none;\r\n\t\t\t-ms-overflow-style: none;\r\n\r\n\t\t\t&::-webkit-scrollbar {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.menu-item {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\t// 适当减小外边距\r\n\t\t\t// width: 195rpx;\r\n\t\t\theight: 46rpx;\r\n\t\t\tline-height: 46rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\t// font-weight: bold;\r\n\t\t\t// padding-bottom: 5rpx;\r\n\t\t\tfont-size: 35rpx;\r\n\t\t\tcolor: #636363;\r\n\t\t\tmargin-right: 30rpx;\r\n\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-right: 0;\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\r\n\t\t.active {\r\n\t\t\tcolor: #343434;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tborder-bottom: 6rpx solid #00C2A0;\r\n\t\t}\r\n\t}\r\n\r\n\t.noenList {\r\n\t\twidth: 690rpx;\r\n\t\theight: 300rpx;\r\n\t\tline-height: 300rpx;\r\n\t\ttext-align: center;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 14rpx;\r\n\t\tbackground: #FFF;\r\n\t\tborder-radius: 16rpx;\r\n\t\tmargin: 0rpx auto 100rpx;\r\n\t\tpadding: 0rpx 20rpx;\r\n\r\n\t}\r\n\r\n\t.productList {\r\n\t\tmargin-top: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 30rpx;\r\n\r\n\t\t.product {\r\n\t\t\twidth: 338rpx;\r\n\t\t\t// height: 482rpx;\r\n\t\t\t// background: #FFFFFF;\r\n\r\n\t\t\t.da {\r\n\t\t\t\twidth: 338rpx;\r\n\t\t\t\theight: 394rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.productItem {\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t\t&:first-child {\r\n\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.head {\r\n\t\t\t\t\twidth: 338rpx;\r\n\t\t\t\t\theight: 252rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bottom {\r\n\t\t\t\t\tpadding: 0 20rpx;\r\n\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tmargin-top: 24rpx;\r\n\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\tcolor: #414141;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.desc {\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #777777;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.money {\r\n\t\t\t\t\t\tmargin-top: 60rpx;\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tcolor: #FB4E44;\r\n\t\t\t\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t\t.add {\r\n\t\t\t\t\t\t\twidth: 84rpx;\r\n\t\t\t\t\t\t\theight: 45rpx;\r\n\t\t\t\t\t\t\tline-height: 45rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\t\tbackground: #00C2A0;\r\n\t\t\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.grid {\r\n\t\twidth: 690rpx;\r\n\t\tmargin: 0 auto;\r\n\t\t// padding: 36rpx 52rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-start;\r\n\t\talign-content: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin-bottom: 38rpx;\r\n\t\tcolor: #323232;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 18rpx;\r\n\t\tpadding-bottom: 30rpx;\r\n\r\n\t\t.grid-item {\r\n\r\n\t\t\twidth: 25%;\r\n\t\t\tmargin-top: 16rpx;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t}\r\n\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t}\r\n\r\n\t.img_box {\r\n\t\twidth: 750rpx;\r\n\t\theight: 570rpx;\r\n\r\n\t\t::v-deep .u-swiper__indicator {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 80rpx !important;\r\n\t\t}\r\n\r\n\t\t.img_box {\r\n\t\t\tposition: absolute;\r\n\t\t}\r\n\r\n\t\t.user {\r\n\t\t\twidth: 704rpx;\r\n\t\t\theight: 136rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 18rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tposition: relative;\r\n\t\t\ttop: 400rpx;\r\n\r\n\t\t\t.user_head_portrait {\r\n\t\t\t\twidth: 89rpx;\r\n\t\t\t\theight: 89rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tmargin-left: 32rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.user_2 {\r\n\t\t\t\twidth: 54%;\r\n\t\t\t\tmargin-left: 30rpx;\r\n\r\n\t\t\t\t.user_2_1 {\r\n\t\t\t\t\twidth: 200px;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #201E2E;\r\n\t\t\t\t\tmargin-bottom: 5rpx;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.user_2_2 {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t// font-weight: 600;\r\n\t\t\t\t\tcolor: #777777;\r\n\t\t\t\t\tmargin-bottom: 5rpx;\r\n\r\n\t\t\t\t\t.num {\r\n\t\t\t\t\t\tcolor: #000;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\t\tmargin-right: 5rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.left {\r\n\t\t\t\t\t\tmargin-left: 10rpx;\r\n\r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tcolor: #000;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.user_2_3 {\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.user_3 {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 30rpx;\r\n\r\n\t\t\t\t.user_code {\r\n\t\t\t\t\twidth: 54rpx;\r\n\t\t\t\t\theight: 54rpx;\r\n\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #2F2F2F;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n\r\n\t.card {\r\n\t\twidth: 704rpx;\r\n\t\tmargin: 0 auto;\r\n\r\n\t\t.card_1 {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t.card_1_left {\r\n\t\t\t\twidth: 345rpx;\r\n\t\t\t\theight: 348rpx;\r\n\t\t\t\tbackground: #FEFAEE;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.card_1_left_title {\r\n\t\t\t\t\tmargin: 50rpx 0 0 20rpx;\r\n\r\n\t\t\t\t\tview:nth-child(1) {\r\n\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\t// color: #000000;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tview:nth-child(2) {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t// color: #797473;\r\n\t\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.card_1_left_img {\r\n\t\t\t\t\twidth: 85%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.card_1_right {\r\n\t\t\t\twidth: 345rpx;\r\n\t\t\t\theight: 348rpx;\r\n\t\t\t\tbackground: #FFF9F9;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.card_1_left_title {\r\n\t\t\t\t\tmargin: 50rpx 0 0 20rpx;\r\n\r\n\t\t\t\t\tview:nth-child(1) {\r\n\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\t// color: #000000;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tview:nth-child(2) {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t// color: #797473;\r\n\t\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.card_1_left_img {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t.card_2 {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tmargin: 20rpx 0;\r\n\r\n\t\t\t.card_2_content {\r\n\t\t\t\twidth: 342rpx;\r\n\t\t\t\theight: 161rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 36rpx 13rpx 13rpx 13rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.card_2_content_left {\r\n\t\t\t\t\tmargin-left: 29rpx;\r\n\t\t\t\t\ttext-align: left;\r\n\r\n\t\t\t\t\tview:nth-child(1) {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tcolor: #000000;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tview:nth-child(2) {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #AAAAAA;\r\n\t\t\t\t\t\tmargin-top: 5rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.card_2_content_right {\r\n\t\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\t\twidth: 85rpx;\r\n\t\t\t\t\theight: 66rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.card_3 {\r\n\t\t\twidth: 100%;\r\n\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557558984\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}