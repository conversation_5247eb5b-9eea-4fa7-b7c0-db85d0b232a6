<view class="container data-v-f00be0f2"><view class="content-container data-v-f00be0f2"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="address data-v-f00be0f2"><view data-event-opts="{{[['tap',[['select',['$0'],[[['list','',index]]]]]]]}}" class="info data-v-f00be0f2" bindtap="__e"><view class="user-address data-v-f00be0f2"><text class="data-v-f00be0f2">{{item.name+" +86 "+item.phone}}</text><block wx:if="{{item.isDefault}}"><text class="tag data-v-f00be0f2">默认</text><view class="bg-arrow-down data-v-f00be0f2"><u-icon vue-id="{{'7748625f-1-'+index}}" name="checkbox-mark" color="#fff" size="28" class="data-v-f00be0f2" bind:__l="__l"></u-icon></view></block></view><text class="detail data-v-f00be0f2">{{item.addr}}</text></view><view class="opt data-v-f00be0f2"><text data-event-opts="{{[['tap',[['editAddress',['$0'],[[['list','',index]]]]]]]}}" bindtap="__e" class="data-v-f00be0f2">编辑</text><text data-event-opts="{{[['tap',[['deleteAddress',['$0'],[[['list','',index,'id']]]]]]]}}" bindtap="__e" class="data-v-f00be0f2">删除</text></view></view></block><block wx:if="{{$root.g0<1}}"><view style="margin-top:150rpx;" class="data-v-f00be0f2"><u-empty vue-id="7748625f-2" mode="data" iconSize="{{150}}" textSize="{{24}}" text="暂无邮寄地址" icon class="data-v-f00be0f2" bind:__l="__l"></u-empty></view></block></view><view class="btn-container data-v-f00be0f2"><view data-event-opts="{{[['tap',[['add',['$event']]]]]}}" class="btn data-v-f00be0f2" bindtap="__e">新增收货地址</view></view></view>