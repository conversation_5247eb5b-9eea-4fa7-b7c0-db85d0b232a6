<template>
	<view class="container">
		<view class="content">


			<view class="form">
				<view class="form-item">
					<text class="label"><span class="red">*</span>收货人</text>
					<input class="value" style="text-align: left;" v-model.trim="stuInfo.name" placeholder="请输入姓名" />
				</view>
				<view class="form-item">
					<text class="label"><span class="red">*</span>联系电话</text>
					<input class="value" style="text-align: left;" v-model.trim="stuInfo.phone" placeholder="请输入手机号"
						maxlength="11" />
				</view>
				<view class="form-item">
					<text class="label"><span class="red">*</span>所在地区</text>
					<view class="right selectCity">
						<select-city :type="type" :districtCode="stuInfo.pcaCode" @confirm="handleConfirm" />
						<uni-icons class="icon-right" type="right" size="20" color="#C3C3C3"></uni-icons>
					</view>
				</view>
				<view class="form-item-step-three">
					<text class="label"><span class="red">*</span>详细地址：</text>
					<view class="value-container">
						<textarea maxlength='100' v-model="stuInfo.detailPlace" class="value-input"
							placeholder="请输入街道门牌信息" />
					</view>
				</view>
				<view class="form-item last-item">
					<text>设为默认地址</text>
					<u-switch v-model="stuInfo.isDefault" activeColor="#22c8a3" size="40"></u-switch>
				</view>
			</view>
			<view class="toolbar">
				<view class="pay-btn" @click="submit">提交</view>
			</view>
		</view>
	</view>
</template>

<script>
	import selectCity from '@/components/select_city/select_city.vue';
	import {
		addShippingAddress
	} from '@/api/comm.js'
	export default {
		components: {
			selectCity
		},
		data() {
			return {
				stuInfo: {
					name: '',
					phone: '',
					pcaCode: '',
					detailPlace: '',
					isDefault: 0,
				},
				type: "1",
			};
		},
		onLoad() {
			// this.getAddress()
		},
		methods: {
			async getAddress() {
				const {
					data,
					errCode,
					msg
				} = await getShippingAddress()
				if (errCode == 0) {
					if (data) {
						this.stuInfo = data
					}

				}
			},
			handleConfirm(result) {
				this.stuInfo.pcaCode = result
			},
			async submit() {
				if (this.stuInfo.name == "") {
					return uni.tip("收货人不能为空")
				}
				if (this.stuInfo.phone == '') {
					return uni.tip("请输入联系电话")
				}
				if (!/^1[3-9]\d{9}$/.test(this.stuInfo.phone)) {
					return uni.tip('请输入正确的联系电话')
				}
				if (this.stuInfo.pcaCode == '') {
					return uni.tip("请选择所在地区")
				}
				if (this.stuInfo.detailPlace == '') {
					return uni.tip("请输入详细地址")
				}
				const {
					data,
					errCode,
					msg
				} = await addShippingAddress({
					...this.stuInfo
				})
				if (errCode == 0) {
					uni.showToast({
						title: '新增成功',
						icon: "success"
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 2000)
				}
			},
		},
	}
</script>
<style>
	page {
		background: #F6F7FB;
	}
</style>
<style lang="scss" scoped>
	.container {
		box-sizing: border-box;
		margin-top: 20rpx;

		.content {
			width: 750rpx;
			padding: 50rpx 30rpx;
			height: 100vh;
			box-sizing: border-box;
			background-color: #fff;
		}

		.form {
			border-radius: 10rpx;
			padding-bottom: 20rpx;
			box-sizing: border-box;
		}

		.form-item-top {
			// width: 690rpx;
			height: 230rpx;
			background: #FFFFFF;
			border-radius: 16rpx;
			border: 1rpx solid #26C8AC;
			margin-bottom: 30rpx;
			padding: 20rpx 30rpx;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			align-items: flex-end;

			textarea {
				width: 100%;
				flex: 1;
				font-size: 30rpx;
				color: #989898;
			}

			.detailBtn {
				width: 150rpx;
				height: 56rpx;
				line-height: 56rpx;
				text-align: center;
				font-weight: bold;
				font-size: 24rpx;
				color: #FFFFFF;
				background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
				border-radius: 20rpx;
			}
		}

		.form-item-box {
			display: flex;
			flex-direction: column;
			min-height: 106rpx;
			border-bottom: 1px solid #eee;

			.form-item {
				min-height: 80rpx;
				border-bottom: none;
				margin-top: 20rpx;
			}

			.cursor {
				color: #f56c6c;
			}
		}

		.form-item {
			display: flex;
			min-height: 95rpx;
			// justify-content: space-between;
			align-items: center;
			background-color: #fff;
			padding: 0 30rpx !important;
			margin-bottom: 30rpx;
			border-radius: 16rpx;
			border: 1rpx solid #2FC293;

			.right {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}
		}

		.last-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.label {
			// flex: 1;
			min-width: 180rpx;
			font-size: 30rpx;
			color: #060606;

			.red {
				color: #f56c6c;
			}
		}

		.value {
			font-size: 30rpx;
			color: #777777;
			text-align: left;
			flex: 1;
			// min-width: 440rpx;
			margin-right: 20rpx;
		}

		.value-input {
			flex: 1;
			padding: 10rpx;
			font-size: 30rpx;
			color: #777777;
			border: 0;
			border-radius: 8rpx;
			outline: none;

		}

		.value-input:focus {
			border-color: #1BB394;
		}

		.form-item-step-three {
			height: 240rpx;
			background: #FFFFFF;
			border-radius: 17rpx;
			padding: 26rpx 30rpx;
			margin-bottom: 26rpx;
			display: flex;
			align-items: flex-start;
			border: 1rpx solid #2FC293;

			.label {
				flex: 1;
				font-weight: bold;
				font-size: 30rpx;
				color: #504E4E;
			}
		}

		.value-container {
			display: flex;
			align-items: center;
			width: 460rpx;
			margin-top: -10rpx;
			// height: 148rpx;
		}

		.value-input {
			flex: 1;
			padding: 10rpx;
			font-size: 30rpx;
			color: #777777;
			border: 0;
			border-radius: 8rpx;
			outline: none;

		}

		.value-input:focus {
			border-color: #1BB394;
		}


		::v-deep .checkbox__inner {
			border-radius: 16rpx !important;
		}

		.toolbar {
			position: fixed;
			bottom: 50rpx;
			left: 0;
			right: 0;
			height: 100rpx;
			background: #fff;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 20rpx;
			// box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);


			.pay-btn {
				width: 510rpx;
				height: 92rpx;
				border-radius: 40rpx;
				line-height: 92rpx;
				text-align: center;
				background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
				font-weight: bold;
				font-size: 30rpx;
				color: #FFFFFF;
				margin: 0 auto;
			}

		}
	}
</style>