<view><view class="box"><view class="box_content"><view class="box_img"><image src="/static/Project_drawing 27.png" mode></image></view><view class="box_price"><text>预约时间</text><block wx:if="{{timeDate}}"><text>{{timeDate}}</text></block><block wx:else><text>--</text></block></view></view><view data-event-opts="{{[['tap',[['setshow',['$event']]]]]}}" class="box_close" bindtap="__e">去预约</view></view><u-popup vue-id="77f332fc-1" show="{{show}}" round="{{10}}" zIndex="10071" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup_box"><view class="popup_box_title flexc flexs"><view></view><view>预约</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" style="padding-top:10rpx;box-sizing:border-box;" bindtap="__e"><u-icon vue-id="{{('77f332fc-2')+','+('77f332fc-1')}}" name="close" size="28" bind:__l="__l"></u-icon></view></view><view class="popup_box_ipt flexc flexs"><text style="width:120rpx;text-align:left;">到店时间:</text><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="popup_box_ipt_right" style="padding-left:8rpx;" bindtap="__e"><block wx:if="{{ddtime}}"><text>{{ddtime}}</text></block><block wx:else><text>请选择到店时间</text></block></view></view><view class="popup_box_ipt flexc flexs"><text style="width:120rpx;text-align:left;">姓名:</text><input class="popup_box_ipt_right" type="text" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['','subscribe_name','$event',[]]]]]]}}" value="{{subscribe_name}}" bindinput="__e"/></view><view class="popup_box_ipt flexc flexs"><text style="width:120rpx;text-align:left;">手机号:</text><input class="popup_box_ipt_right" type="text" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['','subscribe_tel','$event',[]]]]]]}}" value="{{subscribe_tel}}" bindinput="__e"/></view><view class="popup_box_btns flexs"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="popup_box_btn" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['getreservation',['$event']]]]]}}" class="popup_box_btn" bindtap="__e">确定</view></view></view></u-popup><u-datetime-picker vue-id="77f332fc-3" show="{{timeshow}}" mode="time" value="{{value1}}" data-event-opts="{{[['^cancel',[['e4']]],['^confirm',[['timeConfirm']]],['^input',[['__set_model',['','value1','$event',[]]]]]]}}" bind:cancel="__e" bind:confirm="__e" bind:input="__e" bind:__l="__l"></u-datetime-picker><login vue-id="77f332fc-4" show="{{enter}}" data-event-opts="{{[['^closepage',[['e5']]]]}}" bind:closepage="__e" bind:__l="__l"></login></view>