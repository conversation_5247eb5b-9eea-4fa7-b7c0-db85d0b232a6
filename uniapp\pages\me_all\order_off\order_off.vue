<template>
	<view class="container">
		<view class="top">
			<view class="tip">
				<text class="tip-text">扫码核销</text>
			</view>
			<view class="ma" @click="startScan">
				<image src="../../../static/erweima.png" mode=""></image>
				<text>扫码核销</text>
			</view>
		</view>
		<view class="top">
			<view class="tip">
				<text class="tip-text">核销码</text>
			</view>
			<input type="text" v-model="code" placeholder="请输入核销码" />
			<view class="btns" @click="now">立刻核销</view>
		</view>
		<u-popup :show="visible" :round="10" mode="center" @close="close" @open="open">
			<view class="popupContent">
				<view class="content">
					<view class="title">{{detail.productName}}</view>
					<view class="detail">
						<!-- <text class="subject">考研英语</text> -->
						<text class="endTime" v-if="detail.startTime">{{detail.startTime}}-{{detail.startEnd}}</text>
						<text class="num" v-if="detail.hours">共{{detail.hours}}节</text>
					</view>
					<view class="bottom">
						<view class="teacher-list" v-if="detail.teachers">
							<view class="teacher-info" v-for="(item,index) in detail.teachers" :key="index">
								<image class="avatar" :src="item.img"></image>
								<text>{{item.title}}</text>
							</view>

						</view>

					</view>
				</view>
				<view class="btn" @click="redeem">立即核销</view>
				<view class="btn" @click="close">取消</view>
			</view>

		</u-popup>
	</view>
	</view>
</template>

<script>
	import {
		queryInfoForRedeem,
		wxOrderRedeem
	} from '@/api/comm.js'
	export default {
		data() {
			return {
				visible: false,
				detail: {},
				code: '',
				order: {}
			}
		},
		methods: {
			// 立即核销
			async redeem() {
				const {
					data,
					errCode,
					msg
				} = await wxOrderRedeem({
					...this.order
				})
				if (errCode == 0) {
					uni.showToast({
						title: '核销成功',
						icon: 'success'
					})
					this.visible = false
				} else {
					uni.showToast({
						title: msg,
						icon: 'none'
					})
				}
			},
			// 取消核销
			close() {
				this.visible = false
			},
			// 立即核销
			now() {
				this.verifyCode(this.code)
			},
			getObj(str) {
				const delimiter = "_";
				const fields = ["orderId", "orderProductId", "redeemCode"];
				// 分割字符串
				const parts = str.split(delimiter);

				// 创建结果对象
				const result = {};

				// 将分割后的部分映射到对象属性
				fields.forEach((field, index) => {
					result[field] = parts[index] || '';
				});

				this.order = result
				this.verifyCode(this.order.redeemCode);
			},
			// 开始扫码
			startScan() {
				// 检查权限
				this.checkCameraPermission().then(() => {
					// 开始扫码
					wx.scanCode({
						onlyFromCamera: true, // 只允许从相机扫码
						scanType: ['qrCode'], // 只识别二维码
						success: (res) => {
							this.getObj(res.result)
							// 验证核销码
						},
						fail: (err) => {
							console.error('扫码失败:', err);
							wx.showToast({
								title: '扫码失败，请重试',
								icon: 'none'
							});
						}
					});
				}).catch(err => {
					console.error('摄像头权限错误:', err);
				});
			},

			// 检查摄像头权限
			checkCameraPermission() {
				return new Promise((resolve, reject) => {
					wx.authorize({
						scope: 'scope.camera',
						success: resolve,
						fail: () => {
							wx.showModal({
								title: '权限提示',
								content: '需要摄像头权限进行扫码，是否去设置开启？',
								success: (res) => {
									if (res.confirm) {
										wx.openSetting({
											success: (settingRes) => {
												if (settingRes.authSetting[
														'scope.camera']) {
													resolve();
												} else {
													reject('用户未授权摄像头权限');
												}
											}
										});
									} else {
										reject('用户拒绝授权');
									}
								}
							});
						}
					});
				});
			},

			// 验证核销码
			verifyCode(val) {
				const {
					data,
					errCode,
					msg
				} = queryInfoForRedeem({
					redeemCode: val
				})
				if (errCode == 0) {
					this.detail = data
					this.visible = true
				} else {
					wx.showToast({
						title: msg,
						icon: 'none'
					});
				}
				// wx.showLoading({
				// 	title: '核销中...',
				// 	mask: true
				// });

				// // 调用后端API验证核销码
				// wx.request({
				// 	url: 'https://your-api.com/api/verify',
				// 	method: 'POST',
				// 	header: {
				// 		'Content-Type': 'application/json',
				// 		'Authorization': `Bearer ${wx.getStorageSync('token')}`
				// 	},
				// 	data: {
				// 		code: code,
				// 		merchantId: wx.getStorageSync('merchantId')
				// 	},
				// 	success: (res) => {
				// 		wx.hideLoading();
				// 		if (res.data.success) {
				// 			this.setData({
				// 				result: res.data.data
				// 			});
				// 			wx.showToast({
				// 				title: '核销成功',
				// 				icon: 'success'
				// 			});

				// 			// 播放成功音效
				// 			wx.playBackgroundAudio({
				// 				dataUrl: '/sounds/success.mp3'
				// 			});
				// 		} else {
				// 			wx.showToast({
				// 				title: res.data.message || '核销失败',
				// 				icon: 'none'
				// 			});
				// 		}
				// 	},
				// 	fail: (err) => {
				// 		wx.hideLoading();
				// 		console.error('核销请求失败:', err);
				// 		wx.showToast({
				// 			title: '网络错误，请重试',
				// 			icon: 'none'
				// 		});
				// 	}
				// });
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.top {
			width: 690rpx;
			margin: 30rpx auto;
			padding: 30rpx 30rpx 45rpx;
			box-sizing: border-box;
			background-color: #fff;

			.tip {
				.tip-text {
					position: relative;
					display: inline-block;
					font-weight: 800;
					font-size: 30rpx;
					color: #060606;
					z-index: 100;
				}

				.tip-text::after {
					content: "";
					position: absolute;
					bottom: -6rpx;
					left: 0;
					width: 104%;
					height: 20rpx;
					/* 指定高度 */
					background-color: #DBFF9C;
					/* 底部背景颜色 */
					z-index: -1;
				}
			}

			.ma {
				width: 360rpx;
				height: 360rpx;
				// text-align: center;
				font-weight: 400;
				font-size: 30rpx;
				color: #FFFFFF;
				margin: 30rpx auto 0;
				position: relative;

				image {
					position: absolute;
					width: 360rpx;
					height: 360rpx;
				}

				text {
					position: absolute;
					top: 220rpx;
					left: 50%;
					transform: translateX(-50%);
					z-index: 9;
				}
			}

			input {
				width: 563rpx;
				height: 106rpx;
				text-align: center;
				font-size: 32rpx;
				color: #5A5A5A;
				margin: 50rpx auto 80rpx;
				background: #F6F7FB;
				border-radius: 16rpx;
			}

			.btns {
				width: 550rpx;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				font-weight: bold;
				font-size: 30rpx;
				color: #FFFFFF;
				background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
				border-radius: 40rpx;
				margin: 0 auto;
			}
		}



		::v-deep .u-popup__content {
			background-color: transparent !important;
		}

		.popupContent {
			width: 555rpx;
			height: 587rpx;
			padding: 40rpx 30rpx 50rpx;
			box-sizing: border-box;
			background: url(https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/ticket_bg.png) no-repeat;
			background-size: 100%;
		}

		.content {
			height: 250rpx;

			.title {
				font-weight: bold;
				font-size: 26rpx;
				color: #060606;
				line-height: 1.4;
			}

			.detail {
				margin-top: 12rpx;
				display: flex;
				align-items: center;
				color: #A4A4A4;

				text {

					font-weight: 500;
					font-size: 24rpx;
					color: #A4A4A4;
				}

				.subject {
					font-weight: bold;
					font-size: 22rpx;
					color: #09CC8C;
					padding: 9rpx 7rpx;
					background: #EEFAF6;
					border-radius: 10rpx;
				}

				.endTime {
					margin: 0 12rpx;
					position: relative;

					&::after {
						content: '';
						position: absolute;
						width: 1rpx;
						height: 25rpx;
						top: 50%;
						transform: translateY(-50%);
						right: -12rpx;
						background-color: #A4A4A4;
					}
				}

				.num {
					margin-left: 12rpx;
				}
			}

			.bottom {
				margin-top: 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.teacher-list {
					padding-left: 10rpx;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					flex-wrap: wrap;

					.teacher-info {
						margin-right: 15rpx;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						font-size: 22rpx;
						color: #818181;
						margin-bottom: 20rpx;

						&:last-child {
							margin-right: 0;
						}

						.avatar {
							width: 60rpx;
							height: 60rpx;
							border-radius: 100%;
							margin-bottom: 2rpx;
						}

					}
				}

			}
		}

		.btn {
			width: 390rpx;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-weight: bold;
			font-size: 30rpx;
			color: #FFFFFF;
			margin: 50rpx auto 25rpx;
			background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
			border-radius: 40rpx;

			&:last-child {
				color: #989898;
				margin: 0 auto 0;
				background: #F6F7FB;
			}
		}
	}
</style>