@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content.data-v-aa0cedb0 {
  min-height: 100vh;
  background: #ffffff;
}
.content_header.data-v-aa0cedb0 {
  position: relative;
  height: 489rpx;
  background: linear-gradient(180deg, #00c2a0 0%, #97d9c8 100%);
}
.content_header .nav-title.data-v-aa0cedb0 {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 10;
}
.content_header .nav-title text.data-v-aa0cedb0 {
  font-size: 34rpx;
  color: #ffffff;
  font-weight: 400;
}
.content_header .back-left.data-v-aa0cedb0 {
  position: absolute;
  left: 30rpx;
  z-index: 10;
}
.content_header .header-image.data-v-aa0cedb0 {
  position: absolute;
  right: 30rpx;
  top: 140rpx;
  width: 278rpx;
  height: 278rpx;
}
.ai-section.data-v-aa0cedb0 {
  padding: 60rpx 30rpx;
  background: #f6f7fb;
}
.ai-section .ai-container.data-v-aa0cedb0 {
  position: relative;
}
.ai-section .ai-container .ai-images.data-v-aa0cedb0 {
  position: relative;
  height: 598rpx;
  margin-bottom: 60rpx;
}
.ai-section .ai-container .ai-images .ai-bg-4.data-v-aa0cedb0 {
  position: absolute;
  top: 0;
  left: 159rpx;
  width: 432rpx;
  height: 427rpx;
  z-index: 1;
}
.ai-section .ai-container .ai-images .ai-bg-1.data-v-aa0cedb0 {
  position: absolute;
  top: 17rpx;
  left: 175rpx;
  width: 400rpx;
  height: 398rpx;
  z-index: 2;
}
.ai-section .ai-container .ai-images .ai-bg-2.data-v-aa0cedb0 {
  position: absolute;
  top: 90rpx;
  left: 180rpx;
  width: 387rpx;
  height: 328rpx;
  z-index: 3;
}
.ai-section .ai-container .ai-images .ai-center.data-v-aa0cedb0 {
  position: absolute;
  top: 135rpx;
  left: 253rpx;
  width: 240rpx;
  height: 240rpx;
  z-index: 4;
}
.ai-section .ai-container .ai-images .ai-center .ai-main.data-v-aa0cedb0 {
  width: 100%;
  height: 100%;
}
.ai-section .ai-container .ai-images .ai-center .ai-text.data-v-aa0cedb0 {
  position: absolute;
  top: 61rpx;
  left: 31rpx;
  width: 178rpx;
  height: 121rpx;
}
.ai-section .ai-container .generate-btn.data-v-aa0cedb0 {
  width: 521rpx;
  height: 78rpx;
  background: linear-gradient(135deg, #26c8ac 0%, #19c990 100%);
  border-radius: 39rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20rpx;
}
.ai-section .ai-container .generate-btn text.data-v-aa0cedb0 {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 400;
}
.ai-section .ai-container .usage-count.data-v-aa0cedb0 {
  text-align: center;
  font-size: 26rpx;
  color: #989898;
}
.report-section.data-v-aa0cedb0 {
  padding: 0 30rpx 60rpx;
}
.report-section .section-title.data-v-aa0cedb0 {
  font-size: 30rpx;
  color: #060606;
  margin-bottom: 30rpx;
  padding: 30rpx 0;
  position: relative;
}
.report-section .section-title.data-v-aa0cedb0::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 120rpx;
  height: 19rpx;
  background: #dbff9c;
}
.report-section .report-list .report-item.data-v-aa0cedb0 {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 28rpx;
  margin-bottom: 30rpx;
  box-shadow: 0px 8px 8px 0px #ebebeb;
}
.report-section .report-list .report-item .report-header.data-v-aa0cedb0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.report-section .report-list .report-item .report-header .report-name.data-v-aa0cedb0 {
  font-size: 34rpx;
  color: #00c2a0;
  font-weight: 400;
}
.report-section .report-list .report-item .report-header .report-status.data-v-aa0cedb0 {
  padding: 8rpx 20rpx;
  border-radius: 28rpx;
  font-size: 28rpx;
}
.report-section .report-list .report-item .report-header .report-status.status-completed.data-v-aa0cedb0 {
  background: #d6ffec;
  color: #00c2a0;
}
.report-section .report-list .report-item .report-header .report-status.status-pending.data-v-aa0cedb0 {
  background: #fff3e0;
  color: #ff9800;
}
.report-section .report-list .report-item .report-desc.data-v-aa0cedb0 {
  font-size: 28rpx;
  color: #5a5a5a;
  line-height: 1.5;
  margin-bottom: 20rpx;
}
.report-section .report-list .report-item .report-footer.data-v-aa0cedb0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.report-section .report-list .report-item .report-footer .report-time.data-v-aa0cedb0 {
  font-size: 26rpx;
  color: #818181;
}
.report-section .report-list .report-item .report-footer .report-detail.data-v-aa0cedb0 {
  font-size: 26rpx;
  color: #00c2a0;
}

