{"version": 3, "sources": ["webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/select_city/select_city.vue?16a4", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/select_city/select_city.vue?5f2c", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/select_city/select_city.vue?7381", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/select_city/select_city.vue?b0c3", "uni-app:///components/select_city/select_city.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/select_city/select_city.vue?1b8e", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/select_city/select_city.vue?f067"], "names": ["props", "districtCode", "type", "default", "data", "allRegions", "currentIndex", "computed", "provinces", "cities", "districts", "currentRegion", "watch", "immediate", "handler", "mounted", "res", "methods", "setCurrentByCode", "console", "targetIndexes", "handleChange", "column", "value", "newIndex", "handleConfirm"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;;;AAGvE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACUznB;;;;;;;;;;eAGA;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACAD;MACAA;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MACA,2BACA;IACA;IACAC;MAAA;MACA,2BACA;IACA;IACAC;MAAA;MACA;MACA,kCACA,sJACA,kJACA,yHACA;IACA;EACA;EACAC;IACA;IACAX;MACAY;MACAC;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OAOA;YAAA;cAAAC;cACA;cACA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAC;IACA;IACAC;MACAC;MACA;;MAEA;MACA;QAAA;QACA;UAAA;UACA;YACA;cACAC;cACA;YACA;UACA;QACA;MACA;MAEA;QACA;MACA;IACA;IAEAC;MACA;QAAAC;QAAAC;MACA;MAEAC;MACA;MACA;MAEA;IACA;IAEAC;MACA;MACA;MACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxHA;AAAA;AAAA;AAAA;AAAu3B,CAAgB,w3BAAG,EAAC,C;;;;;;;;;;;ACA34B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/select_city/select_city.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./select_city.vue?vue&type=template&id=21e89522&\"\nvar renderjs\nimport script from \"./select_city.vue?vue&type=script&lang=js&\"\nexport * from \"./select_city.vue?vue&type=script&lang=js&\"\nimport style0 from \"./select_city.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/select_city/select_city.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./select_city.vue?vue&type=template&id=21e89522&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./select_city.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./select_city.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<picker mode=\"multiSelector\" :range=\"[provinces, cities, districts]\" range-key=\"name\" :value=\"currentIndex\"\r\n\t\t@columnchange=\"handleChange\" @change=\"handleConfirm\">\r\n\t\t<view class=\"simple-picker\">\r\n\t\t\t{{ currentRegion || '点击选择地区' }}\r\n\t\t</view>\r\n\t</picker>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetPcaJsonData,\r\n\t} from \"@/api/comm.js\"\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\t// 接收外部传入的区code\r\n\t\t\tdistrictCode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tallRegions: [],\r\n\t\t\t\tcurrentIndex: null // 初始为null表示未选择\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tprovinces() {\r\n\t\t\t\treturn this.allRegions\r\n\t\t\t},\r\n\t\t\tcities() {\r\n\t\t\t\treturn this.currentIndex ?\r\n\t\t\t\t\tthis.provinces[this.currentIndex[0]]?.children || [] : []\r\n\t\t\t},\r\n\t\t\tdistricts() {\r\n\t\t\t\treturn this.currentIndex ?\r\n\t\t\t\t\tthis.cities[this.currentIndex[1]]?.children || [] : []\r\n\t\t\t},\r\n\t\t\tcurrentRegion() {\r\n\t\t\t\tif (!this.currentIndex) return ''\r\n\t\t\t\treturn [\r\n\t\t\t\t\tthis.provinces[this.currentIndex[0]]?.name,\r\n\t\t\t\t\tthis.cities[this.currentIndex[1]]?.name,\r\n\t\t\t\t\tthis.districts[this.currentIndex[2]]?.name\r\n\t\t\t\t].join('/')\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 监听外部传入的区code变化\r\n\t\t\tdistrictCode: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(code) {\r\n\t\t\t\t\tif (code && this.allRegions.length) {\r\n\t\t\t\t\t\tthis.setCurrentByCode(code)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync mounted() {\r\n\t\t\t// if (this.type == 1) {\r\n\t\t\t// \tconst res = await getSchoolPcaJsonData()\r\n\t\t\t// \tthis.allRegions = res.data || [];\r\n\t\t\t// \t// 初始化时如果有默认code\r\n\t\t\t// \tif (this.districtCode) this.setCurrentByCode(this.districtCode)\r\n\t\t\t// } else {\r\n\t\t\tconst res = await getPcaJsonData()\r\n\t\t\tthis.allRegions = res.data || [];\r\n\t\t\t// 初始化时如果有默认code\r\n\t\t\tif (this.districtCode) this.setCurrentByCode(this.districtCode)\r\n\t\t\t// }\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 核心方法：根据区code设置选中状态\r\n\t\t\tsetCurrentByCode(code) {\r\n\t\t\t\tconsole.log(code)\r\n\t\t\t\tlet targetIndexes = null\r\n\r\n\t\t\t\t// 三级遍历查找\r\n\t\t\t\tthis.allRegions.some((province, pIndex) => {\r\n\t\t\t\t\treturn province.children?.some((city, cIndex) => {\r\n\t\t\t\t\t\treturn city.children?.some((district, dIndex) => {\r\n\t\t\t\t\t\t\tif (district.code === code) {\r\n\t\t\t\t\t\t\t\ttargetIndexes = [pIndex, cIndex, dIndex]\r\n\t\t\t\t\t\t\t\treturn true\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\r\n\t\t\t\tif (targetIndexes) {\r\n\t\t\t\t\tthis.currentIndex = targetIndexes\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\thandleChange(e) {\r\n\t\t\t\tconst [column, value] = [e.detail.column, e.detail.value]\r\n\t\t\t\tconst newIndex = this.currentIndex ? [...this.currentIndex] : [0, 0, 0]\r\n\r\n\t\t\t\tnewIndex[column] = value\r\n\t\t\t\tif (column === 0) newIndex.splice(1, 2, 0, 0)\r\n\t\t\t\tif (column === 1) newIndex[2] = 0\r\n\r\n\t\t\t\tthis.currentIndex = newIndex\r\n\t\t\t},\r\n\r\n\t\t\thandleConfirm() {\r\n\t\t\t\tif (!this.currentIndex) return\r\n\t\t\t\tconst district = this.districts[this.currentIndex[2]]\r\n\t\t\t\tif (district) {\r\n\t\t\t\t\tthis.$emit('update:districtCode', district.code) // 只返回区code\r\n\t\t\t\t\tthis.$emit('confirm', district.code)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t.simple-picker {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #777777;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./select_city.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./select_city.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557530820\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}