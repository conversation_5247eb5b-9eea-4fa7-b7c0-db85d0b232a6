.popBox {
  padding: 32rpx;
  box-sizing: border-box;
  position: relative;
  width: 100%;
}
.popBox .popBox_title {
  font-size: 32rpx;
  color: #000;
  width: 100%;
  margin-bottom: 36rpx;
}
.popBox .popBox_content {
  font-size: 32rpx;
}
.popBox .popBox_content .popBox_content_item {
  height: 100rpx;
  width: 750rpx;
}
.popBox .popBox_content .popBox_content_item image {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 18rpx;
}
.payBtn {
  width: 623rpx;
  height: 80rpx;
  background: #157FE7;
  border-radius: 41rpx;
  text-align: center;
  line-height: 80rpx;
  font-size: 30rpx;
  margin: 40rpx auto 0;
  color: #fff;
}

