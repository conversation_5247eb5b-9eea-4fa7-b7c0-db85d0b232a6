<template>
	<view class="container">
		<view class="info" v-for="(item, index) in list" :key="index">
			<view class="info-title">
				<image class="logo" src="../static/imgs/rhomb.png" mode=""></image>
				<text class="title-text">
					{{ item.title }}
				</text>
			</view>

			<view class="info-content" v-for="(obj, key) in item.content" :key="key">
				<view class="content-title">
					<view class="cricle">

					</view>
					<view class="content-title-text">
						{{ obj.title }}
					</view>
				</view>

				<view class="subtitle-content-container">
					<template v-if="obj.list.length">
						<view class="subtitle-content" v-for="(value, k) in obj.list" :key="k">


							<view class="subtitle-text">
								{{ value.name }}
							</view>
							<view class="subtitle-content-text">
								<rich-text :nodes="value.content"></rich-text>
							</view>

						</view>
					</template>
					<!-- <rich-text :nodes="obj.content"></rich-text> -->
					<view class="" v-else v-html="obj.content"></view>
					<!-- {{obj.list.length}}{{obj.list}} -->
					<!-- <view class="" @click="again('xygh')">重新获取</view> -->
					<text class="loading" v-if="!obj.list.length&&!typeList.includes(obj.type)">
						<view class="loading-spinner"></view>
					</text>
					<text style="color:red;" v-if="typeList.includes(obj.type)"> 服务器繁忙，请重试</text>
					<view class="again" v-if="typeList.includes(obj.type)" @click="again(obj.type)">
						重新生成</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import sseEvent from "@/utils/sse_event.js"
	import {
		getReportTagList
		// getOrgListBy
	} from "@/api/user.js"
	import {
		mapState
	} from "vuex"
	export default {
		data() {
			return {
				list: [{
					title: "大一期间核心学业规划",
					content: {
						zzsh: {
							title: '组织生活',
							content: '',
							type: 'zzsh',
							list: [

							]
						},
						xygh: {
							title: '学业规划',
							content: '',
							type: 'xygh',
							list: [

							]
						},
						sxbk: {
							title: '升学准备',
							content: '',
							type: 'sxbk',
							list: [

							]
						},
						jyzb: {
							title: '就业指导',
							content: '',
							type: 'jyzb',
							list: [

							]
						},
					}
				}],
				obj: {
					zzsh: '',
					xygh: '',
					sxbk: '',
					jyzb: ''
				},
				grade_1: {

				},
				urls: '',
				role: '',

			};
		},
		props: {
			msgtype: {
				type: Array,
				default: () => []
			}
		},
		computed: {
			...mapState('user', ['typeList']),
		},
		created() {
			// this.role = JSON.parse(uni.getStorageSync('USER-INFO-KEY')).role
			// if (this.role == 'user') {
			// 	this.urls = '/my_dxt/report/queryBlock2'
			// }
			// if (this.role == 'student') {
			this.urls = '/stu/report/queryBlock2'
			// }
			for (let type in this.list[0]['content']) {
				this.fetchSseData(type)
			}

		},
		methods: {
			again(type) {
				this.$store.commit('user/removeType', type)
				this.list[0]['content'][type].content = ''
				this.fetchSseData(type)
			},
			async fetchSseData(type) {
				let requestUrl = ''
				// if (this.role == 'student') {
				requestUrl =
					`${uni.http.baseUrl}${this.urls}?
									type=${type}
									&token=${this.$store.getters.token}
									&reportId=${this.$store.state.user.reportInfo.report.id}
									`;
				// }
				// if (this.role == 'user') {
				// 	let params = {
				// 		...this.$store.state.user.reportInfo,
				// 		reportId: this.$store.state.user.reportInfo.report.id,
				// 		type: type,
				// 		token: this.$store.getters.token,
				// 		postGraduation: JSON.stringify(this.$store.state.user.reportInfo.postGraduation),
				// 	}
				// 	let queryStr = Object.keys(params)
				// 		.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
				// 		.join('&');
				// 	requestUrl = `${uni.http.baseUrl}${this.urls}?${queryStr}`;
				// }
				let buffers = '';
				try {
					const abortController = new AbortController();
					// 20秒总超时
					const timeout20s = setTimeout(() => {
						abortController.abort();
					}, 20000);

					// 10秒无响应中断
					let responseTimeout = setTimeout(() => {
						abortController.abort();
					}, 10000);
					// 每次调用 sseEvent 都需要等待它完成后才能继续下一个
					await sseEvent(requestUrl, type, (buffer, eventType, t, f) => {
						clearTimeout(responseTimeout);
						responseTimeout = setTimeout(() => {
							abortController.abort();
						}, 10000);
						if (eventType === "say") {
							if (timeout20s) {
								clearTimeout(timeout20s);
							}
							if (f == 'json') {
								this.list[0]['content'][type].list = buffer
							} else {
								let content = this.list[0]['content'][type].content
								console.log(content)
								content += buffer
								this.list[0]['content'][type].content = content.replace(/\*\*([^*]+)\*\*/g,
										"<b>$1</b>").replace(/&emsp;(.+?)##/g,
										"<view class='sec-p'>$1</view>##")
									.replace(/##(.+?)\n/g,
										"<view class='sec-title'>$1</view>&emsp;").replace(/\n{1,2}/g, "<br>")
									.replace(/[\[(（【]注[:：].*?[】）)\]]/g, "")
							}
						} else if (eventType === "reply") {
							if (timeout20s) {
								clearTimeout(timeout20s);
							}
							this.list[0]['content'][mask].list[index].content = buffer;
						} else if (eventType === "end") {
							if (f != 'json') {
								if (['zzsh', 'xygh', 'sxbk', 'jyzb'].includes(t)) {
									buffers = this.list[0]['content'][type].content.replace(/&emsp;(.+?$)/g,
										"<view class='sec-p'>$1</view>");
									const regex =
										/<view class='sec-title'>(.*?)<\/view>.*?<view class='sec-p'>(.*?)<\/view>/gs;
									const result = [];
									let match;

									while ((match = regex.exec(buffers)) !== null) {
										result.push({
											name: match[1].trim(),
											content: match[2].trim()
										});
									}
									this.list[0]['content'][t].list = result
								}
							}
							this.$store.commit('user/setPlan', {
								[type]: this.list[0]['content'][t].list
							});
						}
					}, {
						signal: abortController.signal
					});
					// 请求成功完成，清除定时器
					clearTimeout(timeout20s);
					clearTimeout(responseTimeout);
				} catch (error) {
					this.$store.commit('user/setType', type);
					console.error('请求错误:', error);
				}
				// }
			},
			async getTagList() {
				let result = {}
				// if (this.role == 'student') {
				const data = {
					reportId: this.$store.state.user.reportInfo.report.id,
				}
				result = await getReportTagList(data)
				// }
				// if (this.role == 'user') {
				// 	const data = {
				// 		studentId: this.$store.state.user.reportInfo.studentId,
				// 	}
				// 	result = await getOrgListBy(data)
				// }

				let advice = {}
				if (result.errCode == 0) {
					const tagList = {}
					result.data.forEach((item, index) => {
						let mark = ''
						if (index == 0) {
							mark = 'zzsh'
						} else {
							mark = item.mark
						}
						this.list[0]['content'][mark]['title'] = item.name
						item.children
							.filter(child => (Array.isArray(child.children) && child
								.children
								.length >
								0))
							.forEach(child => {
								child.children.forEach(grandson => {
									this.list[0]['content'][mark]['list']
										.push({
											id: grandson.id,
											subtitle: grandson.name,
											content: grandson.content,
											type: grandson.type ?
												grandson
												.type : ''
										})

								})

							})
					})
				}
			},


		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background: linear-gradient(180deg, rgba(203, 242, 224, 0.2) 0%, rgba(255, 255, 255, 0) 20%);
		border-radius: 14rpx;
		border: 2rpx dashed #1BB394;
		padding: 24rpx 28rpx;

		@keyframes icon-loading {
			0% {
				transform: rotate(0);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		.loading {
			color: #909090;
		}

		.loading svg {
			will-change: transform;
			width: 1em;
			height: 1em;
			animation: 0.6s linear infinite icon-loading;
		}

		.again {
			display: inline-block;
			width: 100rpx;
			height: 35rpx;
			line-height: 35rpx;
			border-radius: 10rpx;
			text-align: center;
			font-size: 16rpx;
			color: #fff;
			background: #1bb394;
			cursor: pointer;
			margin-left: 10rpx;
		}

		.loading-spinner {
			width: 36px;
			height: 36px;
			border: 4px solid rgba(0, 0, 0, 0.1);
			border-radius: 50%;
			border-top-color: currentColor;
			animation: spin 1s linear infinite;
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		.info {
			padding-top: 30rpx;

			.info-title {
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.logo {
					width: 32rpx;
					height: 24rpx;
					margin-right: 8rpx;
				}

				.title-text {
					font-weight: 800;
					font-size: 16rpx;
					color: #4C5370;
				}

			}

			.info-content {
				margin-top: 20rpx;

				.content-title {
					display: flex;
					justify-content: flex-start;
					align-items: center;

					.cricle {
						width: 6rpx;
						height: 6rpx;
						border-radius: 50%;
						background-color: #AEE8CD;
						margin-right: 10rpx;
					}

					.content-title-text {
						font-weight: bold;
						font-size: 16rpx;
						color: #5A5A5A;
					}

				}

				.subtitle-content {
					margin-top: 20rpx;
				}

				.subtitle-content-container {
					margin-top: 20rpx;
					border-radius: 9rpx;
					border: 1rpx solid #A0E4C4;
					padding: 28rpx;
					// padding-top: 0;

					.subtitle-text {
						display: inline-block;
						padding: 0 6rpx;
						border-radius: 4rpx 4rpx 4rpx 4rpx;
						border: 1rpx solid #FF9B3A;
						font-weight: 400;
						font-size: 16rpx;
						line-height: 32rpx;
						text-align: center;
						color: #FF9B3A;
					}

					.subtitle-content-text {
						margin-top: 10rpx;
						font-weight: 400;
						font-size: 16rpx;
						color: #504E4E;
						line-height: 24rpx;
					}
				}
			}
		}

		::v-deep .sec-title {
			border: 1rpx solid #ff9b3a;
			border-radius: 4rpx;
			font-size: 16rpx !important;
			padding: 6rpx;
			color: #ff9b3a;
			position: relative;
			// margin-bottom: 5px;

		}

		::v-deep .sec-p {
			width: 100%;
			border-radius: 8rpx;
			padding: 10rpx 15rpx;
			font-size: 16rpx;
			color: #504E4E;
			line-height: 24rpx;
			// border: 1px solid #e3e3e3;
			display: block;
			// margin: 10px 0 15px;
		}
	}
</style>