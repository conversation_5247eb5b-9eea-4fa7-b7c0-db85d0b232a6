<view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="search-container data-v-14542fb0" bindtap="__e"><view data-event-opts="{{[['tap',[['doNothing',['$event']]]]]}}" class="content data-v-14542fb0" catchtap="__e"><view class="search-input-container data-v-14542fb0"><input class="search-input data-v-14542fb0" type="text" placeholder="请输入关键词" data-event-opts="{{[['input',[['__set_model',['','value','$event',[]]]]]]}}" value="{{value}}" bindinput="__e"/><image class="search-logo data-v-14542fb0" src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/search.png" mode="widthFix"></image></view><view class="list data-v-14542fb0"><view class="items data-v-14542fb0"><block wx:for="{{searchedList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['chooseItem',['$0'],[[['searchedList','id',item.id]]]]]]]}}" class="item-text data-v-14542fb0" bindtap="__e"><rich-text nodes="{{item.nameStr}}" class="data-v-14542fb0"></rich-text></view></block></view></view></view></view>