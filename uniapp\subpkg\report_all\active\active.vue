<template>
	<view class="container">

		<view class="head">
			<uni-icons @tap="back" class="back-icon" type="left" size="20" color="#fff"></uni-icons>
		</view>
		<view class="login-form">

			<view class="active-code">
				<text class="active-code-title">激活码</text>
				<view class="active-code-btn-container">
					<!-- 修改为 8 个 input 框 -->
					<!-- <input v-for="i in 8" :key="i"  v-model="form.activateCodeArray[i - 1]"
						type="text" maxlength="1" @input="onInput(i - 1)"> -->
					<!-- <input v-for="(code, index) in codes" :key="index" v-model="codes[index]" :ref="`input${index}`"
						class="active-code-btn" maxlength="1" @input="handleInput(index, $event)"
						@keydown.delete="handleDelete(index, $event)" /> -->
					<input v-for="(item, index) in 8" :key="index" v-model.trim="codeParts[index]" :id="'input' + index"
						class="active-code-btn" type="text" maxlength="1" :focus="focusIndex === index"
						@input="handleInput(index, $event)" @keydown.delete.prevent="handleDelete(index, $event)"
						@paste.prevent="handleH5Paste" />
				</view>
			</view>
			<!-- 登录按钮 -->
			<button :loading="loading" class="login-btn" @click="submit">立即激活</button>
		</view>
		<view class="enter-pay">
			<view class="title">
				<image src="@/static/car.png" mode=""></image>
				<text>如无激活码，请购买大学入学发展报告</text>
			</view>
			<view class="btn">去购买</view>
		</view>
	</view>
</template>

<script>
	import {
		getActivecode,
		smsRegLogin,
		reportActivate
	} from '@/api/user.js'

	export default {
		data() {
			return {
				form: {
					phone: '',
					smsCode: '',
					activateCode: '',

				},
				codes: ['', '', '', '', '', ''],
				codeParts: Array(8).fill(''), // 8个空字符串
				focusIndex: 0,
				counting: false,
				timer: null,
				count: 60,
				loading: false,
				selectedAgreement: '', // 存储选中状态
				reportStatus: 0, //0: 未激活   1：服务已激活  2. 服务已使用
				from: 0, //0：客户录入  1：激活码注册 
			};
		},
		onLoad() {

		},
		computed: {
			codeText() {
				return this.counting ? `${this.count}s后重新获取` : '获取验证码'
			},
			fullCode() {
				return this.codeParts.join('');
			}
		},
		methods: {
			// 单选框变更事件
			onAgreementChange(e) {
				this.selectedAgreement = e.detail.value
			},
			// 获取验证码
			async getCode() {
				if (this.counting) return

				// 验证手机号
				if (!this.form.phone) {
					return uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
				}
				if (!/^1[3-9]\d{9}$/.test(this.form.phone)) {
					return uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
				}

				try {
					const res = await getActivecode({
						phone: this.form.phone
					})

					if (res.errCode === 0) {
						uni.showToast({
							title: '验证码已发送',
							icon: 'none'
						})
						this.startCount()
					} else {
						uni.showToast({
							title: res.msg || '发送失败',
							icon: 'none'
						})
					}
				} catch (e) {
					uni.showToast({
						title: '发送失败',
						icon: 'none'
					})

				}
			},

			// 启动倒计时
			startCount() {
				this.counting = true
				this.count = 60
				this.timer = setInterval(() => {
					if (this.count > 0) {
						this.count--
					} else {
						this.counting = false
						clearInterval(this.timer)
					}
				}, 1000)
			},

			// 提交激活
			async submit() {
				if (this.loading) return
				// 如果用户有token就不需要验证手机号和验证码以及用户协议
				if (!this.$store.getters.token) {
					// 表单验证
					if (!this.form.phone) {
						return uni.showToast({
							title: '请输入手机号',
							icon: 'none'
						})
					}
					if (!/^1[3-9]\d{9}$/.test(this.form.phone)) {
						return uni.showToast({
							title: '请输入正确的手机号',
							icon: 'none'
						})
					}
					if (!this.form.smsCode) {
						return uni.showToast({
							title: '请输入验证码',
							icon: 'none'
						})
					}
					if (!this.selectedAgreement) {
						uni.showToast({
							title: '请先同意协议',
							icon: 'none'
						})
						return
					}
				}


				// 检查激活码数组是否都有值
				if (this.codeParts.some(code => code === '')) {
					return uni.showToast({
						title: '请输入完整的激活码',
						icon: 'none'
					});
				}
				// 将激活码数组拼接成字符串
				this.form.activateCode = this.codeParts.join('');

				try {
					this.loading = true
					let res = {}
					if (!this.$store.getters.token) {
						res = await smsRegLogin({
							phone: this.form.phone,
							smsCode: this.form.smsCode,
							activateCode: this.form.activateCode
						})
						// 保存 token
						this.$store.commit('user/setToken', res.data)
						// 更新用户信息
						this.$store.commit('user/setUserInfo', {
							...res.student,
							role: 'student'
						})
					} else {

						res = await reportActivate({
							activateCode: this.form.activateCode
						})
					}

					if (res.errCode == 0) {
						uni.navigateTo({
							url: '/subpkg/report/report'
						})

						uni.showToast({
							title: '激活成功',
							icon: 'success'
						})


						if (this.$store.getters.token) {
							this.$store.dispatch('user/getReportData')
						}
					} else {
						uni.showToast({
							title: res.msg || '激活失败',
							icon: 'none'
						})
					}
				} catch (e) {
					console.log(e)
					uni.showToast({
						title: '激活失败',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}

			},
			handleDelete(index, e) {
				const currentValue = this.codeParts[index];

				if (currentValue) {
					// 情况1：当前有值，直接删除
					this.codeParts.splice(index, 1, '');
				} else if (index > 0) {
					// 情况2：当前为空，删除前一位
					this.codeParts.splice(index - 1, 1, '');
					this.focusIndex = index - 1;

					// 安全访问ref（关键修复）
					this.$nextTick(() => {
						const refName = `input${index - 1}`;
						if (this.$refs[refName] && typeof this.$refs[refName].focus === 'function') {
							this.$refs[refName].focus();
						}
					});
				}
			},

			handleInput(index, e) {
				const value = e.target.value.replace(/[^a-zA-Z0-9]/g, '');
				this.codeParts.splice(index, 1, value.slice(0, 1));

				if (value && index < 7) {
					this.focusIndex = index + 1;

					// 安全跳转（跨平台兼容）
					this.$nextTick(() => {
						const refName = `input${index + 1}`;
						if (this.$refs[refName] && typeof this.$refs[refName].focus === 'function') {
							this.$refs[refName].focus();
						}
					});
				}
			},
			async handleH5Paste(e) {
				try {
					// 1. 安全获取剪贴板数据
					let pasteText = '';

					// 方法1：标准H5 API（需要HTTPS环境）
					if (navigator.clipboard && !this.isInWechat()) {
						pasteText = await navigator.clipboard.readText()
					}
					// 方法2：降级处理（兼容HTTP和微信浏览器）
					else if (e.clipboardData) {
						pasteText = e.clipboardData.getData('text')
					}
					// 方法3：兼容微信浏览器
					else if (this.isInWechat()) {
						const res = await this.wechatPaste()
						pasteText = res
					}

					// 3. 数据清洗处理
					const cleanText = this.cleanInputText(pasteText)


					// 4. 填充输入框
					this.fillCodeInputs(cleanText)

				} catch (error) {
					console.error('粘贴失败:', error)
				} finally {

				}
			},

			// 微信浏览器特殊处理
			async wechatPaste() {
				return new Promise((resolve) => {
					const input = document.createElement('input')
					input.style.position = 'fixed'
					input.style.opacity = 0
					document.body.appendChild(input)
					input.focus()

					setTimeout(() => {
						const text = input.value
						document.body.removeChild(input)
						resolve(text)
					}, 100)
				})
			},

			// 数据清洗方法
			cleanInputText(text) {
				return text
					.toString()
					.replace(/\s/g, '') // 去除空格
					.replace(/[^a-zA-Z0-9]/g, '') // 过滤特殊字符
					.slice(0, 8)
			},

			// 填充输入框逻辑
			fillCodeInputs(cleanText) {
				this.codeParts = Array(8).fill('').map((_, i) =>
					cleanText[i] || ''
				)
				const filledLength = Math.min(cleanText.length, 7)
				this.focusIndex = filledLength

				// 自动聚焦
				this.$nextTick(() => {
					const targetInput = this.$refs[`input${filledLength}`]?.[0]?.$el
					if (targetInput) {
						targetInput.focus()
						targetInput.setSelectionRange(1, 1) // 光标定位末尾
					}
				})
			},

			// 判断是否在微信浏览器
			isInWechat() {
				const ua = navigator.userAgent.toLowerCase()
				return ua.includes('micromessenger')
			},
			viewPrivacyPolicy() {
				// 实现查看隐私政策的功能
			},

			back() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
		},
		// 组件销毁时清除定时器
		beforeDestroy() {
			if (this.timer) {
				clearInterval(this.timer)
			}
		},
		async created() {
			if (this.$store.getters.token) {
				await this.$store.dispatch('user/getReportData') // 初始化 org
			}
			this.from = this.$store.state.user.userInfo.from
			this.reportStatus = this.$store.state.user.userInfo.reportStatus
		},
	};
</script>

<style scoped lang="scss">
	.container {
		box-sizing: border-box;
		min-height: 100vh;
		background-color: #fff;

		.head {
			width: 100%;
			height: 500rpx;
			background-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/login_header_img.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;

			.back-icon {
				position: absolute;
				top: 30rpx;
				left: 30rpx;
			}
		}
	}

	.login-form {
		margin-top: -134rpx;
		padding: 0 28rpx;
		padding-top: 80rpx;
		border-top-left-radius: 36rpx;
		border-top-right-radius: 36rpx;
		background-color: #fff;
	}

	.form {
		display: flex;
		flex-direction: column;
	}

	.input-group {
		margin-bottom: 20px;
	}

	input {
		padding: 10px;
		font-size: 16px;
		border: none;
		border-bottom: 1px solid #ccc;
		outline: none;
	}

	.active-code {
		display: flex;
		flex-direction: column;
		padding-left: 20rpx;
	}

	.uni-input-placeholder {
		font-size: 28rpx;
		color: #A5A5A5;
	}

	.active-code-title {
		font-size: 28rpx;
		color: #A5A5A5;
	}

	.active-code-btn-container {
		margin-top: 20px;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.active-code-btn {
			width: 70rpx;
			height: 70rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 3rpx 6rpx 1rpx #D6D6D6, inset 0rpx 3rpx 6rpx 1rpx #C1F1DA;
			border-radius: 12rpx;
			border: 1rpx solid #26C8AC;
			font-size: 34rpx;
			color: #5A5A5A;
			text-align: center;
			line-height: 70rpx;
			/* 添加输入框样式 */
			padding: 0;
			margin: 0;
			outline: none;
		}
	}

	input:focus {
		border-bottom: 1px solid #1BB394;
	}

	.code-group {
		margin-bottom: 20px;
		position: relative;
	}

	.code-input {
		flex: 1;
		/* 使输入框占据剩余空间 */
		padding: 10px;
		font-size: 16px;
		border: none;
		border-bottom: 1px solid #ccc;
		outline: none;
	}

	.code-input:focus {
		border-bottom: 1px solid #1BB394;
	}

	.code-btn {
		width: 160rpx;
		padding: 14rpx;
		background-color: #26C8AC;
		position: absolute;
		right: 10rpx;
		top: 50%;
		transform: translateY(-65%);
		color: white;
		font-size: 22rpx;
		margin-left: 10px;
		border-radius: 4px;
		cursor: pointer;
		text-align: center;
	}

	.code-btn:disabled {
		background-color: #ccc;
	}

	.login-btn {

		width: 80%;
		height: 45px;
		padding: 12px;
		font-size: 16px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 30px;
		background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
		color: white;
		cursor: pointer;
		margin: 120rpx auto;
	}

	.agreement {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		margin-bottom: 20px;
		margin-top: 70rpx;
	}

	.radio-group {
		display: flex;
		align-items: center;
	}

	.radio-label {
		font-size: 14px;
		color: #333;
	}

	.agreement-text {
		color: red;
		cursor: pointer;
		margin-left: 5px;
	}

	.enter-pay {
		width: 750rpx;
		height: 126rpx;
		position: fixed;
		bottom: 0;
		padding: 0 30rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #F6F7FB;

		.title {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #A5A5A5;

			image {
				width: 70rpx;
				height: 70rpx;
				margin-right: 10rpx;
			}
		}

		.btn {
			width: 145rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			font-weight: bold;
			font-size: 28rpx;
			color: #FFFFFF;
			background: #26C8AC;
			border-radius: 14rpx;
		}
	}
</style>