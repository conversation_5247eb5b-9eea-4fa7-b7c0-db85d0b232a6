{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/index/index.vue?6ece", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/index/index.vue?e1f5", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/index/index.vue?7312", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/index/index.vue?3109", "uni-app:///subpkg/report_all/index/index.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/index/index.vue?1de0", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/index/index.vue?ee09"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "props", "data", "showAnimation", "enter", "percentage", "System_height", "mounted", "console", "methods", "back", "uni", "url", "report", "routergo", "closepage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACoDnnB;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;EACAC;IACA;MACAC;MACAC;MACA;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;EACA;EAEAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACAF;QACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IAEA;IACA;IACA;IACAE;MAEA;MACA;MACA;QACAH;UACAC;QACA;MACA;QACA;QACAJ;MACA;IAEA;IACAO;MACA;MACA;MACAP;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "subpkg/report_all/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg/report_all/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=63a500a1&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=63a500a1&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"63a500a1\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg/report_all/index/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=63a500a1&scoped=true&\"", "var components\ntry {\n  components = {\n    height: function () {\n      return import(\n        /* webpackChunkName: \"components/height/height\" */ \"@/components/height/height.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n    login: function () {\n      return import(\n        /* webpackChunkName: \"components/login/login\" */ \"@/components/login/login.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<height :hg='System_height'></height>\r\n\t\t<view class=\"content_hadrimg\">\r\n\t\t\t<view class=\"nav-title\" :style=\"{'top':System_height+'rpx'}\">\r\n\t\t\t\t<!--  -->\r\n\t\t\t\t<text>入学报告</text>\r\n\t\t\t</view>\r\n\t\t\t<uni-icons type=\"left\" size=\"24\" color=\"#fff\" class=\"back-left\" :style=\"{'top':System_height+'rpx'}\"\r\n\t\t\t\t@tap=\"back\"></uni-icons>\r\n\t\t\t<image src=\"https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/enter.png\"\r\n\t\t\t\tmode=\"\"></image>\r\n\t\t</view>\r\n\t\t<view class=\"btn-ai-bg\">\r\n\t\t\t<view class=\"btn-ai\">\r\n\t\t\t\t<view class=\"num\">可用次数：2</view>\r\n\t\t\t\t<view class=\"bg-container\" @click.stop>\r\n\t\t\t\t\t<view class=\"ai3-bg-container\">\r\n\t\t\t\t\t\t<view class=\"ai3-bg\" :class='{ \"ai3-bg-animation\": showAnimation }'>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"ai2-bg-container\">\r\n\t\t\t\t\t\t<view class=\"ai2-bg\" :class='{ \"ai2-bg-animation\": showAnimation }'>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"ai-bg\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn-report\" @click=\"report\">点击生成报告</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"report-list\">\r\n\t\t\t<view class=\"title\">查看报告</view>\r\n\t\t\t<view class=\"list\">\r\n\t\t\t\t<view class=\"report-title\">\r\n\t\t\t\t\t<view class=\"name\">大学入学发展报告1</view>\r\n\t\t\t\t\t<view class=\"status\">已生成</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"des\">报告包含专业分析、院校分析、学习计划等针对性综\r\n\t\t\t\t\t合分析</view>\r\n\t\t\t\t<view class=\"time-detail\">\r\n\t\t\t\t\t<view class=\"time\">生成时间：2025年8月23日</view>\r\n\t\t\t\t\t<view class=\"detail\">查看详情</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<login :show=\"enter\" @closepage='closepage'></login>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tuserInfo\r\n\t} from \"@/api/public.js\"\r\n\timport {\r\n\t\tcellphone\r\n\t} from \"@/utils/type_height.js\"\r\n\texport default {\r\n\t\tprops: ['asset'],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowAnimation: true,\r\n\t\t\t\tenter: false,\r\n\t\t\t\t// token: uni.getStorageSync('TOKEN') || null,\r\n\t\t\t\t// user: uni.getStorageSync('user') || {},\r\n\t\t\t\tpercentage: 0,\r\n\t\t\t\tSystem_height: cellphone(), //系统高度\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tmounted() {\r\n\t\t\t// this.getUser()\r\n\t\t\tconsole.log('index', uni.getStorageSync('TOKEN'))\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 返回\r\n\t\t\tback() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 点击生成入学报告\r\n\t\t\treport() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/subpkg/report_all/report/report'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// async getUser() {\r\n\t\t\t// \tlet token = uni.getStorageSync('userinfo').token\r\n\t\t\t// \tif (token) {\r\n\t\t\t// \t\tlet user = await userInfo()\r\n\t\t\t// \t\tif (user.code == 1) {\r\n\t\t\t// \t\t\tuni.setStorageSync('user', user.data)\r\n\t\t\t// \t\t\tthis.percentage = (this.user.score / this.user.upgrade) * 100\r\n\t\t\t// \t\t}\r\n\t\t\t// \t}\r\n\r\n\t\t\t// },\r\n\r\n\t\t\t// contact(e) {\r\n\t\t\t// \tconsole.log(e);\r\n\t\t\t// },\r\n\t\t\troutergo(url) {\r\n\r\n\t\t\t\tlet token = uni.getStorageSync('TOKEN')\r\n\t\t\t\tlet user = uni.getStorageSync('user')\r\n\t\t\t\tif (token && user) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.enter = true\r\n\t\t\t\t\tconsole.log('====>', this.enter);\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\tclosepage() {\r\n\t\t\t\tthis.enter = false\r\n\t\t\t\tthis.user = uni.getStorageSync('user')\r\n\t\t\t\tconsole.log(this.user)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\tpage {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: #F6F7FB !important;\r\n\t}\r\n\r\n\t.content {\r\n\t\tposition: relative;\r\n\t\tbackground: #F6F7FB !important;\r\n\r\n\t\t.content_hadrimg {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 490rpx;\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\r\n\t\t\t.nav-title {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: 999;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\t// font-weight: bold;\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\r\n\r\n\r\n\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.back-left {\r\n\t\t\tposition: absolute !important;\r\n\t\t\tleft: 30rpx !important;\r\n\t\t\tz-index: 9999;\r\n\t\t}\r\n\r\n\t\t@keyframes rotate {\r\n\t\t\tfrom {\r\n\t\t\t\ttransform: rotate(0deg);\r\n\t\t\t}\r\n\r\n\t\t\tto {\r\n\t\t\t\ttransform: rotate(360deg);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t@keyframes rotateCounter {\r\n\t\t\tfrom {\r\n\t\t\t\ttransform: rotate(0deg);\r\n\t\t\t}\r\n\r\n\t\t\tto {\r\n\t\t\t\ttransform: rotate(-360deg);\r\n\t\t\t\t/* 负值实现逆时针旋转 */\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.btn-ai-bg {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 370rpx;\r\n\t\t\twidth: 750rpx;\r\n\t\t\t// height: 200rpx;\r\n\t\t\tbackground: #F6F7FB;\r\n\t\t\tpadding-top: 35rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tborder-radius: 30rpx 30rpx 0rpx 0rpx;\r\n\r\n\t\t\t.btn-ai {\r\n\t\t\t\twidth: 690rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t// height: 598rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tpadding: 35rpx 30rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\r\n\t\t\t\t.num {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #989898;\r\n\t\t\t\t\tright: 30rpx;\r\n\t\t\t\t\ttop: 35rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bg-container {\r\n\t\t\t\t\theight: 480rpx;\r\n\t\t\t\t\twidth: 480rpx;\r\n\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t}\r\n\r\n\r\n\r\n\t\t\t\t.ai3-bg-container {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.ai3-bg {\r\n\t\t\t\t\tbackground-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai3.png');\r\n\t\t\t\t\theight: 480rpx;\r\n\t\t\t\t\twidth: 480rpx;\r\n\t\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t\t\tbackground-size: contain;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.ai3-bg-animation {\r\n\t\t\t\t\tanimation: rotate 4s linear infinite;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.ai2-bg-container {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.ai2-bg {\r\n\t\t\t\t\tbackground-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai2.png');\r\n\t\t\t\t\theight: 360rpx;\r\n\t\t\t\t\twidth: 425rpx;\r\n\t\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t\t\tbackground-size: contain;\r\n\t\t\t\t\tz-index: 99;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.ai2-bg-animation {\r\n\t\t\t\t\tanimation: rotateCounter 4.2s linear infinite;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.ai-bg {\r\n\t\t\t\t\tbackground-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai.png');\r\n\t\t\t\t\theight: 236rpx;\r\n\t\t\t\t\twidth: 236rpx;\r\n\t\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t\t\tbackground-size: contain;\r\n\t\t\t\t\tz-index: 100;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn-report {\r\n\t\t\t\t\twidth: 521rpx;\r\n\t\t\t\t\theight: 78rpx;\r\n\t\t\t\t\tline-height: 78rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tmargin: 40rpx auto 0;\r\n\t\t\t\t\tbackground: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);\r\n\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.report-list {\r\n\t\t\tmargin-top: 1050rpx;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tfont-weight: 800;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #060606;\r\n\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 38rpx;\r\n\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\theight: 19rpx;\r\n\t\t\t\t\tbackground: #DBFF9C;\r\n\t\t\t\t\tborder-radius: 39rpx;\r\n\t\t\t\t\tz-index: -1;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.list {\r\n\t\t\t\twidth: 690rpx;\r\n\t\t\t\theight: 245rpx;\r\n\t\t\t\tpadding: 20rpx 20rpx 20rpx 30rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tmargin-top: 25rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tborder-radius: 18rpx;\r\n\t\t\t\tbox-shadow: 0rpx 8rpx 8rpx 1rpx #EBEBEB;\r\n\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-bottom: 100rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.report-title {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\tcolor: #00C2A0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.status {\r\n\t\t\t\t\t\twidth: 124rpx;\r\n\t\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\t\tline-height: 56rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #00C2A0;\r\n\t\t\t\t\t\tbackground: #D6FFEC;\r\n\t\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.des {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #5A5A5A;\r\n\t\t\t\t\tline-height: 1.3;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.time-detail {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.time {\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #818181;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.detail {\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #00C2A0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=63a500a1&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=63a500a1&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557567085\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}