<template>
	<view class="content">
		<height :hg='System_height'></height>
		<view class="content_hadrimg">
			<view class="nav-title" :style="{'top':System_height+'rpx'}">
				<!--  -->
				<text>入学报告</text>
			</view>
			<uni-icons type="left" size="24" color="#fff" class="back-left" :style="{'top':System_height+'rpx'}"
				@tap="back"></uni-icons>
			<image src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/enter.png"
				mode=""></image>
		</view>
		<view class="btn-ai-bg">
			<view class="btn-ai">
				<view class="num">可用次数：2</view>
				<view class="bg-container" @click.stop>
					<view class="ai3-bg-container">
						<view class="ai3-bg" :class='{ "ai3-bg-animation": showAnimation }'>
						</view>
					</view>
					<view class="ai2-bg-container">
						<view class="ai2-bg" :class='{ "ai2-bg-animation": showAnimation }'>
						</view>
					</view>

					<view class="ai-bg">
					</view>
				</view>
				<view class="btn-report" @click="report">点击生成报告</view>
			</view>
		</view>
		<view class="report-list">
			<view class="title">查看报告</view>
			<view class="list">
				<view class="report-title">
					<view class="name">大学入学发展报告1</view>
					<view class="status">已生成</view>
				</view>
				<view class="des">报告包含专业分析、院校分析、学习计划等针对性综
					合分析</view>
				<view class="time-detail">
					<view class="time">生成时间：2025年8月23日</view>
					<view class="detail">查看详情</view>
				</view>
			</view>
		</view>
		<login :show="enter" @closepage='closepage'></login>
	</view>
</template>

<script>
	import {
		userInfo
	} from "@/api/public.js"
	import {
		cellphone
	} from "@/utils/type_height.js"
	export default {
		props: ['asset'],
		data() {
			return {
				showAnimation: true,
				enter: false,
				// token: uni.getStorageSync('TOKEN') || null,
				// user: uni.getStorageSync('user') || {},
				percentage: 0,
				System_height: cellphone(), //系统高度
			};
		},

		mounted() {
			// this.getUser()
			console.log('index', uni.getStorageSync('TOKEN'))
		},

		methods: {
			// 返回
			back() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			// 点击生成入学报告
			report() {
				uni.navigateTo({
					url: '/subpkg/report_all/report/report'
				})
			},
			// async getUser() {
			// 	let token = uni.getStorageSync('userinfo').token
			// 	if (token) {
			// 		let user = await userInfo()
			// 		if (user.code == 1) {
			// 			uni.setStorageSync('user', user.data)
			// 			this.percentage = (this.user.score / this.user.upgrade) * 100
			// 		}
			// 	}

			// },

			// contact(e) {
			// 	console.log(e);
			// },
			routergo(url) {

				let token = uni.getStorageSync('TOKEN')
				let user = uni.getStorageSync('user')
				if (token && user) {
					uni.navigateTo({
						url: url
					})
				} else {
					this.enter = true
					console.log('====>', this.enter);
				}

			},
			closepage() {
				this.enter = false
				this.user = uni.getStorageSync('user')
				console.log(this.user)
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		min-height: 100vh;
		background: #F6F7FB !important;
	}

	.content {
		position: relative;
		background: #F6F7FB !important;

		.content_hadrimg {
			width: 100%;
			height: 490rpx;
			position: absolute;
			top: 0;

			.nav-title {
				width: 100%;
				position: absolute;
				z-index: 999;
				display: flex;
				align-items: center;
				justify-content: center;
				// font-weight: bold;
				font-size: 34rpx;
				color: #FFFFFF;




			}
		}

		.back-left {
			position: absolute !important;
			left: 30rpx !important;
			z-index: 9999;
		}

		@keyframes rotate {
			from {
				transform: rotate(0deg);
			}

			to {
				transform: rotate(360deg);
			}
		}

		@keyframes rotateCounter {
			from {
				transform: rotate(0deg);
			}

			to {
				transform: rotate(-360deg);
				/* 负值实现逆时针旋转 */
			}
		}

		.btn-ai-bg {
			position: absolute;
			top: 370rpx;
			width: 750rpx;
			// height: 200rpx;
			background: #F6F7FB;
			padding-top: 35rpx;
			box-sizing: border-box;
			border-radius: 30rpx 30rpx 0rpx 0rpx;

			.btn-ai {
				width: 690rpx;
				position: relative;
				// height: 598rpx;
				margin: 0 auto;
				padding: 35rpx 30rpx;
				box-sizing: border-box;
				background: #FFFFFF;
				border-radius: 40rpx;

				.num {
					position: absolute;
					font-weight: 400;
					font-size: 26rpx;
					color: #989898;
					right: 30rpx;
					top: 35rpx;
				}

				.bg-container {
					height: 480rpx;
					width: 480rpx;
					margin: 0 auto;
					position: relative;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
				}



				.ai3-bg-container {
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
				}

				.ai3-bg {
					background-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai3.png');
					height: 480rpx;
					width: 480rpx;
					background-repeat: no-repeat;
					background-size: contain;
				}

				.ai3-bg-animation {
					animation: rotate 4s linear infinite;
				}

				.ai2-bg-container {
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
				}

				.ai2-bg {
					background-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai2.png');
					height: 360rpx;
					width: 425rpx;
					background-repeat: no-repeat;
					background-size: contain;
					z-index: 99;
				}

				.ai2-bg-animation {
					animation: rotateCounter 4.2s linear infinite;
				}

				.ai-bg {
					background-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai.png');
					height: 236rpx;
					width: 236rpx;
					background-repeat: no-repeat;
					background-size: contain;
					z-index: 100;
				}

				.btn-report {
					width: 521rpx;
					height: 78rpx;
					line-height: 78rpx;
					text-align: center;
					font-weight: bold;
					font-size: 28rpx;
					color: #FFFFFF;
					margin: 40rpx auto 0;
					background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
					border-radius: 40rpx;
				}
			}
		}

		.report-list {
			margin-top: 1050rpx;
			padding: 0 30rpx;
			box-sizing: border-box;
			position: relative;

			.title {
				position: relative;
				font-weight: 800;
				font-size: 30rpx;
				color: #060606;

				&::before {
					content: "";
					position: absolute;
					top: 38rpx;
					width: 120rpx;
					height: 19rpx;
					background: #DBFF9C;
					border-radius: 39rpx;
					z-index: -1;
				}
			}

			.list {
				width: 690rpx;
				height: 245rpx;
				padding: 20rpx 20rpx 20rpx 30rpx;
				box-sizing: border-box;
				background: #FFFFFF;
				margin-top: 25rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				border-radius: 18rpx;
				box-shadow: 0rpx 8rpx 8rpx 1rpx #EBEBEB;

				&:last-child {
					margin-bottom: 100rpx;
				}

				.report-title {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.name {
						font-weight: bold;
						font-size: 34rpx;
						color: #00C2A0;
					}

					.status {
						width: 124rpx;
						height: 56rpx;
						line-height: 56rpx;
						text-align: center;
						font-size: 28rpx;
						color: #00C2A0;
						background: #D6FFEC;
						border-radius: 30rpx;
					}
				}

				.des {
					font-size: 28rpx;
					color: #5A5A5A;
					line-height: 1.3;
				}

				.time-detail {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.time {
						font-weight: 500;
						font-size: 26rpx;
						color: #818181;
					}

					.detail {
						font-weight: 500;
						font-size: 26rpx;
						color: #00C2A0;
					}
				}
			}
		}
	}
</style>