<view class="content data-v-70684b36"><view class="content_hadrimg data-v-70684b36"><image src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/icon.png" mode class="data-v-70684b36"></image></view><view class="content_main data-v-70684b36"><view class="content_main_box data-v-70684b36"><view class="content_main_box_1 data-v-70684b36"><view data-event-opts="{{[['tap',[['routergo']]]]}}" class="content_main_box_1_left data-v-70684b36" bindtap="__e"><view class="content_main_box_1_left_avatar data-v-70684b36"><view class="content_main_box_1_left_avatar_img data-v-70684b36"><image src="../../static/me/icon1.svg" mode class="data-v-70684b36"></image></view><view class="content_main_box_1_left_img data-v-70684b36"><image src="{{user.avatar||'../../static/avatar.png'}}" mode="aspectFill" class="data-v-70684b36"></image></view></view><view class="content_main_box_1_left_title flexColumn data-v-70684b36"><view class="content_main_box_1_left_title_1 flexc data-v-70684b36"><view class="me-text-beyond content_main_box_1_left_title_1_text data-v-70684b36">{{''+(user.nickname||'请登录')+''}}</view></view></view></view></view></view><view class="card-box data-v-70684b36"><view class="card-box_title data-v-70684b36">我的资产</view><view class="card-box_title_content flexc flexs data-v-70684b36"><view data-event-opts="{{[['tap',[['routergo',['/pages/me_all/integral/integral']]]]]}}" class="card-box_title_content_item data-v-70684b36" bindtap="__e"><view class="card-box_title_content_item_top flexc data-v-70684b36"><image class="img_1 data-v-70684b36" src="../../static/me/icon2.png" mode></image><view class="text_1 data-v-70684b36">蝌蚪币</view></view><view class="card-box_title_content_item_bom data-v-70684b36"><label class="_span data-v-70684b36">{{(asset.point||0)+"个"}}</label><label class="_span data-v-70684b36">未使用</label></view></view><view data-event-opts="{{[['tap',[['routergo',['pages/me_all/coupon/coupon']]]]]}}" class="card-box_title_content_item data-v-70684b36" bindtap="__e"><view class="card-box_title_content_item_top flexc data-v-70684b36"><image class="img_2 data-v-70684b36" src="../../static/me/icon3.svg" mode></image><view class="text_1 data-v-70684b36">优惠券</view></view><view class="card-box_title_content_item_bom data-v-70684b36"><label class="_span data-v-70684b36">{{(asset.couponCnt||0)+"张"}}</label><label class="_span data-v-70684b36">未使用</label></view></view></view></view><block wx:if="{{user.user}}"><view class="card data-v-70684b36"><view class="card_title data-v-70684b36">机构管理</view><view class="card_groud lastflex data-v-70684b36"><view data-event-opts="{{[['tap',[['routergo',['/pages/me_all/order_off/order_off?goback='+2]]]]]}}" class="card_groud_item data-v-70684b36" bindtap="__e"><image src="../../static/me/icon18.png" mode class="data-v-70684b36"></image><view class="data-v-70684b36">扫码核验</view></view><view data-event-opts="{{[['tap',[['routergo',['/pages/me_all/all_orders/all_orders?goback='+2]]]]]}}" class="card_groud_item data-v-70684b36" bindtap="__e"><image src="../../static/me/icon19.png" mode class="data-v-70684b36"></image><view class="data-v-70684b36">全部订单</view></view></view></view></block><block wx:else><view class="card data-v-70684b36"><view class="card_title data-v-70684b36">我的订单</view><view class="card_groud flexw data-v-70684b36"><view data-event-opts="{{[['tap',[['routergo',['/pages/order_all/shipping_address/shipping_address?goback='+2]]]]]}}" class="card_groud_item wrap data-v-70684b36" bindtap="__e"><image src="../../static/me/icon12.png" mode class="data-v-70684b36"></image><view class="data-v-70684b36">已支付</view></view><view data-event-opts="{{[['tap',[['routergo',['/pages/me_all/setmessage/setmessage']]]]]}}" class="card_groud_item wrap data-v-70684b36" bindtap="__e"><image src="../../static/me/icon15.png" mode class="data-v-70684b36"></image><view class="data-v-70684b36">待核验</view></view><view data-event-opts="{{[['tap',[['routergo',['/pages/me_all/messagenotification/messagenotification']]]]]}}" class="card_groud_item wrap data-v-70684b36" bindtap="__e"><image src="../../static/me/icon14.png" mode class="data-v-70684b36"></image><view class="data-v-70684b36">已退款</view></view><image class="border data-v-70684b36" src="../../static/me/icon16.png" mode></image><view data-event-opts="{{[['tap',[['routergo',['/pages/me_all/my_orders/my_orders']]]]]}}" class="card_groud_item wrap data-v-70684b36" bindtap="__e"><image src="../../static/me/icon17.png" mode class="data-v-70684b36"></image><view class="data-v-70684b36">全部订单</view></view></view></view></block><view class="card data-v-70684b36"><view class="card_title data-v-70684b36">我的功能</view><view class="card_groud flexw data-v-70684b36"><view data-event-opts="{{[['tap',[['routergo',['/pages/order_all/shipping_address/shipping_address?goback='+2]]]]]}}" class="card_groud_item data-v-70684b36" bindtap="__e"><image class="svgImg data-v-70684b36" src="../../static/me/icon5.svg" mode></image><view class="data-v-70684b36">我的地址</view></view></view></view></view><login vue-id="c6bcc6e0-1" show="{{enter}}" data-event-opts="{{[['^closepage',[['closepage']]]]}}" bind:closepage="__e" class="data-v-70684b36" bind:__l="__l"></login></view>