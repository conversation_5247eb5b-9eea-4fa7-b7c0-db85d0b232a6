<template>
	<view class="container">
		<view class="value-container">
			<textarea maxlength='100' v-model="feedbackContent" class="value-input" placeholder="请输入文本内容" />
		</view>
		<view class="image-content">
			<u-upload height="200rpx" width="200rpx" uploadText="上传照片" :fileList="fileList" @afterRead="afterRead"
				@delete="deletePic" name="1" multiple :maxCount="6"></u-upload>
			<!-- <image src="@/static/imgs/camera.png" mode=""></image>
				<view class="img">上传图片</view> -->
		</view>
		<view class="btn" @click="submit">提交</view>
	</view>
</template>

<script>
	import COS from 'cos-js-sdk-v5';

	import {
		getCosStsAuth,
		subTaskDone
	} from '@/api/user.js'
	export default {
		data() {
			return {
				feedbackContent: '',
				fileList: [],
				taskMaterialId: '',
				taskId: '',
				feedbackImage: [],
				feedbackAttachment: []
			}
		},
		methods: {
			// 点击提交
			async submit() {
				this.feedbackImage = []
				this.fileList.forEach(item => {
					this.feedbackImage.push(item.url)
				})
				try {
					const {
						data,
						errCode,
						msg
					} = await subTaskDone({
						feedbackContent: this.feedbackContent,
						taskMaterialId: this.taskMaterialId ? this.taskMaterialId : '',
						taskId: this.taskId,
						feedbackImage: this.feedbackImage,
						feedbackAttachment: [],
					})
					if (errCode == 0) {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						})
						uni.navigateBack()
					} else {
						uni.showToast({
							title: msg,
							icon: 'error'
						})
					}
				} catch (e) {

				}
			},
			async urlToFile(url, fileName, mimeType) {
				// 使用 fetch 获取图片的二进制数据
				const response = await fetch(url);
				const blob = await response.blob(); // 转换为 Blob 对象

				// 将 Blob 转换为 File 对象
				return new File([blob], fileName, {
					type: mimeType
				});
			},

			// // 使用示例
			// const imageUrl = 'https://example.com/path/to/image.jpg';
			// urlToFile(imageUrl, 'image.jpg', 'image/jpeg')
			// .then((file) => {
			// 	console.log('文件对象', file);
			// 	// 这里可以调用上传逻辑
			// })
			// .catch((err) => {
			// 	console.error('转换失败', err);
			// });
			//图片上传
			async afterRead(event) {
				let that = this
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				console.log(event)
				this.loading = true;
				for (let i = 0; i < lists.length; i++) {
					let bold = await this.urlToFile(lists[i].url, lists[i].name, lists[i].type)
					return new Promise(async (resolve, reject) => {
						try {
							const result = await getCosStsAuth(lists[i].name)
							if (result.errCode == 0) {

								// 在返回值里取临时密钥信息，上传的文件路径信息
								console.log(result)
								const {
									tmpSecretId,
									tmpSecretKey,
									sessionToken,
									startTime,
									expiredTime,
									bucket,
									region,
									key,
								} = {
									...result.data,
									...result.data.credentials
								}
								// 创建 JS SDK 实例，传入临时密钥参数
								// 其他配置项可参考下方 初始化配置项
								const cos = new COS({
									SecretId: tmpSecretId,
									SecretKey: tmpSecretKey,
									SecurityToken: sessionToken,
									StartTime: startTime,
									ExpiredTime: expiredTime
								});
								// 上传文件
								cos.uploadFile({
									Bucket: bucket,
									Region: region,
									Key: key,
									Body: bold, // 要上传的文件对象。
									onProgress: function(progressData) {
										// console.log('上传进度：', progressData);
									}
								}, function(err, data) {
									console.log('data', data)
									let item = that.fileList[fileListLen]
									that.fileList.splice(fileListLen, 1, Object.assign(item, {
										status: 'success',
										message: '',
										url: data.Location
									}))
									fileListLen++
									if (err) {
										console.log('err')
										reject(err);
									} else {
										console.log('data')
										resolve(data);
									}
								});
							}

						} catch (err) {
							//移除图片
							setTimeout(() => {
								this.fileList.splice(fileListLen, 1)
							}, 1500)

						}
					})
				}
				this.loading = false
			},
			deletePic(event) {
				this.fileList.splice(event.index, 1)
			},
		},
		onLoad(option) {
			this.taskMaterialId = option.id
			this.taskId = option.taskId
		},
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 30px;
		box-sizing: border-box;

		.value-container {
			width: 100%;
			height: 241rpx;
			background: #FFFFFF;
			border-radius: 20rpx;
			padding: 15px;
			box-sizing: border-box;
			margin-bottom: 30px;

			.value-input {

				border: 0;
				border-radius: 8rpx;
				outline: none;

			}

			.value-input:focus {
				border-color: #1BB394;
			}


			::v-deep .checkbox__inner {
				border-radius: 16rpx !important;
			}
		}

		.image-content {
			width: 100%;
			padding: 20rpx;
			background: #FFFFFF;
			border-radius: 20rpx;

			/deep/.u-upload__wrap__preview {
				width: 180rpx !important;
				height: 180rpx !important;
				border-radius: 12rpx !important;
			}

			/deep/.u-icon--right,
			/deep/.u-upload__button__text {
				font-size: 30rpx !important;
				border-radius: 12rpx !important;
			}

			/deep/.uicon-camera-fill {
				font-size: 80rpx !important;
				line-height: 80rpx !important;
			}



		}

		.btn {
			width: 469rpx;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-weight: bold;
			font-size: 30rpx;
			color: #FFFFFF;
			position: absolute;
			bottom: 50rpx;
			left: 50%;
			transform: translateX(-50%);
			background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
			border-radius: 40rpx;
		}

	}
</style>