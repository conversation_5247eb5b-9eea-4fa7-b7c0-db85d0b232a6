<template>
	<picker mode="multiSelector" :range="[provinces, cities, districts]" range-key="name" :value="currentIndex"
		@columnchange="handleChange" @change="handleConfirm">
		<view class="simple-picker">
			{{ currentRegion || '点击选择地区' }}
		</view>
	</picker>
</template>

<script>
	import {
		getPcaJsonData,
	} from "@/api/comm.js"
	export default {
		props: {
			// 接收外部传入的区code
			districtCode: {
				type: String,
				default: ''
			},
			type: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				allRegions: [],
				currentIndex: null // 初始为null表示未选择
			}
		},
		computed: {
			provinces() {
				return this.allRegions
			},
			cities() {
				return this.currentIndex ?
					this.provinces[this.currentIndex[0]]?.children || [] : []
			},
			districts() {
				return this.currentIndex ?
					this.cities[this.currentIndex[1]]?.children || [] : []
			},
			currentRegion() {
				if (!this.currentIndex) return ''
				return [
					this.provinces[this.currentIndex[0]]?.name,
					this.cities[this.currentIndex[1]]?.name,
					this.districts[this.currentIndex[2]]?.name
				].join('/')
			}
		},
		watch: {
			// 监听外部传入的区code变化
			districtCode: {
				immediate: true,
				handler(code) {
					if (code && this.allRegions.length) {
						this.setCurrentByCode(code)
					}
				}
			}
		},
		async mounted() {
			// if (this.type == 1) {
			// 	const res = await getSchoolPcaJsonData()
			// 	this.allRegions = res.data || [];
			// 	// 初始化时如果有默认code
			// 	if (this.districtCode) this.setCurrentByCode(this.districtCode)
			// } else {
			const res = await getPcaJsonData()
			this.allRegions = res.data || [];
			// 初始化时如果有默认code
			if (this.districtCode) this.setCurrentByCode(this.districtCode)
			// }

		},
		methods: {
			// 核心方法：根据区code设置选中状态
			setCurrentByCode(code) {
				console.log(code)
				let targetIndexes = null

				// 三级遍历查找
				this.allRegions.some((province, pIndex) => {
					return province.children?.some((city, cIndex) => {
						return city.children?.some((district, dIndex) => {
							if (district.code === code) {
								targetIndexes = [pIndex, cIndex, dIndex]
								return true
							}
						})
					})
				})

				if (targetIndexes) {
					this.currentIndex = targetIndexes
				}
			},

			handleChange(e) {
				const [column, value] = [e.detail.column, e.detail.value]
				const newIndex = this.currentIndex ? [...this.currentIndex] : [0, 0, 0]

				newIndex[column] = value
				if (column === 0) newIndex.splice(1, 2, 0, 0)
				if (column === 1) newIndex[2] = 0

				this.currentIndex = newIndex
			},

			handleConfirm() {
				if (!this.currentIndex) return
				const district = this.districts[this.currentIndex[2]]
				if (district) {
					this.$emit('update:districtCode', district.code) // 只返回区code
					this.$emit('confirm', district.code)
				}
			}
		}
	}
</script>
<style>
	.simple-picker {
		font-size: 26rpx;
		color: #777777;
		margin-right: 15rpx;
	}
</style>