@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.hade {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: 20rpx 0;
}
.hade .hade_left {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #323232;
}
.hade .hade_left view:nth-child(1) {
  padding: 0 30rpx;
}
.hade .hade_left .selectCity {
  display: flex;
  align-items: center;
  padding: 10rpx 15rpx;
  box-sizing: border-box;
  border-radius: 12rpx;
  border: 1rpx solid #C3C3C3;
}
.hade .hade_right {
  width: 141rpx;
  height: 66rpx;
  background: #F7F7F7;
  border-radius: 33rpx;
  display: flex;
  align-items: center;
  text-align: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #7A7A7A;
  margin-right: 20rpx;
}
.hade .hade_right view:nth-child(1) {
  margin-left: 10rpx;
}
.content {
  width: 704rpx;
  margin: 0 auto;
}
.content .content_hade {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
}
.content .content_hade .content_hade_2 {
  font-size: 28rpx;
  font-weight: 400;
  color: #373737;
  padding: 0 10rpx;
}
.content .map {
  width: 100%;
  height: 399rpx;
}
.content .display {
  height: 67rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 6rpx 6rpx;
}
.content .display .display_text {
  width: 150rpx;
  margin: 0 auto;
}
.content .display .display_text .display_text_content {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  font-size: 24rpx;
  font-weight: 400;
  color: #373737;
}
.content .display .display_text .display_text_content text {
  margin-right: 10rpx;
}
.content .position_box {
  background: #FFFFFF;
  border-radius: 6rpx;
  margin: 20rpx 0;
}
.content .position_box .chosen_position {
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content .position_box .chosen_position .chosen_position_left {
  width: 70%;
}
.content .position_box .chosen_position .chosen_position_left .chosen_position_left_1 {
  font-size: 32rpx;
  font-weight: 600;
  color: #060606;
}
.content .position_box .chosen_position .chosen_position_left .chosen_position_left_2 {
  display: flex;
  font-size: 24rpx;
  font-weight: 400;
  color: #676767;
  margin-top: 15rpx;
}
.content .position_box .chosen_position .chosen_position_left .chosen_position_left_3 {
  display: flex;
  font-size: 24rpx;
  font-weight: 400;
  color: #676767;
  margin-top: 15rpx;
}
.content .position_box .chosen_position .chosen_position_left .chosen_position_left_4 {
  display: flex;
  align-items: center;
  margin-top: 15rpx;
}
.content .position_box .chosen_position .chosen_position_left .chosen_position_left_4 view:nth-child(1) {
  height: 39rpx;
  border-radius: 4rpx;
  border: 1rpx solid #05B6F6;
  font-size: 24rpx;
  font-weight: 400;
  color: #05B6F6;
  text-align: center;
  box-sizing: border-box;
  padding: 0 10rpx;
  margin: 0 10rpx;
  line-height: 37rpx;
}
.content .position_box .chosen_position .chosen_position_left .chosen_position_left_4 view:nth-child(2) {
  height: 39rpx;
  background: #05B6F6;
  border-radius: 4rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #F3F8FF;
  margin-right: 10rpx;
  padding: 0 10rpx;
  line-height: 39rpx;
}
.content .position_box .chosen_position .chosen_position_left .chosen_position_left_4 view:nth-child(3) {
  height: 39rpx;
  background: #05B6F6;
  border-radius: 4rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #F3F8FF;
  margin-right: 10rpx;
  padding: 0 10rpx;
  line-height: 39rpx;
}
.content .position_box .chosen_position .chosen_position_left .chosen_position_left_4 view:nth-child(4) {
  height: 39rpx;
  background: #05B6F6;
  border-radius: 4rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #F3F8FF;
  padding: 0 10rpx;
  line-height: 39rpx;
}
.content .position_box .chosen_position .chosen_position_right {
  width: 180rpx;
  text-align: center;
  border-left: 1rpx solid #F0F0F0;
  padding-left: 20rpx;
}
.content .position_box .chosen_position .chosen_position_right .chosen_position_right_1 {
  font-size: 26rpx;
  font-weight: 400;
  color: #05B6F6;
  padding: 20rpx 0;
}
.content .position_box .chosen_position .chosen_position_right .chosen_position_right_2 {
  font-size: 24rpx;
  font-weight: 400;
  color: #666666;
  margin-bottom: 20rpx;
}
.content .position_box .chosen_position .chosen_position_right .chosen_position_right_3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content .position_box .chosen_position .chosen_position_right .chosen_position_right_3 .chosen_position_right_3_1 {
  width: 66rpx;
  height: 66rpx;
}
.content .position_box .chosen_position .chosen_position_right .chosen_position_right_3 .chosen_position_right_3_2 {
  width: 66rpx;
  height: 66rpx;
}

