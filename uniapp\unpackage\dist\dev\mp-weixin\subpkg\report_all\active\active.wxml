<view class="container data-v-7e26637b"><view class="head data-v-7e26637b"><uni-icons class="back-icon data-v-7e26637b" vue-id="46a68ceb-1" type="left" size="20" color="#fff" data-event-opts="{{[['^tap',[['back']]]]}}" bind:tap="__e" bind:__l="__l"></uni-icons></view><view class="login-form data-v-7e26637b"><view class="active-code data-v-7e26637b"><text class="active-code-title data-v-7e26637b">激活码</text><view class="active-code-btn-container data-v-7e26637b"><block wx:for="{{8}}" wx:for-item="item" wx:for-index="index" wx:key="index"><input class="active-code-btn data-v-7e26637b" id="{{'input'+index}}" type="text" maxlength="1" focus="{{focusIndex===index}}" data-event-opts="{{[['input',[['__set_model',['$0',index,'$event',['trim']],['codeParts']],['handleInput',[index,'$event']]]],['keydown',[['handleDelete',[index,'$event']]]],['paste',[['handleH5Paste',['$event']]]],['blur',[['$forceUpdate']]]]}}" value="{{codeParts[index]}}" bindinput="__e" bindkeydown="__e" bindpaste="__e" bindblur="__e"/></block></view></view><button class="login-btn data-v-7e26637b" loading="{{loading}}" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">立即激活</button></view><view class="enter-pay data-v-7e26637b"><view class="title data-v-7e26637b"><image src="/static/car.png" mode class="data-v-7e26637b"></image><text class="data-v-7e26637b">如无激活码，请购买大学入学发展报告</text></view><view class="btn data-v-7e26637b">去购买</view></view></view>