<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<script>
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
				CSS.supports('top: constant(a)'))
			document.write(
				'<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
				(coverSupport ? ', viewport-fit=cover' : '') + '" />')
		</script>
		<title></title>
		<!--preload-links-->
		<!--app-context-->
	</head>
	<style>

	</style>
	<body>
		<div id="app"><!--app-html--></div>
		<script type="module" src="/main.js"></script>
	</body>
</html>