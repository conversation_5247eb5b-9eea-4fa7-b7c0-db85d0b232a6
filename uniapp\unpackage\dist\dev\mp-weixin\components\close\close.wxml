<view><view class="box"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="box_content" bindtap="__e"><view class="box_img"><image src="/static/Project_drawing 27.png" mode></image><text>{{count||0}}</text></view><view class="box_price"><text>总额</text><text style="font-size:34rpx;">¥</text><text>{{''+(totalPrice||0)}}</text></view></view><view data-event-opts="{{[['tap',[['goback']]]]}}" class="box_close" bindtap="__e">去结算</view></view><u-popup vue-id="7593ff10-1" show="{{show}}" round="{{10}}" zIndex="10071" mode="bottom" data-event-opts="{{[['^close',[['e1']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="upcart"><view class="upcart_top"><view class="upcart_top_left">已选商品</view><view data-event-opts="{{[['tap',[['remove']]]]}}" class="upcart_top_right" bindtap="__e"><u-icon vue-id="{{('7593ff10-2')+','+('7593ff10-1')}}" name="trash" color="#676767 " size="30" bind:__l="__l"></u-icon><text>清空</text></view></view><view class="upcart_order"><block wx:if="{{list<1}}"><view style="margin-top:150rpx;"><u-empty vue-id="{{('7593ff10-3')+','+('7593ff10-1')}}" mode="data" iconSize="{{150}}" textSize="{{24}}" text="购物车空空如也" icon bind:__l="__l"></u-empty></view></block><scroll-view style="height:570rpx;" scroll-y="true"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="*this"><view class="upcart_order_content"><view class="upcart_order_content_img"><image src="{{item.course_cover}}" mode></image></view><view class="upcart_order_content_title"><view>{{item.name}}</view><view><text class="money">{{"￥"+item.checkout_price}}</text><view><u-number-box bind:change="__e" bind:input="__e" vue-id="{{('7593ff10-4-'+index)+','+('7593ff10-1')}}" value="{{value[index]}}" data-event-opts="{{[['^change',[['valChange']]],['^input',[['__set_model',['$0',index,'$event',[]],['value']]]]]}}" bind:__l="__l" vue-slots="{{['minus','input','plus']}}"><view class="minus" slot="minus" data-event-opts="{{[['tap',[['reduce',['$0',index,'$1'],[[['list','',index]],[['list','',index,'min_quantity']]]]]]]}}" bindtap="__e"><u-icon vue-id="{{('7593ff10-5-'+index)+','+('7593ff10-4-'+index)}}" name="minus" labelSize="20px" color="#05B6F6" size="24" bind:__l="__l"></u-icon></view><text class="input" style="width:50px;text-align:center;" slot="input">{{value[index]||item.quantity}}</text><view class="plus" slot="plus" data-event-opts="{{[['tap',[['add',['$0'],[[['list','',index]]]]]]]}}" bindtap="__e"><u-icon vue-id="{{('7593ff10-6-'+index)+','+('7593ff10-4-'+index)}}" name="plus" labelSize="20px" color="#FFFFFF" size="24" bind:__l="__l"></u-icon></view></u-number-box></view></view></view></view></block><view style="height:80rpx;"></view></scroll-view></view></view></u-popup></view>