<template>
	<view class="container">

		<view class="top-banner">
			<image :src="detail.course_cover" mode="widthFix"></image>
		</view>
		<view class="title">
			<text class="title-name">{{detail.name}}</text>
			<view class="center">
				<text>开课时间：{{detail.start_time ||''}}-{{detail.end_time ||''}}</text>
				<text class="u-border-left">课时：{{detail.hours ||'-'}}节</text>
			</view>
			<text class="money  u-border-bottom">￥{{detail.checkout_price}}</text>
		</view>
		<view class=" tabs">
			<view class="tabsBox">
				<u-tabs :list="type==1 ? list : lists" lineWidth="50" lineHeight="10"
					:lineColor="`url(https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/wan.png) 100% 100%`"
					:activeStyle="{
				        color: '#26C8AC',
				        fontWeight: 'bold',
				        transform: 'scale(1.1)'
				    }" :inactiveStyle="{
						fontSize:'28rpx',
				        color: '#777777',
						fontWeight: 'bold',
				        transform: 'scale(1)'
				    }" itemStyle=" height: 82rpx;" @click="chooseItem">
				</u-tabs>
			</view>

			<view class="course-detail" v-show="currentIndex==0">
				<template v-if="detail_list.length>1">
					<view class="dir-tab">
						<text v-for="(item,index) in detail_list" :key="index" :class="listIndex==index ? 'sel' : ''"
							@click="checkDetail(item.detail,index)">{{item.cate}}</text>
					</view>
				</template>
				<view class="copy-content">

					<rich-text :nodes="detailImg"></rich-text>
				</view>
			</view>

			<view class="course-dir padding-dir" v-show="currentIndex==1">
				<template v-if="listenList.length">
					<view class="dir-tab dir-tab-index">
						<text v-for="(item,index) in listenList" :key="index" :class="listenIndex==index ? 'sel' : ''"
							@click="listenClick(item,index)">{{item.cate}}</text>
					</view>
					<!-- 直播信息 -->
					<view class="live-info">
						<view class="live-title">
							<text class="green-block"></text>
							<text>试听课</text>
						</view>
						<!-- <view class="live-name">
						精讲最新时政题型总结
					</view> -->
						<view class="live-date" v-for="(item,index) in videoList" :key="index">
							<view class="live-name">
								<text>{{item.title}}</text>
							</view>
							<view class="btn-enter" @click="studyClick(item)">
								进入试听
							</view>
						</view>

					</view>
				</template>

				<!-- 课程列表 -->
				<view class="dir-tab m-t-30 dir-tab-index">
					<text :class="indexActive==index ? 'sel' : ''" v-for="(item,index) in index_list" :key="index"
						@click="listClick(item,index)">{{item.cate}}</text>
				</view>
				<view class="dir-tabs">
					<u-tabs :list="moduleDetail" lineWidth="40" lineHeight="10"
						:lineColor="`url(https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/wan.png) 100% 100%`"
						:activeStyle="{
					        color: '#26C8AC',
					        fontWeight: 'bold',
					        transform: 'scale(1.1)'
					    }" :inactiveStyle="{
							fontSize:'26rpx',
					        color: '#777777',
							fontWeight: 'bold',
					        transform: 'scale(1)'
					    }" itemStyle="box-sizing: border-box; padding-right: 20rpx; height: 82rpx;" :current="activeTabIndex"
						@click="chooseDirItem">
					</u-tabs>
				</view>
				<view class="course-container">
					<uni-collapse v-for="(item,index) in classList" :key="index">
						<view class="course-radius">
							<uni-collapse-item :title="item.name" :show-animation="true">
								<view class="content" v-for="(items,k) in item.indexes" :key="k">
									<text class="text">{{items.name}}</text>
								</view>
							</uni-collapse-item>
						</view>

					</uni-collapse>
				</view>
			</view>
		</view>
		<view class="bottom-opt">
			<view class="left">
				<text class="price">￥{{detail.checkout_price}}</text>
			</view>
			<view class="btnBox" v-if="!user.user">
				<view class="car" @click="carClick">
					加入购物车
				</view>
				<!-- <view class="btn" @click="payClick">
					加入购物车
				</view> -->
			</view>

		</view>

		<u-popup :show="showCoupon" mode="bottom" @close="close">
			<view class="coupon-conainer">
				<view class="title-img">
					<image src="@/static/good.png" mode=""></image>
					<text>加入购物车成功</text>
				</view>
				<view class="btn-bottom">
					<view class="btn" @click="closeMask">继续逛</view>
					<view class="btn" @click="goToCart">去购物车</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	// import store from '@/store/user/index.js'
	import {
		// getProductDetail,
		getStuDetail
	} from '@/api/user.js'
	import {
		getProductDetail
	} from '@/api/comm.js'
	// 
	// import {
	// 	getToken,
	// } from "@/utils/storage.js";
	import {
		setupWechatShare
	} from '@/utils/share.js'
	export default {
		data() {
			return {
				showCoupon: false,
				showMask: false,
				show: false,
				currentIndex: 0,
				dirIndex: 0,
				height: 0,
				turnOver: false, //折叠动画
				list: [{
						name: '课程详情'
					},
					{
						name: '课程目录'
					}
				],
				lists: [{
						name: '课程详情'
					},
					{
						name: '课程目录'
					}
				],
				dirList: [{
						name: '导学'
					},
					{
						name: '夯实基础'
					},
					{
						name: "强化提升"
					},
					{
						name: '冲刺阶段'
					},
					{
						name: '点题阶段'
					},
					{
						name: "复试阶段"
					}

				],
				listIndex: 0, //课程详情目录激活索引
				detail_list: [], //课程详情
				detailImg: '', //课程详情图片

				goodLists: [], //本地存储的产品
				tempList: [],
				animation: {},
				animationData: {},
				id: '',
				detail: {}, //详情
				index_list: [], //课程目录
				indexActive: 0, //课程目录激活索引
				moduleDetail: [], //课程目录模块内容
				classList: [], //课程章节
				promo_material: [], //朋友圈图片
				comm_material: [], //社群图片
				type: 0, //0代表公开，1代表品牌商
				userInfo: {},
				activeTabIndex: 0, // 新增变量，用于存储当前激活的 tab 索引
				listenList: [], //未登录和学生端试听
				listenIndex: 0,
				videoList: [],
				user: uni.getStorageSync('user') || {},

			};
		},
		onLoad(options) {
			this.id = options.id
			this.getStudentsDetail()
		},
		onShow() {

		},
		methods: {
			goToCart() {
				this.showCoupon = false
				uni.reLaunch({
					url: '/pages/order/order?showCart=true'
				})
			},
			// 关闭遮罩层
			closeMask() {
				this.showCoupon = false
			},
			// 点击分享
			shareClick() {
				this.showMask = true
			},
			// async initData() {
			// 	if (getToken() && this.userInfo.role === 'user') {
			// 		await this.getDetail();
			// 		this.type = 1;
			// 	} else {
			// 		await this.getStudentsDetail();
			// 		this.type = 0;
			// 	}
			// },
			async initShare(name) {
				try {
					await setupWechatShare(name); // 传入当前页面标题
					console.log('分享配置完成');
				} catch (e) {
					console.error('分享配置失败:', e);
				}
			},
			//切换试听科目
			listenClick(item, index) {
				this.listenIndex = index
				this.videoList = item.videos
			},
			// 去试听
			studyClick(item) {
				let cate = this.listenList[this.listenIndex].name
				// this.$store.commit('user/getListen', {
				// 	cate: cate,
				// 	title: item.title,
				// 	url: item.url
				// });
				uni.navigateTo({
					url: `/subpkg/study/study`
				})
			},
			// 切换课程目录
			listClick(item, index) {
				this.indexActive = index
				this.moduleDetail = item.periods
				this.classList = this.moduleDetail.length ? this.moduleDetail[0].chapters : [],
					this.activeTabIndex = 0; // 重置激活的 tab 索引为 0

			},
			// 切换课程详情目录
			checkDetail(item, index) {
				this.detailImg = item
				this.listIndex = index
			},
			async getStudentsDetail() {
				try {
					const {
						data,
						errCode,
						msg
					} = await getProductDetail(this.id)
					if (errCode == 0) {
						this.detail = data
						this.index_list = data.index_list
						this.detail_list = data.detail_list
						this.detailImg = this.detail_list.length ? this.detail_list[0].detail : ''
						this.moduleDetail = this.index_list.length ? this.index_list[0].periods : []
						this.classList = this.moduleDetail.length ? this.moduleDetail[0].chapters : []
						if (data.free_trial.length) {
							this.listenList = data.free_trial.filter(item => item.videos.length > 0)
							this.videoList = this.listenList[0].videos
						} else {
							this.listenList = []
						}
					} else {
						uni.tip(msg)
					}
				} catch (e) {
					//TODO handle the exception
				}
			},
			// 点击立即支付
			payClick() {
				if (!getToken()) {
					// this.$store.commit('user/setRedirectPath', '/subpkg/product_detail/product_detail?id=' + this.id)
					uni.navigateTo({
						url: '/subpkg/login/login'
					})

					return
				}
				// 
				// this.$store.commit('user/setPayDetail', this.detail)
				uni.setStorageSync('payDetail', JSON.stringify(this.detail));
				if (this.userInfo.role === 'user') {
					uni.navigateTo({
						url: '/subpkg/confirm_order/confirm_order?item=' + JSON.stringify(this.detail)
					})
				} else {
					uni.navigateTo({
						url: '/subpkg/stu_order/stu_order'
					})
				}

			},
			// 点击加入购物车
			carClick() {
				const existingItemIndex = this.goodLists.findIndex(cartItem => cartItem.id === this.detail.id);
				if (existingItemIndex !== -1) {
					// 商品已存在，增加数量
					this.goodLists[existingItemIndex].quantity += this.detail.min_quantity;
				} else {
					// 商品不存在，添加新商品
					const newItem = {
						...this.detail,
						quantity: this.detail.min_quantity
					};
					this.goodLists.push(newItem);
				}

				// 存储到本地
				uni.setStorageSync('cartItems', this.goodLists);
				setTimeout(() => {
					this.showCoupon = true
				}, 1000)

			},
			// 下载图片
			download(url, name) {
				// 创建隐藏链接
				const link = document.createElement('a')
				link.href = url // 替换为实际URL
				link.download = name + '.zip'
				link.style.display = 'none'

				// 触发下载
				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)

				// 显示反馈
				uni.showToast({
					title: '开始下载',
					icon: 'success'
				})
			},
			// 复制朋友圈文本
			async handleCopy(e) {
				try {
					// 获取 HTML 内容
					const htmlContent = this.getHtmlContent(e);
					// 获取纯文本备用
					const plainText = this.getPlainText(e);
					// 现代浏览器方案
					await this.modernCopy(htmlContent, plainText);
					this.showToast('复制成功');
				} catch (error) {
					// 传统降级方案
					this.legacyCopy(htmlContent, plainText);
					this.showToast('复制成功');
				}
			},

			// 获取 DOM 的 HTML
			getHtmlContent(e) {

				return this.$refs.promo_text.$el.innerHTML;
			},

			// 获取纯文本内容
			getPlainText() {
				return this.$refs.promo_text.$el.innerText;

			},
			// 复制社群文本
			async handleCopys(e) {
				try {
					// 获取 HTML 内容
					const htmlContent = this.getHtmlContents(e);
					// 获取纯文本备用
					const plainText = this.getPlainTexts(e);
					// 现代浏览器方案
					await this.modernCopy(htmlContent, plainText);
					this.showToast('复制成功');
				} catch (error) {
					// 传统降级方案
					this.legacyCopy(htmlContent, plainText);
					this.showToast('复制成功');
				}
			},

			// 获取 DOM 的 HTML
			getHtmlContents(e) {

				return this.$refs.comm_text.$el.innerHTML;

			},

			// 获取纯文本内容
			getPlainTexts() {

				return this.$refs.comm_text.$el.innerText;
			},
			// 现代浏览器 API
			async modernCopy(html, text) {
				const htmlBlob = new Blob([html], {
					type: 'text/html'
				});
				const textBlob = new Blob([text], {
					type: 'text/plain'
				});

				const data = [
					new ClipboardItem({
						'text/html': htmlBlob,
						'text/plain': textBlob
					})
				];

				await navigator.clipboard.write(data);
			},

			// 传统浏览器降级方案
			legacyCopy(html, text) {
				const container = document.createElement('div');
				container.style.position = 'fixed';
				container.style.left = '-9999px';
				container.innerHTML = html;

				document.body.appendChild(container);

				const range = document.createRange();
				range.selectNode(container);

				const selection = window.getSelection();
				selection.removeAllRanges();
				selection.addRange(range);

				document.execCommand('copy');

				selection.removeAllRanges();
				document.body.removeChild(container);
			},

			// 显示提示
			showToast(message) {
				uni.showToast({
					title: message,
					icon: 'none',
					duration: 2000
				});
			},
			async getDetail() {
				try {
					const {
						data,
						errCode,
						msg
					} = await getProductDetail(this.id)
					if (errCode == 0) {
						this.detail = data
						this.index_list = data.index_list
						this.detail_list = data.detail_list
						this.detailImg = this.detail_list.length ? this.detail_list[0].detail : ''
						this.moduleDetail = this.index_list.length ? this.index_list[0].periods : []
						this.classList = this.moduleDetail.length ? this.moduleDetail[0].chapters : []


					} else {
						uni.tip(msg)
					}
				} catch (e) {
					//TODO handle the exception
				}
			},
			back() {
				uni.navigateBack()
			},
			open() {
				uni.navigateTo({
					url: '/subpkg/open_lesson/open_lesson'
				})
			},
			study() {
				uni.navigateTo({
					url: "/choosecoursepkg/study/study"
				})
			},
			pay() {
				uni.navigateTo({
					url: ""
				})
			},
			chooseItem(item) {
				this.currentIndex = item.index
				if (item.index == 1) {
					//重新计算 子tab
					this.tempList = [...this.dirList]
					this.moduleDetail = this.index_list.length ? this.index_list[0].periods : [],
						this.classList = this.moduleDetail.length ? this.moduleDetail[0].chapters : []
				}

			},
			chooseDirItem(item) {
				this.classList = item.chapters
				this.dirIndex = item.index
				this.activeTabIndex = item.index
				console.log(this.activeTabIndex)
			},
			rotate() {
				if (!this.turnOver) {
					this.animation.rotate(-180).step()
					this.show = false;
				} else {
					this.animation.rotate(0).step()
					this.show = true;
				}
				this.animationData = this.animation.export()
				this.turnOver = !this.turnOver
			}

		},
		created() {
			var animation = uni.createAnimation({
				duration: 500,
				timingFunction: 'ease',
			})
			this.animation = animation
			// this.$store.dispatch('user/getReportData')
			// 加载本地存储的购物车数据
			const cartData = uni.getStorageSync('cartItems');
			if (cartData) {
				this.goodLists = cartData;
			}
		}
	}
</script>

<style lang="scss">
	/* 颜色变量 */
	$primary-green: #07C160; // 主绿色
	$price-red: #FF3B30; // 价格红
	$dark-text: #333333; // 主文字
	$light-text: #666666; // 次要文字
	$border-color: #E5E5E5; // 分割线颜色

	.container {
		.course-detail {
			padding: 0 30rpx;
			box-sizing: border-box;
		}

		::v-deep .u-popup__content {
			border-radius: 16rpx 16rpx 0rpx 0rpx;
		}

		.coupon-conainer {
			position: relative;
			height: 400rpx;
		}

		@keyframes moveUp {
			0% {
				transform: translate(-50%, 0);
			}

			50% {
				transform: translate(-50%, -220rpx);
			}

			100% {
				transform: translate(-50%, -100rpx);
				/* 上移80px */
			}
		}

		.title-img {
			position: absolute;
			/* 初始位置 */
			top: 250rpx;
			/* 可根据需要调整 */
			left: 50%;
			transform: translateX(-50%);

			/* 动画设置 */
			animation: moveUp 1s ease-in-out forwards;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			color: #4A4A4C;

			image {
				width: 50rpx;
				height: 50rpx;
				margin-right: 20rpx;
			}
		}

		.btn-bottom {
			position: fixed;
			bottom: 10rpx;
			width: 690rpx;
			left: 50%;
			transform: translateX(-50%);
			display: flex;
			align-items: center;

			.btn {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				font-size: 30rpx;
				color: #fff;
				background-color: #26C8AC;
				border-radius: 0 36rpx 36rpx 0;

				&:first-child {
					color: #26C8AC;
					background-color: #E7FBF7;
					border-radius: 36rpx 0 0 36rpx;
				}
			}
		}

		.custom-navbar {
			height: 88rpx;
			padding: 0 32rpx;
			display: flex;
			align-items: center;
			background-color: #fff;
			border-bottom: 1rpx solid #E5E5E5;

			.nav-icon {
				width: 48rpx;
				height: 48rpx;
			}

			.nav-title {
				flex: 1;
				text-align: center;
				font-size: 36rpx;
				color: #333333;
				font-weight: 500;
			}
		}

		.tabsBox {
			background-color: #fff;
			margin-bottom: 20rpx;
		}

		::v-deep .u-border-left {
			height: 25rpx !important;
			line-height: 20rpx;
		}

		::v-deep .u-border-bottom {
			border-bottom: none !important;
		}

		/deep/ .u-tabs__wrapper__nav__line {
			width: 40px !important;
			height: 10px !important;
		}

		::v-deep .u-tabs__wrapper__nav__item {
			padding: 0 !important;
			width: 25% !important;
			text-align: center !important;
		}

		::v-deep .uni-collapse-item__title.uni-collapse-item-border {
			border-bottom: none !important;
		}

		::v-deep .uni-collapse {
			border-radius: 20rpx !important;
		}

		::v-deep .uni-collapse-item__wrap-content.uni-collapse-item--border {
			border: none !important;
		}

		::v-deep .uni-collapse-item__wrap-content {
			.content {
				font-size: 26rpx;
				color: #4A4A4C;
				padding: 25rpx 35rpx;
				box-sizing: border-box;
				border-top: 1rpx solid #B9B9B9;
			}
		}

		::v-deep .uni-collapse-item__title {

			border-radius: 24rpx !important;
		}

		::v-deep .uni-collapse-item__title-arrow {
			width: 30rpx;
			height: 30rpx;
			border-radius: 50%;
			background: #26C8AC;

			.uni-icons {
				color: #fff !important;
			}
		}

		::v-deep.uni-collapse-item__title.is-open {
			border-radius: 24rpx 24rpx 0 0 !important;
		}

		::v-deep .uni-collapse-item__title-text {
			font-weight: bold;
			font-size: 28rpx;
			color: #4A4A4C;
			margin-left: 30rpx;

		}

		::v-deep .uni-collapse-item__title-box {
			position: relative;

			&:before {
				content: '';
				position: absolute;
				width: 14rpx;
				height: 28rpx;
				top: 50%;
				transform: translateY(-50%);
				left: 30rpx;
				background: #26C8AC;
				z-index: 999;
				border-radius: 0rpx 8rpx 0rpx 8rpx;

			}
		}

		view {
			box-sizing: border-box;
		}

		.m-t-30 {
			margin-top: 30rpx;
		}

		padding-bottom: 120rpx;

		.h1-title {
			width: 100%;
			padding: 26rpx 0;
			color: #201E2E;
			text-align: left;
			font-weight: bold;
			font-size: 32rpx;
			text-align: left;
		}

		background-color: #F6F7FB;
		min-height: 100vh;
		// padding-top: 20rpx;

		.top-banner {
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 625rpx;
			}
		}

		.title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-direction: column;
			background-color: #fff;
			padding: 0 30rpx;
			padding-top: 22rpx;
			width: 100%;
			text-align: left;
			color: #060606;
			font-size: 32rpx;
			font-weight: bold;

			.title-name {
				width: 100%;
			}

			.center {
				font-weight: normal;
				margin-top: 12rpx;
				color: #777777;
				font-size: 26rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				width: 100%;
				text-align: left;

				text {
					&:first-child {
						padding-right: 13rpx;
					}

					&:last-child {
						padding-left: 13rpx;

					}
				}
			}

			.money {
				margin-top: 25rpx;
				display: inline-block;
				color: #E16965;
				font-size: 34rpx;
				font-weight: bold;
				width: 100%;
				text-align: left;
				padding-bottom: 22rpx;
				font-weight: 400 !important;
			}

			.last {
				background-color: #fffae8;
				padding: 15rpx;
				border-radius: 20rpx;
				font-size: 24rpx;
				margin-bottom: 20rpx;
				color: #706E6E;
				font-weight: bold;

			}

			.extra-info {
				width: 100%;
				font-size: 26rpx;
				color: #706E6E;
				font-weight: normal;
				padding: 26rpx 0;
				display: flex;
				align-items: center;
				justify-content: flex-start;

				text {
					margin-right: 8rpx;
				}
			}

			// $main-color: #009c7b;
			// $container-bg-color: #F6F7FB;

			.course-list {
				margin-bottom: 22rpx;
				background-color: #fff;
				border-radius: 16rpx;
				width: 100%;

				.mid {
					margin-top: 6rpx;
					display: flex;
					align-items: center;
					justify-content: flex-start;

					.tag {
						padding: 0 8rpx;
						height: 48rpx;
						background-color: #EEFAF6;
						color: #009c7b;
						line-height: 48rpx;
						font-size: 22rpx;
						font-weight: bold;
						border-radius: 10rpx;
					}

					.date {
						margin-left: 8rpx;
						font-size: 22rpx;
						color: #A4A4A4;
						font-weight: 400;
					}
				}

				.bottom {
					margin-top: 20rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.teacher-list {
						padding-left: 36rpx;
						display: flex;
						align-items: center;
						justify-content: flex-start;
						padding-bottom: 20rpx;

						.teacher-info {
							margin-right: 24rpx;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							font-size: 22rpx;
							color: #818181;

							.avatar {
								width: 60rpx;
								height: 60rpx;
								border-radius: 100%;
								margin-bottom: 2rpx;
							}

							text {
								font-weight: 400;
							}

						}
					}

					.course-money {
						color: #E16965;
						font-size: 38rpx;
						margin-right: 34rpx;
					}
				}

			}


			.teachers {
				width: 100%;
				padding-bottom: 26rpx;

				.all-teacher-info {
					width: 100%;
					white-space: nowrap;

					.teacher-item {
						width: 30%;
						display: inline-block;
						align-items: center;
						justify-content: space-between;
						flex-direction: column;

						.top-info {
							display: flex;
							align-items: center;
							justify-content: center;

							image {
								width: 80rpx;
								height: 80rpx;
								border-radius: 50%;
								margin-right: 6rpx;
							}

							text {
								font-weight: 400;
							}

							.teacher-extra {
								display: flex;
								flex-direction: column;
								align-items: center;
								justify-content: space-between;

								text {
									font-size: 22rpx;

									&:first-child {
										color: #818181;

									}

									&:last-child {
										color: #201E2E;
									}
								}
							}
						}

						.bottom-info {
							margin-top: 6rpx;
							display: flex;
							align-items: center;
							justify-content: space-between;
							flex-direction: column;

							text {
								color: #201E2E;
								font-size: 16rpx;
							}
						}
					}
				}
			}
		}

		.tabs {
			width: 100%;
			margin-top: 16rpx;

		}

		.course-dir {
			background-color: #F6F7FB;
			padding: 0 30rpx;

			.dir-tab {
				display: flex;
				align-items: center;
				justify-content: flex-start;

				text {
					width: 110rpx;
					height: 60rpx;
					border-radius: 12rpx;
					text-align: center;
					line-height: 60rpx;
					font-size: 26rpx;
					font-weight: bold;
					color: #201E2E;
					background-color: #fff;
					margin-right: 20rpx;

				}

				.sel {
					background: #26C8AC;
					color: #fff;
				}
			}

			.dir-tab-index {
				margin-bottom: 0;
				padding: 0 30rpx;
				box-sizing: border-box;
			}

			.live-info {
				margin-top: 30rpx;
				background-color: #fff;
				border-radius: 20rpx;
				padding: 30rpx 28rpx;

				.live-title {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					color: #0A0A0A;
					font-size: 30rpx;
					font-weight: bold;

					.green-block {
						width: 14rpx;
						height: 28rpx;
						line-height: 28rpx;
						background: #01997A;
						border-radius: 0rpx 8rpx 0rpx 8rpx;
						margin-right: 6rpx;
					}

				}

				.live-name {
					color: #4A4A4C;
					font-size: 30rpx;
					text-align: left;
					// margin-top: 24rpx;
				}

				.live-date {
					margin-top: 20rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.date {
						color: #A2A2A2;
						font-size: 24rpx;

						text {
							margin-right: 16rpx;

						}
					}

					.btn-enter {
						width: 129rpx;
						height: 44rpx;
						background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
						border-radius: 28rpx;
						line-height: 44rpx;
						text-align: center;
						color: #fff;
						font-size: 24rpx;
						font-weight: bold;
					}

				}
			}

			.dir-tabs {

				// margin-top: 32rpx;
				/deep/ .u-tabs__wrapper__nav__line {
					width: 40px !important;
					height: 10px !important;
				}

				::v-deep .u-tabs__wrapper__nav__item {
					padding: 0 !important;
					width: 20% !important;
					text-align: center !important;
				}

			}

			.course-container {
				width: 690rpx;
				margin: 30rpx auto;
				background-color: #fff;
				// border-radius: 20rpx;

				.title-name {

					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 30rpx 28rpx;

					.left {
						color: #4A4A4C;
						font-size: 28rpx;
						font-weight: bold;

						.green-block {
							display: inline-block;
							width: 14rpx;
							height: 28rpx;
							line-height: 28rpx;
							background: #01997A;
							border-radius: 0rpx 8rpx 0rpx 8rpx;
							margin-right: 6rpx;
						}
					}

					.expand {
						width: 30rpx;
						height: 30rpx;
						background: #01997A;
						border-radius: 50%;
						display: flex;
						align-items: center;
						transition: transform 0.5s ease;
						justify-content: center;

						.rotate {
							transform: rotate(180deg);
						}
					}
				}
			}

			.course-list {
				height: auto;
				overflow: hidden;
				transition: height 0.8s ease;

				.list-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 30rpx 28rpx;

					.title-text {
						display: flex;
						align-items: center;
						justify-content: flex-start;
						font-size: 26rpx;
						color: #4A4A4C;

					}

					.enter {
						width: 130rpx;
						height: 44rpx;
						background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
						border-radius: 60rpx;
						color: #fff;
						text-align: center;
						line-height: 44rpx;
						font-weight: bold;
						font-size: 26rpx;
					}
				}
			}
		}

		.padding-dir {
			padding: 0;
		}

		.bottom-opt {
			width: 100%;
			padding: 0 22rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			position: fixed;
			bottom: 0;
			height: 120rpx;
			background-color: #fff;
			z-index: 9999;
			box-shadow: 0rpx -6rpx 6rpx 1rpx rgba(180, 180, 180, 0.16);

			.left {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.price {
					font-size: 32rpx;
					color: #E16965;
					margin-right: 24rpx;
				}

			}

			.btnBox {
				display: flex;
			}


			.btn,
			.car {
				width: 204rpx;
				height: 66rpx;
				background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
				line-height: 66rpx;
				font-weight: bold;
				font-size: 30rpx;
				color: #FFFFFF;
				text-align: center;
				border-radius: 40rpx;
			}

			.car {
				background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
				margin-right: 24rpx;
			}
		}

		.dir-tab {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			// margin-left: 30rpx;
			margin-bottom: 30rpx;

			text {
				width: 110rpx;
				height: 60rpx;
				border-radius: 12rpx;
				text-align: center;
				line-height: 60rpx;
				font-size: 26rpx;
				font-weight: bold;
				color: #201E2E;
				background-color: #fff;
				margin-right: 20rpx;

			}

			.sel {
				background: #26C8AC;
				color: #fff;
			}
		}

		.copy-content {
			width: 690rpx;
			// margin: 20rpx auto;
			box-sizing: border-box;
			// padding: 20rpx 30rpx 20rpx 40rpx;
			box-sizing: border-box;
			border-radius: 30rpx;
			background-color: #fff;



			p {
				::v-deep img {
					width: 100% !important;
					border-radius: 20rpx;
				}
			}

			.copy-title {
				width: 170rpx;
				height: 40rpx;
				line-height: 36rpx;
				text-align: center;
				font-size: 26rpx;
				color: #01997A;
				border-radius: 363rpx;
				border: 1rpx solid #01997A;
				// 新增样式让元素靠右对齐
				margin-left: auto;
				justify-content: center;
				margin-bottom: 10rpx;
			}

			.content-list {
				font-weight: 400;
				font-size: 26rpx;
				color: #4A4A4C;
				line-height: 1.5;
				margin-bottom: 20rpx;

				.question {
					position: relative;

					&:before {
						content: "";
						position: absolute;
						width: 8rpx;
						height: 8rpx;
						top: 18rpx;
						left: -15rpx;
						border-radius: 50%;
						background: #01997A;
					}
				}

				.answer {
					margin-top: 5rpx;
					color: #01997A;

				}
			}
		}

		.friends-content {
			width: 690rpx;
			margin: 20rpx auto;
			box-sizing: border-box;
			padding: 20rpx 30rpx 20rpx 40rpx;
			box-sizing: border-box;
			border-radius: 20rpx;
			background-color: #fff;

			.friends-title {
				font-weight: bold;
				font-size: 28rpx;
				color: #404040;
				display: flex;
				align-items: center;
				margin-bottom: 15rpx;

				.green {
					width: 11rpx;
					height: 30rpx;
					background: #01997A;
					margin-right: 8rpx;
					border-radius: 0rpx 8rpx 0rpx 8rpx;
				}
			}

			.title-copy {
				font-size: 26rpx;
				color: #01997A;
				display: flex;
				align-items: center;
				justify-content: space-between;

				text {
					margin-left: 20rpx;
					position: relative;

					// &:before {
					// 	content: "";
					// 	position: absolute;
					// 	width: 8rpx;
					// 	height: 8rpx;
					// 	top: 18rpx;
					// 	left: -20rpx;
					// 	border-radius: 50%;
					// 	background: #01997A;
					// }
				}


			}

			.copyText {
				width: 140rpx;
				color: #01997A;
				height: 45rpx;
				line-height: 45rpx;
				text-align: center;
				border-radius: 363rpx;
				border: 1rpx solid #01997A;
			}

			.contentText {
				width: 400rpx;
				text-align: center;
				line-height: 2;
			}

			.contentImg {
				display: flex;
				align-items: flex-start;
				justify-content: space-between;
				margin-top: 15rpx;
				margin-bottom: 20rpx;

				.image-box {
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					width: 400rpx;

					image {
						width: 120rpx;
						height: 120rpx;
						margin-right: 10rpx;
						margin-bottom: 10rpx;
					}
				}
			}
		}

		.mask {
			position: fixed;
			top: 0;
			left: 0;
			width: 100vw;
			height: 100vh;
			background-color: rgba(0, 0, 0, 0.6);
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			z-index: 99999;

			.share {
				width: 340rpx;
				height: 360rpx;
				position: absolute;
				right: 40rpx;
				top: 20rpx;
			}
		}
	}
</style>