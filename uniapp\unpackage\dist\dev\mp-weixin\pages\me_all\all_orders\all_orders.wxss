@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-6e8e75b3 {
  background: #F6F7FB;
  min-height: 100vh;
}
.container .tabs.data-v-6e8e75b3 {
  height: 100rpx;
  background-color: #fff;
  width: 100%;
  font-weight: bold;
  font-size: 30rpx;
  color: #5A5A5A;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-bottom: 1rpx solid #f1f2f3;
}
.container .tabs text.data-v-6e8e75b3 {
  height: 100rpx;
  line-height: 100rpx;
}
.container .tabs .active.data-v-6e8e75b3 {
  color: #00C2A0;
  border-bottom: 6rpx solid #00C2A0;
}
.container .searchBox.data-v-6e8e75b3 {
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .searchBox input.data-v-6e8e75b3 {
  width: 612rpx;
  height: 74rpx;
  text-align: center;
  font-weight: bold;
  font-size: 26rpx;
  color: #989898;
  background: #FFFFFF;
  border-radius: 77rpx;
}
.container .searchBox image.data-v-6e8e75b3 {
  width: 60rpx;
  height: 60rpx;
}
.container .list.data-v-6e8e75b3 {
  width: 690rpx;
  background: #FFFFFF;
  padding: 20rpx 0rpx;
  box-sizing: border-box;
  margin: 0 auto 25rpx;
  border-radius: 20rpx;
}
.container .list .time-top.data-v-6e8e75b3 {
  padding: 0 25rpx 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #E5E5E5;
}
.container .list .time-top .time.data-v-6e8e75b3 {
  font-size: 24rpx;
  color: #A2A2A2;
}
.container .list .time-top .status.data-v-6e8e75b3 {
  display: flex;
  align-items: center;
}
.container .list .time-top .status .btn.data-v-6e8e75b3 {
  width: 110rpx;
  height: 45rpx;
  line-height: 45rpx;
  text-align: center;
  font-size: 22rpx;
  color: #00C2A0;
  border-radius: 30rpx;
  margin-right: 10rpx;
  border: 1rpx solid #00C2A0;
}
.container .list .time-top .status text.data-v-6e8e75b3 {
  font-size: 24rpx;
  color: #A2A2A2;
}
.container .list .content.data-v-6e8e75b3 {
  padding: 20rpx 25rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #E5E5E5;
  margin-bottom: 25rpx;
}
.container .list .content .title.data-v-6e8e75b3 {
  font-weight: bold;
  font-size: 26rpx;
  color: #060606;
  line-height: 1.4;
}
.container .list .content .detail.data-v-6e8e75b3 {
  margin-top: 12rpx;
  display: flex;
  align-items: center;
  color: #A4A4A4;
}
.container .list .content .detail text.data-v-6e8e75b3 {
  font-weight: 500;
  font-size: 24rpx;
  color: #A4A4A4;
}
.container .list .content .detail .subject.data-v-6e8e75b3 {
  font-weight: bold;
  font-size: 22rpx;
  color: #09CC8C;
  padding: 9rpx 7rpx;
  background: #EEFAF6;
  border-radius: 10rpx;
}
.container .list .content .detail .endTime.data-v-6e8e75b3 {
  margin: 0 12rpx;
  position: relative;
}
.container .list .content .detail .endTime.data-v-6e8e75b3::after {
  content: '';
  position: absolute;
  width: 1rpx;
  height: 25rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: -12rpx;
  background-color: #A4A4A4;
}
.container .list .content .detail .num.data-v-6e8e75b3 {
  margin-left: 12rpx;
}
.container .list .content .bottom.data-v-6e8e75b3 {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .list .content .bottom .teacher-list.data-v-6e8e75b3 {
  padding-left: 10rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.container .list .content .bottom .teacher-list .teacher-info.data-v-6e8e75b3 {
  margin-right: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #818181;
  margin-bottom: 20rpx;
}
.container .list .content .bottom .teacher-list .teacher-info.data-v-6e8e75b3:last-child {
  margin-right: 0;
}
.container .list .content .bottom .teacher-list .teacher-info .avatar.data-v-6e8e75b3 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 100%;
  margin-bottom: 2rpx;
}
.container .list .content .bottom .money-right.data-v-6e8e75b3 {
  display: flex;
  align-items: center;
  margin-right: 10rpx;
}
.container .list .content .bottom .money-right .money.data-v-6e8e75b3 {
  font-size: 30rpx;
  color: #4C5370;
  margin-right: 10rpx;
}
.container .list .phone-money.data-v-6e8e75b3 {
  padding: 0 25rpx;
  font-weight: 500;
  font-size: 24rpx;
  color: #A4A4A4;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .list .phone-money .money.data-v-6e8e75b3 {
  display: flex;
  align-items: center;
}
.container .list .phone-money .money text.data-v-6e8e75b3 {
  font-weight: 500;
  font-size: 34rpx;
  color: #E62E2E;
}
.container .list .class-content.data-v-6e8e75b3 {
  padding: 25rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #E5E5E5;
  margin-bottom: 25rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .list .class-content .imageBox.data-v-6e8e75b3 {
  flex: 3.4;
  display: flex;
  /* 启用Flex布局 */
  flex-wrap: nowrap;
  /* 禁止换行 */
  overflow-x: auto;
}
.container .list .class-content .imageBox image.data-v-6e8e75b3 {
  flex: 0 0 auto;
  /* 禁止图片伸缩 */
  width: 156rpx;
  height: 116rpx;
  margin-right: 12rpx;
}
.container .list .class-content .imageBox image.data-v-6e8e75b3:last-child {
  margin-right: 0rpx;
}
.container .list .class-content .title.data-v-6e8e75b3 {
  font-weight: bold;
  font-size: 28rpx;
  color: #414141;
}
.container .list .class-content .desc.data-v-6e8e75b3 {
  font-weight: 400;
  font-size: 22rpx;
  color: #777777;
  margin-top: 10rpx;
}
.container .list .class-content .right.data-v-6e8e75b3 {
  flex: 1;
  font-weight: 500;
  font-size: 30rpx;
  color: #4C5370;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
.container .list .class-content .right .money.data-v-6e8e75b3 {
  width: 100%;
  text-align: right;
}
.container .list .class-content .right .num.data-v-6e8e75b3 {
  width: 100%;
  text-align: right;
  font-size: 20rpx;
}

