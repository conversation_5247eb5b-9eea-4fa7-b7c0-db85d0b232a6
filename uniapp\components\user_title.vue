<template>
	<view class="container">
		<view class="tip">
			<text class="tip-text">{{title}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			};
		},
		props:{
			title:{
				default:'标题',
				type:String	
			}
		}
	}
</script>

<style lang="scss" scoped>
	.tip {
		.tip-text {
			position: relative;
			display: inline-block;
			font-weight: 800;
			font-size: 30rpx;
			color: #060606;
			z-index: 100;
		}

		.tip-text::after {
			content: "";
			position: absolute;
			bottom: -6rpx;
			left: 0;
			width: 100%;
			height: 20rpx;
			/* 指定高度 */
			background-color: #DBFF9C;
			/* 底部背景颜色 */
			z-index: -1;
		}
	}
</style>