import Vue from 'vue'
import Vuex from "vuex"
import user from "./user/index.js"
Vue.use(Vuex)

const store = new Vuex.Store({
	state: {
		eventSourceState: []
	},
	getters: {
		// token: () => uni.getStorageSync('USER-TOKEN') // 实时获取
		// token: state => state.user.token
	},
	mutations: {
		addEventSource(state, data) {
			state.eventSourceState.push(data)
		},
		removeEventSource(state, payload) {
			state.eventSourceState = state.eventSourceState.filter(item => item !== payload)
		}
	},
	actions: {},
	modules: {
		user
	}

})
export default store