<view class="data-v-127632e4"><height vue-id="3c50aaa2-1" hg="{{System_height}}" class="data-v-127632e4" bind:__l="__l"></height><block wx:if="{{showLocationError}}"><view class="location-error-toast data-v-127632e4"><text class="error-text data-v-127632e4">定位失败，请重新开启定位</text><button data-event-opts="{{[['tap',[['getlocation',['$event']]]]]}}" class="retry-btn data-v-127632e4" bindtap="__e">去开启</button></view></block><view class="search data-v-127632e4"><view class="search_left data-v-127632e4"><image src="/static/orderLogo.png" mode class="data-v-127632e4"></image></view></view><view class="hade_location data-v-127632e4"><view data-event-opts="{{[['tap',[['goorderAll']]]]}}" class="hade_location_left data-v-127632e4" bindtap="__e"><view class="hade_location_left_top data-v-127632e4"><text class="data-v-127632e4">{{showLocationError?'定位失败':schoolDetail.name}}</text><block wx:if="{{schoolDetail.isPremier}}"><view class="schoolName data-v-127632e4"><image src="/static/king.png" mode class="data-v-127632e4"></image><text class="data-v-127632e4">旗舰校区</text></view></block><u-icon vue-id="3c50aaa2-2" name="arrow-right" color="#313131" size="24rpx" class="data-v-127632e4" bind:__l="__l"></u-icon></view><view class="hade_location_left_down data-v-127632e4">{{''+(schoolDetail.addr||schoolDetail.selectedPlace)+''}}</view></view></view><view class="scrollBox data-v-127632e4"><scroll-view class="menu data-v-127632e4" scroll-x="{{true}}"><block wx:for="{{menuItems}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['setActiveIndex',[index]]]]]}}" class="{{['data-v-127632e4','menu-item',activeIndex===index?'active':'']}}" bindtap="__e">{{''+item.name+''}}</view></block></scroll-view></view><view class="felx data-v-127632e4"><orderlist vue-id="3c50aaa2-3" activeIndex="{{components_index}}" content="{{store_infoApi_data}}" data-event-opts="{{[['^aid_mgs',[['aid_mgs']]]]}}" bind:aid_mgs="__e" class="data-v-127632e4" bind:__l="__l"></orderlist><view class="felx_right_box data-v-127632e4"><scroll-view style="height:100%;" scroll-y="true" class="data-v-127632e4"><view class="right_title data-v-127632e4"><view data-event-opts="{{[['tap',[['changeTitle',['-1']]]]]}}" class="{{['data-v-127632e4','title',titleIndex=='-1'?'title_active':'']}}" bindtap="__e">全部</view><block wx:if="{{$root.g0}}"><block wx:for="{{titleList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeTitle',[index,'$0'],[[['titleList','',index,'id']]]]]]]}}" class="{{['data-v-127632e4','title',titleIndex==index?'title_active':'']}}" bindtap="__e">{{''+item.name+''}}</view></block></block></view><view class="felx_right_box_conten data-v-127632e4"><block wx:if="{{$root.g1<1}}"><view style="margin-top:150rpx;" class="data-v-127632e4"><u-empty vue-id="3c50aaa2-4" mode="data" iconSize="{{150}}" textSize="{{24}}" text="暂无此类产品" icon class="data-v-127632e4" bind:__l="__l"></u-empty></view></block><ordercard vue-id="3c50aaa2-5" order_id="{{order_id}}" content="{{orderlist}}" store_id="{{store_default_data.id}}" activate_data="{{activate_data}}" data-event-opts="{{[['^specification',[['specification']]],['^order',[['order_carListApi']]],['^login',[['e0']]],['^addCar',[['addCar']]]]}}" bind:specification="__e" bind:order="__e" bind:login="__e" bind:addCar="__e" class="data-v-127632e4" bind:__l="__l"></ordercard><block wx:if="{{$root.g2}}"><uni-load-more vue-id="3c50aaa2-6" contentText="{{contentText}}" status="{{loadMoreStatus}}" data-event-opts="{{[['^clickLoadMore',[['getMore']]]]}}" bind:clickLoadMore="__e" class="data-v-127632e4" bind:__l="__l"></uni-load-more></block></view><height vue-id="3c50aaa2-7" hg="{{70}}" class="data-v-127632e4" bind:__l="__l"></height></scroll-view></view></view><block wx:if="{{!user.user}}"><close vue-id="3c50aaa2-8" shopping_trolley_list="{{shopping_trolley_list}}" isAdd="{{isAdd}}" page-status="{{pageStatus}}" showCart="{{showCart}}" data-event-opts="{{[['^update',[['order_carListApi']]],['^register',[['register']]],['^goPay',[['goPay']]]]}}" bind:update="__e" bind:register="__e" bind:goPay="__e" class="data-v-127632e4" bind:__l="__l"></close></block><login vue-id="3c50aaa2-9" show="{{enter}}" data-event-opts="{{[['^closepage',[['closepage']]]]}}" bind:closepage="__e" class="data-v-127632e4" bind:__l="__l"></login><movable-area class="movableArea data-v-127632e4"><movable-view class="movableView data-v-127632e4" direction="all" x="600rpx" y="800rpx"><block wx:if="{{counp_show}}"><view class="coupn data-v-127632e4"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="coupn_icon data-v-127632e4" bindtap="__e"><u-icon vue-id="3c50aaa2-10" name="close-circle-fill" color="#0C0B0B" size="28" class="data-v-127632e4" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['routergos',['/pages/me_all/coupon_collection/coupon_collection']]]]]}}" class="coupn_img data-v-127632e4" bindtap="__e"><image src="../../static/Project_drawing 38.png" mode class="data-v-127632e4"></image></view><view data-event-opts="{{[['tap',[['routergos',['/pages/me_all/coupon_collection/coupon_collection']]]]]}}" class="coupn_title data-v-127632e4" bindtap="__e"><view class="coupn_title_text data-v-127632e4">领券中心</view><u-icon vue-id="3c50aaa2-11" name="arrow-right" color="#68150A " size="22" class="data-v-127632e4" bind:__l="__l"></u-icon></view></view></block></movable-view></movable-area></view>