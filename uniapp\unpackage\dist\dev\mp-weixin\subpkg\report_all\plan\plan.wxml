<view class="container data-v-0d65619b"><view class="header data-v-0d65619b"><button data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="transparent-button data-v-0d65619b" bindtap="__e"><uni-icons class="left-icon data-v-0d65619b" vue-id="c1715a6a-1" type="left" size="20" bind:__l="__l"></uni-icons></button><image class="header-img data-v-0d65619b" src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/report_header_bg.png" mode=" widthFix"></image><text class="teacher-info data-v-0d65619b">{{"学生："+reportUserInfo.name}}</text></view><view class="content data-v-0d65619b"><view class="base-info data-v-0d65619b"><blue-title vue-id="c1715a6a-2" title="第一部分：个人基础信息" class="data-v-0d65619b" bind:__l="__l"></blue-title><view class="base-info-item-container data-v-0d65619b"><view class="base-info-item width-25 data-v-0d65619b"><text class="item-title data-v-0d65619b">学员姓名：</text><text class="item data-v-0d65619b">{{reportUserInfo.name}}</text></view><view class="base-info-item data-v-0d65619b"><text class="item-title data-v-0d65619b">性别：</text><block wx:if="{{reportUserInfo.gender==0}}"><view class="item data-v-0d65619b">未知</view></block><block wx:if="{{reportUserInfo.gender==2}}"><view class="item data-v-0d65619b">女</view></block><block wx:if="{{reportUserInfo.gender==1}}"><view class="item data-v-0d65619b">男</view></block></view><view class="base-info-item data-v-0d65619b"><text class="item-title data-v-0d65619b">本科入学年份：</text><text class="item data-v-0d65619b">{{reportUserInfo.joinYear}}</text></view><view class="base-info-item width-25 data-v-0d65619b"><text class="item-title data-v-0d65619b">本科院校：</text><text class="item data-v-0d65619b">{{reportUserInfo.schoolName}}</text></view><view class="base-info-item data-v-0d65619b"><text class="item-title data-v-0d65619b">学院：</text><text class="item data-v-0d65619b">{{reportUserInfo.collegeName}}</text></view><view class="base-info-item data-v-0d65619b"><text class="item-title data-v-0d65619b">专业：</text><text class="item major data-v-0d65619b">{{reportUserInfo.majorName}}</text></view><view class="base-info-item width-25 data-v-0d65619b"><text class="item-title data-v-0d65619b">学员性格：</text><text class="item data-v-0d65619b">{{reportUserInfo.personality==1?'内向':'外向'}}</text></view><view class="base-info-item data-v-0d65619b"><text class="item-title data-v-0d65619b">毕业发展：</text><text class="item data-v-0d65619b">{{reportUserInfo.postGraduationLabel}}</text></view></view><view class="hobby data-v-0d65619b"><view class="hobby-item data-v-0d65619b"><text class="hobby-title data-v-0d65619b">体育特长：</text><text class="hobby-info data-v-0d65619b">{{reportUserInfo.sportsInterest}}</text></view><view class="hobby-item data-v-0d65619b"><text class="hobby-title data-v-0d65619b">艺术特长：</text><text class="hobby-info data-v-0d65619b">{{reportUserInfo.artInterest}}</text></view><view class="hobby-item data-v-0d65619b"><text class="hobby-title data-v-0d65619b">其它特长：</text><text class="hobby-info data-v-0d65619b">{{reportUserInfo.academicInterest}}</text></view><view class="hobby-item data-v-0d65619b"><text class="hobby-title data-v-0d65619b">综合描述：</text><text class="hobby-info data-v-0d65619b">{{reportUserInfo.collegePlan}}</text></view></view></view><view class="exam-info data-v-0d65619b"><view class="base-info data-v-0d65619b"><blue-title vue-id="c1715a6a-3" title="第二部分：高考基础信息" class="data-v-0d65619b" bind:__l="__l"></blue-title></view><view class="table data-v-0d65619b"><view class="header data-v-0d65619b"><view class="title data-v-0d65619b">总分</view><view class="title data-v-0d65619b">排名</view><view class="title data-v-0d65619b">位次</view><view class="title data-v-0d65619b">语文</view><view class="title data-v-0d65619b">数学</view><view class="title data-v-0d65619b">外语</view><view class="title data-v-0d65619b">物理</view><view class="title data-v-0d65619b">化学</view><view class="title data-v-0d65619b">生物</view><view class="title data-v-0d65619b">政治</view><view class="title data-v-0d65619b">历史</view><view class="title data-v-0d65619b">地理</view></view><view class="table-line data-v-0d65619b"><view class="table-line-item data-v-0d65619b">{{''+reportUserInfo.totalScore+''}}</view><view class="table-line-item data-v-0d65619b">{{''+reportUserInfo.rank+''}}</view><view class="table-line-item data-v-0d65619b">{{''+reportUserInfo.position+''}}</view><view class="table-line-item data-v-0d65619b">{{''+reportUserInfo.chineseScore+''}}</view><view class="table-line-item data-v-0d65619b">{{''+reportUserInfo.mathScore+''}}</view><view class="table-line-item data-v-0d65619b">{{''+reportUserInfo.foreignLangScore+''}}</view><view class="table-line-item data-v-0d65619b">{{''+(reportUserInfo.physicsScore?reportUserInfo.physicsScore:'-')+''}}</view><view class="table-line-item data-v-0d65619b">{{''+(reportUserInfo.chemistryScore?reportUserInfo.chemistryScore:'-')+''}}</view><view class="table-line-item data-v-0d65619b">{{''+(reportUserInfo.biologyScore?reportUserInfo.biologyScore:'-')+''}}</view><view class="table-line-item data-v-0d65619b">{{''+(reportUserInfo.politicsScore?reportUserInfo.politicsScore:'-')+''}}</view><view class="table-line-item data-v-0d65619b">{{''+(reportUserInfo.historyScore?reportUserInfo.historyScore:'-')+''}}</view><view class="table-line-item data-v-0d65619b">{{''+(reportUserInfo.geographyScore?reportUserInfo.geographyScore:'-')+''}}</view></view></view></view><view class="university-info data-v-0d65619b"><blue-title vue-id="c1715a6a-4" title="第三部分：院校基本信息" class="data-v-0d65619b" bind:__l="__l"></blue-title><view class="university-tag data-v-0d65619b"><image class="logo data-v-0d65619b" src="{{reportInfo.school.logo}}" mode></image><view class="tag data-v-0d65619b"><text class="name data-v-0d65619b">{{''+reportInfo.school.name+''}}</text><view class="tag-list data-v-0d65619b"><block wx:for="{{reportInfo.school.tags}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><view class="tag-list-item data-v-0d65619b">{{''+tag+''}}</view></block></view></view></view><user-content vue-id="c1715a6a-5" msgtype="{{schoolInfo}}" class="data-v-0d65619b" bind:__l="__l"></user-content></view><view class="plan data-v-0d65619b"><blue-title vue-id="c1715a6a-6" title="第四部分：就业方向" class="data-v-0d65619b" bind:__l="__l"></blue-title><user-content style="margin-top:28rpx;" vue-id="c1715a6a-7" msgtype="{{careerDirection}}" class="data-v-0d65619b" bind:__l="__l"></user-content></view><view class="plan data-v-0d65619b"><blue-title vue-id="c1715a6a-8" title="第五部分：升学规划" class="data-v-0d65619b" bind:__l="__l"></blue-title><user-content style="margin-top:28rpx;" vue-id="c1715a6a-9" msgtype="{{educationPlanning}}" class="data-v-0d65619b" bind:__l="__l"></user-content></view><view class="ability data-v-0d65619b"><blue-title vue-id="c1715a6a-10" title="第六部分：学术能力提升" class="data-v-0d65619b" bind:__l="__l"></blue-title><user-content style="margin-top:28rpx;" vue-id="c1715a6a-11" msgtype="{{academicAbilityEnhancement}}" class="data-v-0d65619b" bind:__l="__l"></user-content></view><view class="university-plan data-v-0d65619b"><blue-title vue-id="c1715a6a-12" title="第七部分：大学规划" class="data-v-0d65619b" bind:__l="__l"></blue-title><user-content-two style="margin-top:28rpx;" vue-id="c1715a6a-13" msgtype="{{organizationalLife}}" class="data-v-0d65619b" bind:__l="__l"></user-content-two></view><view class="ability data-v-0d65619b"><blue-title vue-id="c1715a6a-14" title="第八部分：预科推荐" class="data-v-0d65619b" bind:__l="__l"></blue-title><user-content style="margin-top:28rpx;" vue-id="c1715a6a-15" isSse="{{false}}" class="data-v-0d65619b" bind:__l="__l"></user-content></view></view><view class="footer data-v-0d65619b"><button class="save-btn data-v-0d65619b" style="{{'background:'+(generating?'#ccc':'linear-gradient(268deg, #26C8AC 0%, #19C990 100%)')+';'}}" loading="{{loading}}" data-event-opts="{{[['tap',[['handleSave',['$event']]]]]}}" bindtap="__e">保存报告</button></view></view>