{"version": 3, "sources": ["webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/form_step4/form_step4.vue?cd44", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/form_step4/form_step4.vue?0184", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/form_step4/form_step4.vue?c8cf", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/form_step4/form_step4.vue?b14a", "uni-app:///subpkg/select_school/form_step4/form_step4.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/form_step4/form_step4.vue?16ee", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/form_step4/form_step4.vue?471b", "uni-app:///main.js"], "names": ["data", "formData", "regionPreference", "provinceSelection", "dreamSchool", "schoolLevel", "referenceBooks", "computed", "onLoad", "methods", "back", "uni", "prevStep", "nextStep", "url", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAomB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACuFxnB;AAEA;AAAA;AAAA,eACA;EACAA;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC,4BACA,uCACA;EACAC;IACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IACAC;MACA;MACAD;IACA;IACAE;MACA;MACAF;;MAEA;MACAA;QACAG;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClIA;AAAA;AAAA;AAAA;AAAuqC,CAAgB,6oCAAG,EAAC,C;;;;;;;;;;;ACA3rC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C", "file": "subpkg/select_school/form_step4/form_step4.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./form_step4.vue?vue&type=template&id=03cd5f3c&scoped=true&\"\nvar renderjs\nimport script from \"./form_step4.vue?vue&type=script&lang=js&\"\nexport * from \"./form_step4.vue?vue&type=script&lang=js&\"\nimport style0 from \"./form_step4.vue?vue&type=style&index=0&id=03cd5f3c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"03cd5f3c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg/select_school/form_step4/form_step4.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./form_step4.vue?vue&type=template&id=03cd5f3c&scoped=true&\"", "var components\ntry {\n  components = {\n    height: function () {\n      return import(\n        /* webpackChunkName: \"components/height/height\" */ \"@/components/height/height.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./form_step4.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./form_step4.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<height :hg='System_height'></height>\n\t\t<view class=\"content_header\">\n\t\t\t<view class=\"nav-title\" :style=\"{'top':System_height+'rpx'}\">\n\t\t\t\t<text>AI考研择校报告</text>\n\t\t\t</view>\n\t\t\t<uni-icons type=\"left\" size=\"24\" color=\"#2D2D2D\" class=\"back-left\" :style=\"{'top':System_height+'rpx'}\"\n\t\t\t\t@tap=\"back\"></uni-icons>\n\t\t</view>\n\t\t\n\t\t<!-- 通知栏 -->\n\t\t<view class=\"notification\">\n\t\t\t<image src=\"/static/select_school/notification_icon-56586a.png\" class=\"notification-icon\"></image>\n\t\t\t<text class=\"notification-text\">请认真完善信息，以便于精准生成报告！</text>\n\t\t</view>\n\t\t\n\t\t<!-- 步骤指示器 -->\n\t\t<view class=\"step-indicator\">\n\t\t\t<view class=\"step-item\">\n\t\t\t\t<view class=\"step-number\">1</view>\n\t\t\t</view>\n\t\t\t<view class=\"step-item\">\n\t\t\t\t<view class=\"step-number\">2</view>\n\t\t\t</view>\n\t\t\t<view class=\"step-item\">\n\t\t\t\t<view class=\"step-number\">3</view>\n\t\t\t</view>\n\t\t\t<view class=\"step-item active\">\n\t\t\t\t<view class=\"step-number\">4</view>\n\t\t\t</view>\n\t\t\t<view class=\"step-item\">\n\t\t\t\t<view class=\"step-number\">5</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 表单标题 -->\n\t\t<view class=\"form-title\">\n\t\t\t<view class=\"title-bg\"></view>\n\t\t\t<text class=\"title-text\">目标院校倾向</text>\n\t\t</view>\n\t\t\n\t\t<!-- 表单内容 -->\n\t\t<view class=\"form-content\">\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"form-label\">地区倾向</view>\n\t\t\t\t<input class=\"form-input\" v-model=\"formData.regionPreference\" placeholder=\"请选择地区\" />\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#C3C3C3\" class=\"form-arrow\"></uni-icons>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"form-label\">省份选择</view>\n\t\t\t\t<input class=\"form-input\" v-model=\"formData.provinceSelection\" placeholder=\"请选择省份\" />\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#C3C3C3\" class=\"form-arrow\"></uni-icons>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"form-label\">梦校</view>\n\t\t\t\t<input class=\"form-input\" v-model=\"formData.dreamSchool\" placeholder=\"请输入梦想院校\" />\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#C3C3C3\" class=\"form-arrow\"></uni-icons>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"form-label\">院校层次</view>\n\t\t\t\t<input class=\"form-input\" v-model=\"formData.schoolLevel\" placeholder=\"请选择院校层次\" />\n\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#C3C3C3\" class=\"form-arrow\"></uni-icons>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"form-item large\">\n\t\t\t\t<view class=\"form-label\">专业课制定参考书:</view>\n\t\t\t\t<textarea class=\"form-textarea\" v-model=\"formData.referenceBooks\" placeholder=\"请输入专业课参考书目\"></textarea>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部按钮 -->\n\t\t<view class=\"bottom-buttons\">\n\t\t\t<view class=\"prev-btn\" @click=\"prevStep\">\n\t\t\t\t<text>上一步</text>\n\t\t\t</view>\n\t\t\t<view class=\"next-btn\" @click=\"nextStep\">\n\t\t\t\t<text>下一步</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport {\n\t\tmapState\n\t} from 'vuex'\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tformData: {\n\t\t\t\t\tregionPreference: 'A区',\n\t\t\t\t\tprovinceSelection: '安徽省、上海市、浙江省、江苏省',\n\t\t\t\t\tdreamSchool: '中国科学技术大学',\n\t\t\t\t\tschoolLevel: '985',\n\t\t\t\t\treferenceBooks: '马克思主义理论书籍'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t...mapState(['System_height'])\n\t\t},\n\t\tonLoad() {\n\t\t\t// 加载之前保存的数据\n\t\t\tconst savedData = uni.getStorageSync('selectSchoolStep4')\n\t\t\tif (savedData) {\n\t\t\t\tthis.formData = { ...this.formData, ...savedData }\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tback() {\n\t\t\t\tuni.navigateBack()\n\t\t\t},\n\t\t\tprevStep() {\n\t\t\t\t// 返回上一步\n\t\t\t\tuni.navigateBack()\n\t\t\t},\n\t\t\tnextStep() {\n\t\t\t\t// 保存当前步骤数据\n\t\t\t\tuni.setStorageSync('selectSchoolStep4', this.formData)\n\t\t\t\t\n\t\t\t\t// 跳转到下一步\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/subpkg/select_school/form_step5/form_step5'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.content {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(180deg, #CBF2E0 0%, #F6F7FB 43.3%);\n\t\tpadding-bottom: 160rpx;\n\t}\n\t\n\t.content_header {\n\t\tposition: relative;\n\t\theight: 140rpx;\n\t\tbackground: #00C2A0;\n\t\t\n\t\t.nav-title {\n\t\t\tposition: absolute;\n\t\t\tleft: 50%;\n\t\t\ttransform: translateX(-50%);\n\t\t\tz-index: 10;\n\t\t\t\n\t\t\ttext {\n\t\t\t\tfont-size: 34rpx;\n\t\t\t\tcolor: #2D2D2D;\n\t\t\t\tfont-weight: 400;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.back-left {\n\t\t\tposition: absolute;\n\t\t\tleft: 30rpx;\n\t\t\tz-index: 10;\n\t\t}\n\t}\n\t\n\t.notification {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin: 30rpx;\n\t\tpadding: 14rpx 28rpx;\n\t\tbackground: #F5FFFD;\n\t\tborder-radius: 31rpx;\n\t\t\n\t\t.notification-icon {\n\t\t\twidth: 49rpx;\n\t\t\theight: 49rpx;\n\t\t\tmargin-right: 28rpx;\n\t\t}\n\t\t\n\t\t.notification-text {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #5A5A5A;\n\t\t\tflex: 1;\n\t\t}\n\t}\n\t\n\t.step-indicator {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin: 30rpx 0;\n\t\t\n\t\t.step-item {\n\t\t\tmargin: 0 81rpx;\n\t\t\t\n\t\t\t.step-number {\n\t\t\t\twidth: 43rpx;\n\t\t\t\theight: 43rpx;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tbackground: #FF9B3A;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t}\n\t\t\t\n\t\t\t&.active .step-number {\n\t\t\t\tbackground: #FF9B3A;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.form-title {\n\t\tposition: relative;\n\t\tmargin: 30rpx;\n\t\t\n\t\t.title-bg {\n\t\t\tposition: absolute;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t\twidth: 183rpx;\n\t\t\theight: 19rpx;\n\t\t\tbackground: #DBFF9C;\n\t\t}\n\t\t\n\t\t.title-text {\n\t\t\tfont-size: 30rpx;\n\t\t\tcolor: #060606;\n\t\t\tfont-weight: 400;\n\t\t}\n\t}\n\t\n\t.form-content {\n\t\tpadding: 0 30rpx;\n\t\t\n\t\t.form-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder-radius: 16rpx;\n\t\t\tpadding: 31rpx 28rpx;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\tposition: relative;\n\t\t\t\n\t\t\t&.large {\n\t\t\t\talign-items: flex-start;\n\t\t\t\tmin-height: 156rpx;\n\t\t\t\t\n\t\t\t\t.form-label {\n\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.form-label {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #504E4E;\n\t\t\t\twidth: 240rpx;\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\t\t\t\n\t\t\t.form-input {\n\t\t\t\tflex: 1;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #989898;\n\t\t\t\ttext-align: right;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.form-textarea {\n\t\t\t\tflex: 1;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #989898;\n\t\t\t\tmin-height: 80rpx;\n\t\t\t\tmargin-top: 10rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.form-arrow {\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.bottom-buttons {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbackground: #F6F7FB;\n\t\tpadding: 30rpx;\n\t\tdisplay: flex;\n\t\tgap: 30rpx;\n\t\t\n\t\t.prev-btn {\n\t\t\tflex: 1;\n\t\t\theight: 80rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tborder: 2rpx solid #5A5A5A;\n\t\t\tborder-radius: 40rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\t\n\t\t\ttext {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #5A5A5A;\n\t\t\t\tfont-weight: 400;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.next-btn {\n\t\t\tflex: 1;\n\t\t\theight: 80rpx;\n\t\t\tbackground: linear-gradient(135deg, #26C8AC 0%, #19C990 100%);\n\t\t\tborder-radius: 40rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\t\n\t\t\ttext {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tfont-weight: 400;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./form_step4.vue?vue&type=style&index=0&id=03cd5f3c&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./form_step4.vue?vue&type=style&index=0&id=03cd5f3c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754561053376\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg/select_school/form_step4/form_step4.vue'\ncreatePage(Page)"], "sourceRoot": ""}