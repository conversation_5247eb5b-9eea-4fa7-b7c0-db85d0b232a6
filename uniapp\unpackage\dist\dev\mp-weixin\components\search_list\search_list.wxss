@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.search-container.data-v-14542fb0 {
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-container .content.data-v-14542fb0 {
  z-index: 100;
  width: 632rpx;
  height: 900rpx;
  background: linear-gradient(181deg, #CBF2E0 0%, #FFFFFF 25%);
  padding: 64rpx 40rpx;
  border-radius: 32rpx;
}
.search-container .content .search-input-container.data-v-14542fb0 {
  background-color: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  height: 76rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 34rpx;
  border: 1rpx solid #2FC293;
}
.search-container .content .search-input-container .search-input.data-v-14542fb0 {
  height: 100%;
  border: 0;
  padding-left: 20rpx;
  flex: 1;
}
.search-container .content .search-input-container .search-logo.data-v-14542fb0 {
  width: 34rpx;
}
.search-container .content .list.data-v-14542fb0 {
  width: 630rpx;
  height: 635rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 11rpx 30rpx 1rpx rgba(55, 55, 55, 0.16);
  border-radius: 22rpx;
  margin-top: 20rpx;
  overflow-y: scroll;
}
.search-container .content .list .items.data-v-14542fb0 {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.search-container .content .list .items .item-text.data-v-14542fb0 {
  width: 100%;
  font-size: 30rpx;
  height: 42rpx;
  line-height: 42rpx;
  font-weight: 400;
  text-align: center;
  margin-top: 32rpx;
}

