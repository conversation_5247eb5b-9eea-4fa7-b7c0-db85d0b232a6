@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content_1.data-v-62fb55ca {
  background-color: #FFFFFF;
  padding: 30rpx;
}
.content_1 .content_1_title.data-v-62fb55ca {
  font-size: 32rpx;
  font-weight: 500;
  color: #353535;
}
.content_1 .content_1_label.data-v-62fb55ca {
  margin-top: 20rpx;
}
.content_1 .content_1_label text.data-v-62fb55ca {
  background-color: #F5F5F5;
  font-size: 24rpx;
  font-weight: 400;
  color: #353535;
  padding: 5rpx 10rpx;
  margin-right: 10rpx;
}
.content_1 .content_1_sales.data-v-62fb55ca {
  font-size: 24rpx;
  font-weight: 400;
  color: #8A8A8A;
  margin-top: 20rpx;
}
.content_1 .content_1_price.data-v-62fb55ca {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10rpx;
}
.content_1 .content_1_price view.data-v-62fb55ca:nth-child(1) {
  font-size: 34rpx;
  font-weight: 600;
  color: #FC4F46;
}
.content_1 .content_1_price view.data-v-62fb55ca:nth-child(2) {
  width: 209rpx;
  height: 61rpx;
  background: #05B6F6;
  border-radius: 13rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 61rpx;
  text-align: center;
  margin-top: 20rpx;
}
.content_2.data-v-62fb55ca {
  background: #FFFFFF;
  margin-top: 30rpx;
  padding: 30rpx 0;
}
.content_2 .content_2_title.data-v-62fb55ca {
  font-size: 32rpx;
  font-weight: 600;
  color: #353535;
  padding: 0 30rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #ECECEC;
}
.content_2 .content_2_text.data-v-62fb55ca {
  min-height: 300rpx;
  height: auto !important;
  padding: 20rpx 20rpx;
}
.content_2 .content_2_content .content_2_content_Title.data-v-62fb55ca {
  display: inline-block;
  background-color: #f8f8f8;
  text-align: center;
  font-size: 28rpx;
  margin: 20rpx;
  padding: 16rpx 24rpx;
  box-sizing: border-box;
  border-radius: 18rpx;
}
.content_2 .content_2_content .active.data-v-62fb55ca {
  background-color: #05B6F6;
  color: #fff !important;
}
.content_2 .content_2_content .content_2_content_item.data-v-62fb55ca {
  border: 1rpx solid #585858;
  margin: 10rpx 9rpx 10rpx;
  text-align: center;
  font-size: 24rpx;
  border-radius: 8rpx;
  padding: 10rpx 22rpx;
  box-sizing: border-box;
}
.content_2 .content_2_content .content_2_content_item_box.data-v-62fb55ca {
  margin-top: 30rpx;
  margin-left: 10rpx;
}
.content_2 .content_2_content .content_2_content_item_status.data-v-62fb55ca {
  border: 1rpx solid #cacaca !important;
  color: #cacaca !important;
}
.content_2 .content_2_content .content_2_content_item_active.data-v-62fb55ca {
  border: 1rpx solid #05B6F6 !important;
  color: #05B6F6 !important;
}
.settle.data-v-62fb55ca {
  position: fixed;
  bottom: 60rpx;
}
.specification.data-v-62fb55ca {
  width: 688rpx;
  background: #FFFFFF;
  border-radius: 12rpx;
  position: relative;
  z-index: 10074;
}
.specification .specification_title.data-v-62fb55ca {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #353535;
  margin: 0 auto;
}
.specification .specification_title_1.data-v-62fb55ca {
  width: 95%;
  height: 504rpx;
  margin: 0 auto;
}
.specification .specification_title_1 .specification_title_1_title.data-v-62fb55ca {
  font-size: 24rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #676767;
  display: inline-block;
  padding: 20rpx 0;
}
.specification .specification_title_1 .specification_title_1_content.data-v-62fb55ca {
  display: flex;
  align-items: center;
  overflow-x: auto;
  display: -webkit-box;
}
.specification .specification_title_1 .specification_title_1_content .specification_title_1_content_flex_activate.data-v-62fb55ca {
  background: #F4FCFF !important;
  border: 1rpx solid #00B8FB !important;
  color: #00B8FB !important;
}
.specification .specification_title_1 .specification_title_1_content .font_sizi_1.data-v-62fb55ca {
  color: #00B8FB;
}
.specification .specification_title_1 .specification_title_1_content .font_sizi_2.data-v-62fb55ca {
  border-left: 1rpx solid #00B8FB;
}
.specification .specification_title_1 .specification_title_1_content .specification_title_1_content_flex.data-v-62fb55ca {
  height: 63rpx;
  background: #FFFFFF;
  border-radius: 14rpx;
  border: 1rpx solid #F1F1F1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-right: 20rpx;
}
.specification .specification_title_1 .specification_title_1_content .specification_title_1_content_flex view.data-v-62fb55ca:nth-child(1) {
  font-size: 24rpx;
  font-weight: 400;
  color: #343434;
  line-height: 63rpx;
  text-align: center;
  padding: 0 10rpx;
}
.specification .specification_title_1 .specification_title_1_content .specification_title_1_content_flex view.data-v-62fb55ca:nth-child(2) {
  font-size: 24rpx;
  font-weight: 600;
  color: #F65329;
  line-height: 63rpx;
  border-left: 1rpx solid #F5F5F5;
  text-align: center;
  padding: 0 10rpx;
}
.specification .close.data-v-62fb55ca {
  position: absolute;
  bottom: -120rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.selected.data-v-62fb55ca {
  width: 688rpx;
  padding: 20rpx 0;
  background: #F5F5F5;
  margin-top: 60rpx;
}
.selected text.data-v-62fb55ca {
  font-size: 24rpx;
  font-weight: 400;
  color: #363636;
}
.selected text.data-v-62fb55ca:nth-child(1) {
  font-size: 24rpx;
  font-weight: 400;
  color: #676767;
  padding: 0 20rpx;
  margin-left: 10rpx;
}
.sublist.data-v-62fb55ca {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
}
.sublist .sublist_left text.data-v-62fb55ca:nth-child(1) {
  font-size: 28rpx;
  font-weight: 600;
  color: #363636;
}
.sublist .sublist_left text.data-v-62fb55ca:nth-child(2) {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF4000;
}
.sublist .sublist_left text:nth-child(2) text.data-v-62fb55ca:nth-child(1) {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF4000;
  font-size: 24rpx;
}
.sublist .sublist_right.data-v-62fb55ca {
  width: 234rpx;
  height: 62rpx;
  background: #02B6FD;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #F3FCFF;
  line-height: 62rpx;
  text-align: center;
}
.coupn.data-v-62fb55ca {
  position: absolute;
  bottom: 150rpx;
  right: 0;
  z-index: 999;
}
.coupn .coupn_icon.data-v-62fb55ca {
  width: 24rpx;
  height: 24rpx;
  margin-left: 100rpx;
  margin-bottom: 10rpx;
  opacity: 0.5;
}
.coupn .coupn_img.data-v-62fb55ca {
  width: 130rpx;
  height: 121rpx;
}
.coupn .coupn_title.data-v-62fb55ca {
  padding: 5rpx 10rpx;
  background: #FEDD5B;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  position: relative;
  bottom: 30rpx;
}
.coupn .coupn_title .coupn_title_text.data-v-62fb55ca {
  font-size: 22rpx;
  font-weight: 400;
  color: #631407;
  margin-left: 10rpx;
}

