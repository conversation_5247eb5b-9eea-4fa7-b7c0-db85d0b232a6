<template>
	<view>
		<view class="back">
			<image src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/ckg_6.png"
				mode=""></image>
		</view>
		<height :hg='System_height'></height>
		<view class="nav-tab">
			<view class="nav-tab_left" @tap="goback">
				<u-icon name="arrow-left" color="#FFFFFF" size="32"></u-icon>
			</view>
			<view class="nav-tab_content">
				会员码
			</view>
			<view class="">
			</view>
		</view>

		<view class="qr">
			<view class="qr_1" @tap="routerTo('/pages/me_all/personage/personage')">
				<view class="qr_1_img">
					<image :src="userall.avatar" mode=""></image>
				</view>
				<text>{{userall.username}}</text>
			</view>
			<view class="qr_2">
				<canvas v-if="showCanvas" canvas-id="qrcode-canvas" id="qrcode-canvas"
					style="width: 300px; height: 300px; position: absolute; left: -9999px"></canvas>
				<!-- <image :src="img" mode=""></image> -->
				<image v-if="qrCodeUrl" :src="qrCodeUrl" mode="widthFix" class="ma" />
			</view>
			<!-- <view class="qr_3">
				会员码还有{{time}}秒自动更新，请在店内消费使用
			</view> -->
		</view>

	</view>
</template>

<script>
	import {
		userInfo
	} from "@/api/public.js"
	import {
		user_code
	} from "@/api/user.js"
	import {
		cellphone
	} from "@/utils/type_height.js"
	// 引入uqrcode组件
	import UQRCode from '@/uni_modules/Sansnn-uQRCode/js_sdk/uqrcode/uqrcode.js'
	export default {
		data() {
			return {
				clear1: null,
				clear2: null,
				userall: {},
				time: 30,
				showCanvas: false,
				qrCodeUrl: '', // 生成的二维码图片路径
				img: '',
				System_height: cellphone() //系统高度
			};
		},
		onLoad() {
			// this.userInfoApi()
			// this.user_codeApi()
			// // 每30秒刷新一次
			// this.clear1 = setInterval(res => {
			// 	this.time--
			// 	if (this.time == 0) {
			// 		this.time = 30
			// 	}
			// }, 1000)
			// this.clear2 = setInterval(res => {
			// 	this.user_codeApi()
			// 	uni.showToast({
			// 		title: '已刷新二维码',
			// 		icon: "none"
			// 	})
			// }, 30000)
			let id = uni.getStorageSync('user').id
			this.createQRCode(id)
		},
		methods: {
			// / 创建二维码 (修复后的方法)
			createQRCode(content) {
				// 显示canvas（微信小程序需要）
				this.showCanvas = true

				// 等待DOM更新
				this.$nextTick(() => {
					try {
						// 创建UQRCode实例
						const qr = new UQRCode()

						// 设置二维码参数
						qr.data = content // 使用后端返回的核销码
						qr.size = 300 // 二维码大小
						qr.margin = 10 // 边距
						qr.foregroundColor = '#000000' // 前景色
						qr.backgroundColor = '#FFFFFF' // 背景色
						qr.errorCorrectLevel = UQRCode.errorCorrectLevel.H // 容错级别

						// 关键修复：先调用make()方法
						qr.make()

						// 获取canvas上下文
						const ctx = uni.createCanvasContext('qrcode-canvas', this)

						// 绘制二维码
						qr.canvasContext = ctx
						qr.drawCanvas()

						// 获取临时图片路径 (微信小程序特殊处理)
						setTimeout(() => {
							uni.canvasToTempFilePath({
								canvasId: 'qrcode-canvas',
								success: (res) => {
									this.qrCodeUrl = res.tempFilePath
									this.showCanvas = false // 生成后隐藏canvas
								},
								fail: (err) => {
									console.error('生成二维码失败', err)
									uni.showToast({
										title: '生成二维码失败',
										icon: 'none'
									})
								}
							}, this)
						}, 300)
					} catch (error) {
						console.error('生成二维码异常:', error)
						uni.showToast({
							title: '生成二维码异常',
							icon: 'none'
						})
					}
				})
			},
			// 返回上一页
			goback() {
				uni.navigateBack({
					delta: 1
				})
			},
			async userInfoApi() {
				let data = await userInfo()
				if (data.code == 1) {
					uni.setStorageSync('user', data.data)
					this.userall = data.data
					console.log(this.userall, '用户信息');
				} else {
					uni.showToast({
						title: data.msg,
						icon: "none"
					})
				}
			},
			// async user_codeApi() {
			// 	let data = await user_code()
			// 	this.img = data.data
			// 	console.log(this.img, '用户code');
			// }
		},
		// onUnload() {
		// 	clearInterval(this.clear1)
		// 	clearInterval(this.clear2)
		// }
	}
</script>

<style lang="scss">
	.back {
		position: absolute;
		width: 100%;
		height: 100vh;
	}

	.nav-tab {
		display: flex;
		align-items: center;
		justify-content: space-between;
		z-index: 2;

		.nav-tab_left {
			padding: 30rpx;
		}

		.nav-tab_content {
			font-size: 34rpx;
			font-weight: 500;
			color: #FFFFFF;
			z-index: 2;
			text-align: center;
			padding-right: 100rpx;
		}
	}

	.qr {
		width: 700rpx;
		height: 714rpx;
		background: #FFFFFF;
		border-radius: 27rpx;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 30%;

		.qr_1 {
			margin: 0 auto;
			text-align: center;

			.qr_1_img {
				width: 120rpx;
				height: 120rpx;
				border-radius: 50%;
				overflow: hidden;
				position: relative;
				bottom: 50rpx;
				margin: 0 auto;
			}

			text {
				font-size: 32rpx;
				font-weight: 400;
				color: #343434;
				position: relative;
				bottom: 50rpx;
			}

		}

		.qr_2 {
			width: 340rpx;
			height: 340rpx;
			margin: 0 auto;
		}

		.qr_3 {
			font-size: 24rpx;
			font-weight: 400;
			color: #9B9B9B;
			text-align: center;
			margin-top: 50rpx;
		}
	}
</style>