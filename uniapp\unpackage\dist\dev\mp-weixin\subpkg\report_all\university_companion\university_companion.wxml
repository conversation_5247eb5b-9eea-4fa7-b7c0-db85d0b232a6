<view class="container data-v-8e603e4a"><view class="task-section data-v-8e603e4a"><view class="task-header data-v-8e603e4a"><view class="tip data-v-8e603e4a" style="{{'visibility:'+(showMask?'hidden':'visible')+';'}}"><text class="tip-text data-v-8e603e4a">{{isCurrentMonth?'当月任务':$root.m0+'任务'}}</text></view><picker mode="date" fields="month" value="{{currentDate}}" data-event-opts="{{[['change',[['handleDateChange',['$event']]]]]}}" bindchange="__e" class="data-v-8e603e4a"><image style="height:60rpx;width:60rpx;" src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/calendar.png" class="data-v-8e603e4a"></image></picker></view><view class="title data-v-8e603e4a"><view class="tag data-v-8e603e4a">接收率</view><view class="progress data-v-8e603e4a"><view class="overlay data-v-8e603e4a" style="{{'width:'+(readRate+'%')+';'+('background-image:'+('url('+progressImg+')')+';')}}"></view></view><view class="title-info data-v-8e603e4a">{{''+readRate+'%'}}</view></view><view class="title data-v-8e603e4a"><view class="tag data-v-8e603e4a">完成率</view><view class="progress data-v-8e603e4a"><view class="overlay data-v-8e603e4a" style="{{'width:'+(doneRate+'%')+';'+('background-image:'+('url('+progressPink+')')+';')}}"></view></view><view class="title-info data-v-8e603e4a">{{''+doneRate+'%'}}</view></view><view class="task-list data-v-8e603e4a"><block wx:for="{{$root.l0}}" wx:for-item="task" wx:for-index="index" wx:key="index"><view class="task-item data-v-8e603e4a"><text class="task-index data-v-8e603e4a">{{task.f0}}</text><view class="task-title data-v-8e603e4a"><text class="data-v-8e603e4a">{{task.$orig.name}}</text><view class="btnBox data-v-8e603e4a"><block wx:if="{{task.$orig.status==0}}"><button data-event-opts="{{[['tap',[['upload',['$0'],[[['tasks','',index]]]]]]]}}" class="detail data-v-8e603e4a" bindtap="__e">上传</button></block><button data-event-opts="{{[['tap',[['showDetail',['$0'],[[['tasks','',index]]]]]]]}}" class="detail data-v-8e603e4a" bindtap="__e">详情</button></view></view><view class="tagBox data-v-8e603e4a"><block wx:if="{{task.$orig.isRead}}"><view class="tag data-v-8e603e4a">已接收</view></block><block wx:if="{{task.$orig.status==1}}"><view class="tag tags data-v-8e603e4a">已完成</view></block></view></view></block></view></view><view class="suggestion-section data-v-8e603e4a"><view class="task-header data-v-8e603e4a"><view class="tip data-v-8e603e4a"><text class="tip-text data-v-8e603e4a">综合建议</text></view><image style="height:60rpx;width:60rpx;" src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/university_companion_records.png" data-event-opts="{{[['tap',[['showAllSugesstion',['$event']]]]]}}" bindtap="__e" class="data-v-8e603e4a"></image></view><block wx:if="{{$root.g0>0}}"><view class="suggestion-content data-v-8e603e4a"><block wx:for="{{lastSuggestion}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="data-v-8e603e4a"><rich-text nodes="{{item.content}}" class="data-v-8e603e4a"></rich-text><block wx:if="{{item.image}}"><view class="imgs data-v-8e603e4a"><image class="img data-v-8e603e4a" src="{{item.image}}" mode="aspectFill"></image></view></block></view></block></view></block><block wx:else><view class="suggestion-content data-v-8e603e4a"><text class="line data-v-8e603e4a">暂无建议</text></view></block></view><block wx:if="{{showMask}}"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="mask data-v-8e603e4a" bindtap="__e"><view class="content data-v-8e603e4a"><view class="content-scroll data-v-8e603e4a"><block wx:if="{{flag==0}}"><view class="title data-v-8e603e4a">详细内容</view><view class="task-content data-v-8e603e4a"><view class="tip data-v-8e603e4a"><text class="tip-text data-v-8e603e4a">任务主题</text></view><view class="task-content-text data-v-8e603e4a">{{''+taskDetail.list.content+''}}</view></view><block wx:if="{{taskDetail.list.suggestion}}"><view class="task-content data-v-8e603e4a"><view class="tip data-v-8e603e4a"><text class="tip-text data-v-8e603e4a">完成建议</text></view><block wx:if="{{$root.g1}}"><view class="task-content-list-text data-v-8e603e4a"><block wx:for="{{suggestionList}}" wx:for-item="text" wx:for-index="i" wx:key="i"><text class="data-v-8e603e4a">{{''+text+''}}</text></block></view></block><block wx:else><view class="task-content-text data-v-8e603e4a">{{''+taskDetail.list.suggestion+''}}</view></block></view></block><block wx:if="{{taskDetail.list.purpose}}"><view class="task-content data-v-8e603e4a"><view class="tip data-v-8e603e4a"><text class="tip-text data-v-8e603e4a">任务作用</text></view><block wx:if="{{$root.g2}}"><view class="task-content-list-text data-v-8e603e4a"><block wx:for="{{purposeList}}" wx:for-item="text" wx:for-index="i" wx:key="i"><text class="data-v-8e603e4a">{{''+text+''}}</text></block></view></block><block wx:else><view class="task-content-text data-v-8e603e4a">{{''+taskDetail.list.purpose+''}}</view></block></view></block><block wx:if="{{taskDetail.list.image}}"><view class="task-content data-v-8e603e4a"><view class="task-content-image data-v-8e603e4a"><image src="{{taskDetail.list.image}}" mode="widthFix" class="data-v-8e603e4a"></image></view></view></block><block wx:if="{{taskDetail.list.attachment}}"><view class="down-load data-v-8e603e4a"><view class="down-load-icon data-v-8e603e4a"><image class="icon data-v-8e603e4a" src="../../static/imgs/zip_icon.png" mode></image><text class="text data-v-8e603e4a">{{taskDetail.downLoadName}}</text></view><button data-event-opts="{{[['tap',[['downLoad',['$0'],['taskDetail.list']]]]]}}" class="down-load-btn data-v-8e603e4a" bindtap="__e">下载</button></view></block></block><block wx:if="{{flag==1}}"><view class="history-list data-v-8e603e4a"><block wx:if="{{$root.g3===0}}"><view class="history-item data-v-8e603e4a"><view class="history-content data-v-8e603e4a">暂无历史建议记录</view></view></block><block wx:else><block wx:for="{{historySugestionList}}" wx:for-item="item" wx:for-index="i" wx:key="i"><view class="history-item data-v-8e603e4a"><view class="history-date data-v-8e603e4a"><text class="blue-block data-v-8e603e4a"></text><text class="data-v-8e603e4a">{{item.createTime}}</text></view><rich-text nodes="{{item.content}}" class="data-v-8e603e4a"></rich-text><image src="{{item.image}}" class="data-v-8e603e4a"></image></view></block></block></view></block></view><block wx:if="{{flag==0}}"><view class="bottom-btn-container data-v-8e603e4a"><button data-event-opts="{{[['tap',[['handleTaskDone',['$event']]]]]}}" class="{{['bottom-btn','data-v-8e603e4a',(currentMaterial.isRead===true)?'bottom-btn-disabled':'']}}" bindtap="__e">{{''+(currentMaterial.isRead===true?'已知晓':'已知晓阅读')+''}}</button></view></block></view></view></block></view>