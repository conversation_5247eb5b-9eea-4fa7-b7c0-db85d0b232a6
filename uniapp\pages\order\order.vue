<template>
	<view>

		<height :hg='System_height'></height>
		<view v-if="showLocationError" class="location-error-toast">
			<text class="error-text">定位失败，请重新开启定位</text>
			<button class="retry-btn" @click="getlocation">去开启</button>
		</view>
		<view class="search">
			<view class="search_left">
				<image src="@/static/orderLogo.png" mode=""></image>
			</view>
			<!-- <view style="display: flex; padding-right: 200rpx;">
				<view class="search_right" @tap="store_collectionApi(store_default_data.is_collection)">
					<image src="../../static/Project_drawing 40.png" v-if="store_default_data.is_collection" mode="">
					</image>
					<image src="../../static/Project_drawing 39.png" v-else mode=""></image>
				</view>
				<view class="search_right"
					@tap="routerTo('/pages/order_all/ordersearch/ordersearch?id='+store_default_data.id)">
					<image src="../../static/Project_drawing 34.png" mode=""></image>
				</view>
			</view> -->
		</view>
		<view class="hade_location">
			<view class="hade_location_left" @tap="goorderAll()">
				<view class="hade_location_left_top">
					<text>{{showLocationError ?'定位失败':schoolDetail.name}}</text>
					<!-- <text>{{schoolDetail.name}}</text> -->

					<view class="schoolName" v-if="schoolDetail.isPremier">
						<image src="@/static/king.png" mode=""></image>
						<text>旗舰校区</text>
					</view>
					<u-icon name="arrow-right" color='#313131' size="24rpx"></u-icon>
				</view>
				<view class="hade_location_left_down">
					{{schoolDetail.addr||schoolDetail.selectedPlace}}
				</view>
			</view>
		</view>
		<view class="scrollBox">
			<scroll-view class="menu" scroll-x>
				<!-- 修改后的菜单项 -->
				<view :class="['menu-item',activeIndex === index ? 'active' : '']" v-for="(item, index) in menuItems"
					:key="item.id" @tap="setActiveIndex(index)">
					{{ item.name }}
				</view>
			</scroll-view>
		</view>
		<view class="felx">
			<orderlist @aid_mgs='aid_mgs' :activeIndex='components_index' :content='store_infoApi_data'></orderlist>
			<view class="felx_right_box">
				<scroll-view scroll-y="true" style="height: 100%;">
					<view class="right_title">
						<view :class="['title',titleIndex=='-1' ? 'title_active' : '']" @click="changeTitle('-1')">全部
						</view>
						<template v-if="titleList.length">
							<view :class="['title',titleIndex==index ? 'title_active' : '']"
								v-for="(item,index) in titleList" :key="index" @click="changeTitle(index,item.id)">
								{{item.name}}
							</view>
						</template>

					</view>
					<!-- 	:refresher-enabled='true'
						@refresherpulling='refresherpulling' -->
					<view class="felx_right_box_conten">
						<!-- <view class="felx_right_box_conten_img">
							<u-swiper :list="list1" :height='251' @click="swpclick(item.url)"></u-swiper>
						</view> -->
						<!-- <view class="felx_right_box_conten_title">
							{{store_infoApi_data[childindex].name}}
						</view> -->
						<view style="margin-top: 150rpx;" v-if="orderlist.length<1">
							<u-empty mode="data" :iconSize='150' :textSize='24' text='暂无此类产品' icon=""></u-empty>
						</view>
						<ordercard :order_id='order_id' @specification="specification" :content='orderlist'
							:store_id='store_default_data.id' @order='order_carListApi' :activate_data='activate_data'
							@login='enter=true' @addCar="addCar">
						</ordercard>
						<!-- <ordercard2 :order_id='order_id' @specification="specification" :content='orderlist'
							:store_id='store_default_data.id' @order='order_carListApi' :activate_data='activate_data'
							@login='enter=true'>
						</ordercard2> -->
						<uni-load-more v-if="orderlist.length" @clickLoadMore="getMore" :contentText='contentText'
							:status="loadMoreStatus"></uni-load-more>
					</view>
					<height :hg='70'></height>
				</scroll-view>
			</view>
		</view>
		<close v-if="!user.user" :shopping_trolley_list='shopping_trolley_list' @update='order_carListApi'
			@register='register' :isAdd="isAdd" :page-status="pageStatus" @goPay="goPay" :showCart="showCart">
		</close>


		<login :show="enter" @closepage='closepage'></login>
		<movable-area class="movableArea">
			<movable-view class="movableView" direction="all" x="600rpx" y="800rpx">
				<view class="coupn" v-if="counp_show">
					<view class="coupn_icon" @tap="counp_show=false">
						<u-icon name="close-circle-fill" color="#0C0B0B" size="28"></u-icon>
					</view>
					<view class="coupn_img" @tap="routergos('/pages/me_all/coupon_collection/coupon_collection')">
						<image src="../../static/Project_drawing 38.png" mode=""></image>
					</view>
					<view class="coupn_title" @tap="routergos('/pages/me_all/coupon_collection/coupon_collection')">
						<view class="coupn_title_text">
							领券中心
						</view>
						<u-icon name="arrow-right" color="#68150A " size="22"></u-icon>
					</view>
				</view>
			</movable-view>
		</movable-area>
	</view>
</template>

<script>
	import
	Location
	from "@/utils/wxApi.js"
	import {
		store_default,
		store_collection,
		store_cancelColl,
		store_info,
		store_itemPrice,
		store_item,
		store_goodsSearch,
		order_joinCar,
		order_carList,
		order_empty,
		seat,
		treeList,
		getProductList,
		getNearCampus
	} from "@/api/comm.js"
	import {
		forEach
	} from "lodash";
	import {
		cellphone
	} from "@/utils/type_height.js"
	import {
		userInfo
	} from "@/api/public.js"
	export default {
		data() {
			return {
				menuItems: [],
				activeIndex: 0,
				titleList: [],
				titleIndex: '-1',
				addshow: true,
				popshow: false,
				// 胶囊开关
				activate_data: 2,
				// 规格参数
				show_specification: false,
				// 领卷
				counp_show: true,
				list1: [],
				childindex: 0, //侧边栏子组件传过来的索引
				store_default_data: {}, //默认门店
				store_infoApi_data: [], //分类列表
				orderlist: [], //此分类下的商品
				store_id: '', //选择门店后使用选择的门店
				specification_list: [], //规格参数
				order_id: '', //当前规格商品id
				order_type: null, //判断多规格还是单规格
				style: [], //规格动态样式
				orderprive: 0, //价格
				shopping_trolley_list: {}, //购物车列表
				display: false, //页面数据未加载前隐藏
				enter: false, //判断是否登录登录
				System_height: cellphone(), //系统高度
				components_index: 0, //刷新组件索引
				scene: null,
				seatShow: true,
				peopleCur: 0,
				peopleCount: 1,
				people: '',
				addAdish: false,
				option: null,
				activeId: '',
				pageInfo: {
					total: 0,
					page: 1
				},
				loadMoreStatus: 'more',
				contentText: {
					contentdown: "点击加载更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				showLocationError: false, // 控制悬浮提示显示
				locationErrorMsg: "", // 错误信息
				location: {}, //存储定位信息
				schoolDetail: {}, //校区信息
				isAdd: true,
				pageStatus: {
					onShow: false
				},
				showCart: false,
				user: {},
				type: 0,
			};

		},
		onLoad(e) {
			this.orderlist = []
			console.log('e', e)
			if (!e.query) {
				this.getTreeList()
			}
			if (e.query.item) {
				uni.removeStorageSync('cartItems')
				this.type = 1
				this.schoolDetail = JSON.parse(e.query.item)
				// this.getList()
				this.getTreeList()
			}


			if (e.showCart === 'true') {
				this.showCart = true;
			}
			// if (e.scene) {
			// 	let option = uni.getStorageSync('option')
			// 	if (option !== 3) {
			// 		order_empty()
			// 	}
			// 	let bb = e.scene.split('_')
			// 	let cc = bb[1]
			// 	if (cc == '1' || cc == 1) {
			// 		uni.setStorageSync('firstPay', 1)
			// 	} else {
			// 		uni.setStorageSync('firstPay', 2)
			// 	}
			// 	uni.setStorageSync('option', 3)
			// 	uni.setStorageSync('scene', bb[0])
			// 	this.getseat(bb[0])
			// }


			// this.store_defaultApi(1) //默认门店
			// this.getAddress()
		},
		onShow() {
			this.user = uni.getStorageSync('user')
			this.pageStatus.onShow = true
			// this.store_defaultApi() //默认门店
			// let user = uni.getStorageSync('user')
			// let option = uni.getStorageSync('option')
			// let scene = uni.getStorageSync('scene')
			// let firstPay = uni.getStorageSync('firstPay')
			// this.option = option
			// if (user) {
			// 	this.getUser()
			// }
			// if (option == 3) {
			// 	this.seatShow = false
			// 	this.getseat(scene)
			// } else {
			// 	if (scene) {
			// 		uni.removeStorageSync('scene')
			// 	}
			// 	this.seatShow = true
			// }

			// this.enter = false
			// this.order_carListApi() //购物车列表
			// if (option == 2) {
			// 	this.activate_data = 2
			// } else {
			// 	this.activate_data = 1
			// }
			this.orderlist = []
			if (!this.type) {
				this.$forceUpdate()
				this.getAddress()
				this.getlocation()
			}
			//模糊定位

		},
		onHide() {
			this.pageStatus.onShow = false
		},
		methods: {
			// 去结算
			goPay() {
				if (!uni.getStorageSync('user')) {
					this.enter = true
					uni.showToast({
						title: '未登录',
						icon: 'none'
					})
					return
				}
				uni.navigateTo({
					url: `/pages/order_all/affirm_order/affirm_order`
				})
				uni.setStorageSync('campusId', this.schoolDetail.id)
			},
			addCar() {
				this.isAdd = !this.isAdd
			},
			// 根据定位获取校区
			async getAddress() {
				console.log([this.location.longitude, this.location.latitude].join(','))
				const {
					errCode,
					data,
					msg
				} = await getNearCampus({
					coordinate: [this.location.longitude, this.location.latitude].join(',')
					// coordinate: ['117.2832', '31.9386'].join(',')

				})
				if (errCode == 0) {
					this.orderlist = []
					this.showLocationError = false
					this.schoolDetail = data
					this.getList()
				}


			},
			// 获取定位权限
			async getlocation() {
				this.orderlist = []
				this.locationErrorMsg = "";
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						this.location = res;
						console.log('location', this.location)
						this.showLocationError = false;
						setTimeout(() => {
							this.getAddress()
						}, 2000)

					},
					fail: (err) => {
						this.handleLocationError(err);
					}
				})
			},

			// 处理定位错误
			handleLocationError(err) {
				this.showLocationError = true

				// 解析错误类型
				switch (true) {
					case /cancel/.test(err.errMsg):
						this.locationErrorMsg = "您已取消位置选择"
						break;

					case /auth deny|permission/.test(err.errMsg):
						this.locationErrorMsg = "定位权限被拒绝"
						// 引导用户开启权限
						this.showAuthGuide()
						break;

					case /timeout/.test(err.errMsg):
						this.locationErrorMsg = "定位超时，请重试"
						break;

					default:
						this.locationErrorMsg = "定位服务不可用"
				}
			},

			// 显示权限引导
			showAuthGuide() {
				uni.showModal({
					title: '定位权限未开启',
					content: '需要您的位置权限才能获取位置信息，是否前往设置开启？',
					confirmText: '去设置',
					success: (res) => {
						if (res.confirm) {
							// 打开系统设置
							uni.openSetting({
								success: (settingRes) => {
									console.log('设置页面打开成功', settingRes.authSetting);

									// 检查用户是否开启了定位权限
									if (settingRes.authSetting['scope.userLocation']) {
										// 权限已开启，立即获取位置
										this.orderlist = []
										this.getlocation();
									} else {
										// 用户没有开启定位权限
										this.showLocationError = true;
										this.locationErrorMsg = "您仍未开启定位权限";
									}
								},
								fail: (err) => {
									console.error('打开设置页面失败', err);
									this.showLocationError = true;
									this.locationErrorMsg = "无法打开设置页面";
								}
							});
						}
					}
				})
			},
			getMore() {
				if (this.orderlist.length >= this.pageInfo.total) {
					this.loadMoreStatus = "noMore"
					return
				}
				this.pageInfo.page += 1
				this.getList()
			},
			// 获取产品列表
			async getList() {
				const {
					data,
					errCode,
					msg
				} = await getProductList({
					categoryId: this.activeId,
					page: this.pageInfo.page,
					limit: 10,
					// campusId: this.schoolDetail.id || ''
					campusId: this.schoolDetail.id,
				})
				if (errCode == 0) {
					this.orderlist = [...this.orderlist, ...data.data]
					this.pageInfo.total = data.total
					if (this.orderlist.length >= this.pageInfo.total) {
						this.loadMoreStatus = "noMore"
					} else {
						this.loadMoreStatus = "more"
					}
				}
			},
			async getTreeList() {
				const {
					data,
					errCode,
					msg
				} = await treeList()
				if (errCode == 0) {
					this.menuItems = data
					this.store_infoApi_data = this.menuItems[0].children
					this.titleList = this.store_infoApi_data[0].children
					this.activeId = this.store_infoApi_data[0].id
					this.orderlist = []
					if (this.type == 1) {
						this.getList()
					}

				}
			},
			changeTitle(index, id) {
				this.titleIndex = index
				this.pageInfo.page = 1
				this.pageInfo.total = 0
				this.orderlist = []
				if (index == -1) {
					this.activeId = this.this.store_infoApi_data[this.childindex].id
					this.getList()
				} else {
					this.activeId = id
					this.getList()
				}
			},
			setActiveIndex(index) {
				this.activeIndex = index
				this.store_infoApi_data = this.menuItems[index].children || []
				this.components_index = 0
				this.titleList = this.store_infoApi_data.length ? this.store_infoApi_data[this.components_index].children :
					[]
				this.titleIndex = '-1'
				if (this.store_infoApi_data.length) {
					this.activeId = this.store_infoApi_data[0].id
					this.pageInfo.page = 1
					this.total = 0
					this.orderlist = []
					this.getList()
				}
			},
			goorderAll() {
				if (this.showLocationError) return
				let option = uni.getStorageSync('option')
				if (option !== 3) {
					this.routergo('/pages/order_all/selectstore/selectstore')

				}
			},
			async getUser() {
				let user = await userInfo()
				if (user.code == 1) {
					uni.setStorageSync('user', user.data)
				}
			},
			// 获取桌号信息
			async getseat(id) {
				let res = await seat({
					id
				})
				if (res.code == 1) {
					this.scene = res.data
					this.seatShow = false
					uni.setStorageSync('shop', res.data.store_id)
					this.store_defaultApi() //默认门店
				}
			},
			async activate(index) {
				if (this.components_index == index) return
				if (index == 1) {
					this.components_index = 1
				} else {
					this.components_index = 2
				}
				uni.setStorageSync('option', index)
				this.activate_data = index
				this.store_defaultApi()
				// 清空购物车
				let data = await order_empty()
				uni.showToast({
					title: data.msg,
					icon: 'none'
				})
				this.order_carListApi()
			},
			async specification(e) {
				this.order_id = e.e
				this.order_type = e.type
				console.log('eeee', this.order_type);
				setTimeout(() => {
					this.$refs.specification.specification()
					this.show_specification = true
				}, 500)
			},
			aid_mgs(e, index) {
				this.childindex = index
				this.components_index = index
				this.titleList = this.store_infoApi_data[index].children || []
				this.titleIndex = '-1'
				// this.orderlist = []
				// this.orderlist.push(...e)
				this.pageInfo.page = 1
				this.pageInfo.total = 0
				this.activeId = e.id
				this.orderlist = []
				this.getList()
			},
			swpclick(url) {
				uni.navigateTo({
					url: url
				})
			},

			async store_defaultApi(e) {
				let shop = uni.getStorageSync('shop')
				let data = await store_default({
					id: shop || ''
				})
				this.store_default_data = data.data
				if (data.data.takeaway_switch == 1 && data.data.delivery_switch !== 1) {
					this.activate_data = 2
				}
				this.list1 = data.data.images
				if (e == 1) {
					this.store_infoApi() //门店下的分类
				}
			},
			async store_collectionApi(comtent) {
				if (!uni.getStorageSync('userinfo')) {
					this.enter = true
					uni.showToast({
						title: '未登录',
						icon: 'none'
					})
					return
				} else {
					if (comtent) {
						let data = await store_cancelColl({
							store_id: this.store_default_data.id
						})
						if (data == 1) {
							// uni.showToast({
							// 	title: data.msg,
							// 	icon: 'success'
							// })
						} else {
							// uni.showToast({
							// 	title: data.msg,
							// 	icon: 'error'
							// })
						}
					} else {
						let data = await store_collection({
							store_id: this.store_default_data.id
						})
						if (data == 1) {
							// uni.showToast({
							// 	title: data.msg,
							// 	icon: 'success'
							// })
						} else {
							// uni.showToast({
							// 	title: data.msg,
							// 	icon: 'error'
							// })
						}
					}
					this.store_defaultApi()
					this.order_carListApi()
				}
			},
			async store_infoApi() {
				let data = await store_info({
					store_id: this.store_default_data.id,
					shipment: this.activate_data || 2
				})
				this.store_infoApi_data = []
				this.store_infoApi_data.push(...data.data)
				this.orderlist = data.data[0].goods
				this.display = true
			},

			async selective_specification(index, id, name) {

				this.$set(this.style[index], 'id', id)
				this.$set(this.style[index], 'name', name)
				this.store_itemPriceApi()
			},
			// 价格计算
			async store_itemPriceApi() {
				let str = ''
				this.style.forEach(res => {
					str += res.id + '_'
				})
				str = str.substring(0, str.length - 1)
				let data = await store_itemPrice({
					goods_id: this.order_id,
					spu_id: str
				})
				this.orderprive = Location.BumberPrecision(data.data.price)
			},
			// 添加购物车
			async add_joinCar() {
				if (!uni.getStorageSync('userinfo')) {
					this.enter = true
					return
				}
				let str = ''
				this.style.forEach(res => {
					str += res.id + '_'
				})
				str = str.substring(0, str.length - 1)
				let data = await order_joinCar({
					store_id: this.store_default_data.id,
					goods_id: this.order_id,
					spu_id: str,
					count: 1,
					order_type: this.activate_data

				})
				if (data.code == 1) {
					uni.showToast({
						title: data.msg,
						icon: "none"
					})
					this.order_carListApi()
					this.show_specification = false
					this.style = []
				} else {
					uni.showToast({
						title: data.msg,
						icon: "none"
					})
				}
			},
			// 购物车列表
			async order_carListApi() {
				let token = uni.getStorageSync('userinfo').token
				if (!token) {
					this.shopping_trolley_list = []
					return
				}
				let data = await order_carList()
				if (data.code == 1) {
					this.shopping_trolley_list = data.data
				} else {
					this.shopping_trolley_list = []
				}

			},
			//结算前判断是否登录
			register() {
				this.enter = true
			},
			router_close(url) {
				uni.reLaunch({
					url: url
				})
			},
			routergo(url) {
				uni.navigateTo({
					url: url
				})
			},
			routergos(url) {
				let token = uni.getStorageSync('TOKEN')
				let user = uni.getStorageSync('user')
				if (token && user) {
					uni.navigateTo({
						url: url
					})
				} else {
					this.enter = true
				}
			},
			//未登录关闭弹出层需要关掉组件
			closepage() {
				this.enter = false
			},
			refresherpulling() {

			}
		}
	}
</script>

<style>
	page {
		background: #FFFFFF;
	}
</style>
<style lang="scss" scoped>
	/* 定位失败悬浮提示样式 */
	.location-error-toast {
		position: fixed;
		top: 200rpx;
		width: 690rpx;
		left: 30rpx;
		background-color: rgba(0, 0, 0, 0.6);
		color: white;
		padding: 15rpx 20rpx;
		box-sizing: border-box;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		z-index: 9999;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
		animation: fadeIn 0.3s ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			top: 0;
		}

		to {
			opacity: 1;
			top: 20rpx;
		}
	}

	.error-text {
		font-size: 28rpx;
		margin-right: 15rpx;
	}

	.retry-btn {
		background-color: #00C2A0;
		color: white;
		font-size: 12px;
		padding: 4px 12px;
		border-radius: 4px;
		margin: 0;
	}

	.movableArea {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		pointer-events: none; //设置area元素不可点击，则事件便会下移至页面下层元素
	}

	.movableView {
		pointer-events: auto; //可以点击
	}

	.scrollBox {
		padding: 0rpx 30rpx;
		box-sizing: border-box;
		height: 80rpx;
		border-bottom: 1rpx solid #F4F4F4;

		.menu {
			display: flex;
			align-items: center;
			// white-space: nowrap;
			scrollbar-width: none;
			-ms-overflow-style: none;

			&::-webkit-scrollbar {
				display: none;
			}
		}

		.menu-item {
			display: inline-block;
			// 适当减小外边距
			// width: 195rpx;
			height: 80rpx;
			// line-height: 46rpx;
			text-align: center;
			// font-weight: bold;
			// padding-bottom: 5rpx;
			font-size: 30rpx;
			color: #414141;
			margin-right: 30rpx;
			transition: all 0.3s ease;

			&:last-child {
				margin-right: 0;
			}


		}

		.active {
			color: #00C2A0;
			font-size: 30rpx;
			font-weight: bold;
			position: relative;

			&::after {
				content: '';
				position: absolute;
				z-index: 99;
				bottom: 20rpx;
				left: 0;
				width: 100%;
				height: 6rpx;
				border-radius: 50rpx;
				background-color: #00C2A0;
			}
		}
	}

	.search {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.search_left {
			font-size: 32rpx;
			font-weight: 600;
			color: #05B6F6;
			padding-left: 30rpx;

			image {
				width: 206rpx;
				height: 58rpx;
			}
		}

		.search_right {
			width: 62rpx;
			height: 62rpx;
			padding: 0 10rpx;
		}
	}

	.hade_location {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 0 30rpx;

		.tag {
			height: 39rpx;
			border-radius: 4rpx;
			border: 1rpx solid #05B6F6;
			font-size: 24rpx;
			font-weight: 400;
			color: #05B6F6;
			text-align: center;
			box-sizing: border-box;
			padding: 0 10rpx;
			margin-right: 10rpx;
			line-height: 37rpx;
		}

		.hade_location_left {
			margin-left: 20rpx;

			.hade_location_left_top {
				display: flex;
				align-items: center;
				font-size: 32rpx;
				font-weight: 600;
				color: #313131;

				text {
					-webkit-line-clamp: 1; //设置几行
					display: -webkit-box; //设置为伸缩盒弹性盒子展示
					overflow: hidden; //超出隐藏
					text-overflow: ellipsis; //设置超出部分以省略号展示
					-webkit-box-orient: vertical; //伸缩盒弹性盒子的排列方式
				}

				.schoolName {
					height: 45rpx;
					display: flex;
					align-items: center;
					font-weight: bold;
					font-size: 22rpx;
					color: #BE5C2D;
					padding: 5rpx 10rpx 5rpx 5rpx;
					background: #FFF9E2;
					border-radius: 12rpx;

					image {
						width: 36rpx;
						height: 30rpx;
						margin-right: 5rpx;
					}
				}
			}

			.hade_location_left_down {
				font-size: 24rpx;
				font-weight: 400;
				color: #666A6B;
				margin-top: 5rpx;
			}
		}

		.hade_location_right {
			// width: 180rpx;
			height: 63rpx;
			background: #F4F4F4;
			border-radius: 32rpx;
			margin-right: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.hade_location_right_content {
				width: 90rpx;
				height: 63rpx;
				text-align: center;
				line-height: 63rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: #949494;
			}

			.hade_location_right_content_activate {
				background-color: #05B6F6;
				color: #FDFEFF;
				border-radius: 32rpx;
			}
		}
	}

	.felx {
		display: flex;
		justify-content: space-between;

		.felx_right_box {
			width: 571rpx;
			height: 67vh;

			.right_title {
				padding: 20rpx 20rpx 0 20rpx;
				display: flex;
				flex-wrap: wrap;

				.title {
					width: 162rpx;
					height: 62rpx;
					line-height: 62rpx;
					text-align: center;
					font-size: 26rpx;
					color: #414141;
					background: #F6F7FB;
					border-radius: 8rpx;
					margin-right: 20rpx;
					margin-bottom: 20rpx;

					&:nth-child(3n+3) {
						margin-right: 0;
					}
				}

				.title_active {
					font-weight: bold;
					color: #00C2A0;
					background: #DCF6ED;
				}
			}

			.felx_right_box_conten {
				width: 522rpx;
				margin: 0 auto;

				.felx_right_box_conten_img {
					width: 525rpx;
					border-radius: 16rpx;
					overflow: hidden;
				}

				.felx_right_box_conten_title {
					font-size: 26rpx;
					font-weight: 600;
					color: #353535;
					padding: 30rpx 0 30rpx 10rpx;
				}
			}
		}
	}

	.specification {
		width: 688rpx;
		background: #FFFFFF;
		border-radius: 12rpx;
		position: relative;
		z-index: 10074;

		.specification_title {
			text-align: center;
			font-size: 32rpx;
			font-weight: 600;
			color: #353535;
			margin: 0 auto;
		}

		.specification_title_1 {
			width: 95%;
			height: 504rpx;
			margin: 0 auto;

			.specification_title_1_title {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #676767;
				display: inline-block;
				padding: 20rpx 0;

			}

			.specification_title_1_content {
				display: flex;
				overflow-x: auto;
				display: -webkit-box;
				-webkit-overflow-scrolling: touch;

				.specification_title_1_content_flex_activate {
					background: #F4FCFF !important;
					border: 1rpx solid #00B8FB !important;
					color: #00B8FB !important;
				}

				.font_sizi_1 {
					color: #00B8FB;
				}

				.font_sizi_2 {
					border-left: 1rpx solid #00B8FB;
				}

				.specification_title_1_content_flex {
					height: 63rpx;
					background: #FFFFFF;
					border-radius: 14rpx;
					border: 1rpx solid #F1F1F1;
					margin-right: 20rpx;
					text-align: center;
					line-height: 63rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #343434;
					padding: 0 40rpx;
				}
			}
		}

		.close {
			position: absolute;
			bottom: -150rpx;
			left: 50%;
			transform: translateX(-50%);
		}
	}

	.selected {
		width: 688rpx;
		padding: 20rpx 0;
		background: #F5F5F5;
		margin-top: 60rpx;

		text {
			font-size: 24rpx;
			font-weight: 400;
			color: #363636;
		}

		text:nth-child(1) {
			font-size: 24rpx;
			font-weight: 400;
			color: #676767;
			padding: 0 20rpx;
			margin-left: 10rpx;
		}
	}

	.sublist {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx 40rpx;

		.sublist_left {
			text:nth-child(1) {
				font-size: 28rpx;
				font-weight: 600;
				color: #363636;
			}

			text:nth-child(2) {
				font-size: 28rpx;
				font-weight: 600;
				color: #FF4000;

				text:nth-child(1) {
					font-size: 28rpx;
					font-weight: 600;
					color: #FF4000;
					font-size: 24rpx;
				}
			}
		}

		.sublist_right {
			width: 234rpx;
			height: 62rpx;
			background: #02B6FD;
			border-radius: 12rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #F3FCFF;
			line-height: 62rpx;
			text-align: center;
		}
	}

	.coupn {
		.coupn_icon {
			width: 24rpx;
			height: 24rpx;
			margin-left: 100rpx;
			margin-bottom: 10rpx;
			opacity: 0.5;
		}

		.coupn_img {
			width: 130rpx;
			height: 121rpx;
		}

		.coupn_title {
			width: 120rpx;
			padding: 5rpx 10rpx;
			background: #FEDD5B;
			border-radius: 18rpx;
			display: flex;
			align-items: center;
			position: relative;
			bottom: 30rpx;

			.coupn_title_text {
				font-size: 22rpx;
				font-weight: 400;
				color: #631407;
				margin-left: 10rpx;
			}
		}
	}
</style>