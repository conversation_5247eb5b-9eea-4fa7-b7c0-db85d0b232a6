{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/index/index.vue?21b9", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/index/index.vue?00ff", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/index/index.vue?5760", "uni-app:///subpkg/select_school/index/index.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/index/index.vue?f379", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/select_school/index/index.vue?0bc9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "enter", "reportList", "id", "name", "status", "description", "createTime", "computed", "onLoad", "methods", "back", "uni", "checkLogin", "closepage", "generateReport", "url", "viewReport", "console", "viewDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC6FnnB;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC,aACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAC,4BACA,uCACA;EACAC;IACA;EACA;EACAC;IACAC;MACAC;IACA;IACAC;MACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAH;QACAI;MACA;IACA;IACAC;MACA;MACAC;IACA;IACAC;MACA;MACAD;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtJA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "subpkg/select_school/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg/select_school/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=aa0cedb0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=aa0cedb0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"aa0cedb0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg/select_school/index/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=aa0cedb0&scoped=true&\"", "var components\ntry {\n  components = {\n    height: function () {\n      return import(\n        /* webpackChunkName: \"components/height/height\" */ \"@/components/height/height.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n    login: function () {\n      return import(\n        /* webpackChunkName: \"components/login/login\" */ \"@/components/login/login.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"content\">\n    <height :hg=\"System_height\"></height>\n    <view class=\"content_header\">\n      <view class=\"nav-title\" :style=\"{ top: System_height + 'rpx' }\">\n        <text>考研择校</text>\n      </view>\n      <uni-icons\n        type=\"left\"\n        size=\"24\"\n        color=\"#fff\"\n        class=\"back-left\"\n        :style=\"{ top: System_height + 'rpx' }\"\n        @tap=\"back\"\n      ></uni-icons>\n      <image\n        src=\"/static/select_school/top_bg.png\"\n        mode=\"aspectFit\"\n        class=\"header-image\"\n      ></image>\n    </view>\n\n    <view class=\"ai-section\">\n      <view class=\"ai-container\">\n        <view class=\"ai-images\">\n          <image\n            src=\"/static/select_school/ai4-56586a.png\"\n            class=\"ai-bg-4\"\n          ></image>\n          <image\n            src=\"/static/select_school/ai1-56586a.png\"\n            class=\"ai-bg-1\"\n          ></image>\n          <image\n            src=\"/static/select_school/ai2-56586a.png\"\n            class=\"ai-bg-2\"\n          ></image>\n          <view class=\"ai-center\">\n            <image\n              src=\"/static/select_school/ai-56586a.png\"\n              class=\"ai-main\"\n            ></image>\n            <image\n              src=\"/static/select_school/ai_text-56586a.png\"\n              class=\"ai-text\"\n            ></image>\n          </view>\n        </view>\n        <view class=\"generate-btn\" @click=\"generateReport\">\n          <text>点击生成报告</text>\n        </view>\n        <view class=\"usage-count\">可用次数：2</view>\n      </view>\n    </view>\n\n    <view class=\"report-section\">\n      <view class=\"section-title\">查看报告</view>\n      <view class=\"report-list\">\n        <view\n          class=\"report-item\"\n          v-for=\"(report, index) in reportList\"\n          :key=\"index\"\n          @click=\"viewReport(report)\"\n        >\n          <view class=\"report-header\">\n            <view class=\"report-name\">{{ report.name }}</view>\n            <view\n              class=\"report-status\"\n              :class=\"\n                report.status === '已生成'\n                  ? 'status-completed'\n                  : 'status-pending'\n              \"\n            >\n              {{ report.status }}\n            </view>\n          </view>\n          <view class=\"report-desc\">{{ report.description }}</view>\n          <view class=\"report-footer\">\n            <view class=\"report-time\">生成时间：{{ report.createTime }}</view>\n            <view class=\"report-detail\" @click.stop=\"viewDetail(report)\"\n              >查看详情</view\n            >\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <login :show=\"enter\" @closepage=\"closepage\"></login>\n  </view>\n</template>\n\n<script>\nimport { mapState } from \"vuex\";\nexport default {\n  data() {\n    return {\n      enter: false,\n      reportList: [\n        {\n          id: 1,\n          name: \"AI考研择校报告1\",\n          status: \"已生成\",\n          description: \"报告包含专业分析、院校分析、学习计划等针对性综合分析\",\n          createTime: \"2025年8月23日\",\n        },\n        {\n          id: 2,\n          name: \"AI考研择校报告2\",\n          status: \"已生成\",\n          description: \"报告包含专业分析、院校分析、学习计划等针对性综合分析\",\n          createTime: \"2025年8月24日\",\n        },\n      ],\n    };\n  },\n  computed: {\n    ...mapState([\"System_height\"]),\n  },\n  onLoad() {\n    this.checkLogin();\n  },\n  methods: {\n    back() {\n      uni.navigateBack();\n    },\n    checkLogin() {\n      const token = uni.getStorageSync(\"token\");\n      if (!token) {\n        this.enter = true;\n      }\n    },\n    closepage() {\n      this.enter = false;\n    },\n    generateReport() {\n      // 跳转到填写报告第一步\n      uni.navigateTo({\n        url: \"/subpkg/select_school/form_step1/form_step1\",\n      });\n    },\n    viewReport(report) {\n      // 查看报告详情\n      console.log(\"查看报告:\", report);\n    },\n    viewDetail(report) {\n      // 查看详情\n      console.log(\"查看详情:\", report);\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.content {\n  min-height: 100vh;\n  background: #ffffff;\n}\n\n.content_header {\n  position: relative;\n  height: 489rpx;\n  background: linear-gradient(180deg, #00c2a0 0%, #97d9c8 100%);\n\n  .nav-title {\n    position: absolute;\n    left: 50%;\n    transform: translateX(-50%);\n    z-index: 10;\n\n    text {\n      font-size: 34rpx;\n      color: #ffffff;\n      font-weight: 400;\n    }\n  }\n\n  .back-left {\n    position: absolute;\n    left: 30rpx;\n    z-index: 10;\n  }\n\n  .header-image {\n    position: absolute;\n    right: 30rpx;\n    top: 140rpx;\n    width: 278rpx;\n    height: 278rpx;\n  }\n}\n\n.ai-section {\n  padding: 60rpx 30rpx;\n  background: #f6f7fb;\n\n  .ai-container {\n    position: relative;\n\n    .ai-images {\n      position: relative;\n      height: 598rpx;\n      margin-bottom: 60rpx;\n\n      .ai-bg-4 {\n        position: absolute;\n        top: 0;\n        left: 159rpx;\n        width: 432rpx;\n        height: 427rpx;\n        z-index: 1;\n      }\n\n      .ai-bg-1 {\n        position: absolute;\n        top: 17rpx;\n        left: 175rpx;\n        width: 400rpx;\n        height: 398rpx;\n        z-index: 2;\n      }\n\n      .ai-bg-2 {\n        position: absolute;\n        top: 90rpx;\n        left: 180rpx;\n        width: 387rpx;\n        height: 328rpx;\n        z-index: 3;\n      }\n\n      .ai-center {\n        position: absolute;\n        top: 135rpx;\n        left: 253rpx;\n        width: 240rpx;\n        height: 240rpx;\n        z-index: 4;\n\n        .ai-main {\n          width: 100%;\n          height: 100%;\n        }\n\n        .ai-text {\n          position: absolute;\n          top: 61rpx;\n          left: 31rpx;\n          width: 178rpx;\n          height: 121rpx;\n        }\n      }\n    }\n\n    .generate-btn {\n      width: 521rpx;\n      height: 78rpx;\n      background: linear-gradient(135deg, #26c8ac 0%, #19c990 100%);\n      border-radius: 39rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin: 0 auto 20rpx;\n\n      text {\n        font-size: 28rpx;\n        color: #ffffff;\n        font-weight: 400;\n      }\n    }\n\n    .usage-count {\n      text-align: center;\n      font-size: 26rpx;\n      color: #989898;\n    }\n  }\n}\n\n.report-section {\n  padding: 0 30rpx 60rpx;\n\n  .section-title {\n    font-size: 30rpx;\n    color: #060606;\n    margin-bottom: 30rpx;\n    padding: 30rpx 0;\n    position: relative;\n\n    &::after {\n      content: \"\";\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      width: 120rpx;\n      height: 19rpx;\n      background: #dbff9c;\n    }\n  }\n\n  .report-list {\n    .report-item {\n      background: #ffffff;\n      border-radius: 16rpx;\n      padding: 28rpx;\n      margin-bottom: 30rpx;\n      box-shadow: 0px 8px 8px 0px rgba(235, 235, 235, 1);\n\n      .report-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 20rpx;\n\n        .report-name {\n          font-size: 34rpx;\n          color: #00c2a0;\n          font-weight: 400;\n        }\n\n        .report-status {\n          padding: 8rpx 20rpx;\n          border-radius: 28rpx;\n          font-size: 28rpx;\n\n          &.status-completed {\n            background: #d6ffec;\n            color: #00c2a0;\n          }\n\n          &.status-pending {\n            background: #fff3e0;\n            color: #ff9800;\n          }\n        }\n      }\n\n      .report-desc {\n        font-size: 28rpx;\n        color: #5a5a5a;\n        line-height: 1.5;\n        margin-bottom: 20rpx;\n      }\n\n      .report-footer {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n\n        .report-time {\n          font-size: 26rpx;\n          color: #818181;\n        }\n\n        .report-detail {\n          font-size: 26rpx;\n          color: #00c2a0;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=aa0cedb0&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=aa0cedb0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754561053240\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}