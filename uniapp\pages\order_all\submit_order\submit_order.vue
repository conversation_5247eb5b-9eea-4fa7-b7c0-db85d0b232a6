<template>
	<view class="container">


		<view class="content">
			<image class="right"
				src="https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/right.png" mode="">
			</image>
			<text class="success">支付成功</text>
			<!-- <view class="btn-box" v-if="show">
				<view class="btn" @click="againClick">继续购买</view>
				<view class="btn" @click="openClick">去开课</view>
			</view> -->
			<view class="btn-box">
				<view class="btns" @click="backClick">返回</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: true
			}
		},
		created() {

		},
		onLoad(option) {
			// this.show = option.show == 1 ? false : true
		},
		methods: {
			backClick() {
				uni.reLaunch({
					url: '/pages/order/order'
				})
			},
			againClick() {
				// uni.navigateTo({
				// 	url: '/subpkg/product_library/product_library'
				// })
			},
			openClick() {
				// uni.navigateTo({
				// 	url: '/subpkg/open_lesson/open_lesson'
				// })
			}
		}
	}
</script>

<style lang="scss">
	.container {


		.header {
			margin-bottom: 20rpx;
			background-color: #fff;

			/* 移除原有的背景色，使用容器的背景色 */
			.nav-bar {
				width: 100vw;
				height: 88rpx;
				padding: 0 30rpx;
				display: flex;
				align-items: center;
				position: relative;
				color: #fff;


				.back-icon {
					position: absolute;
					left: 30rpx;
				}

				.list {
					font-weight: 600;
				}

				.title {
					flex: 1;
					text-align: center;
					font-weight: bold;
					font-size: 34rpx;
					color: #2D2D2D;
				}
			}
		}

		.content {

			background: #fff;
			height: 100vh;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.right {
			width: 350rpx;
			height: 314rpx;
			margin-top: 170rpx;
		}

		.success {
			font-weight: bold;
			font-size: 34rpx;
			color: #404040;
			margin: 70rpx 0;
		}

		.btn-box {
			display: flex;
			align-items: center;
			justify-content: center;

			.btns {
				width: 190rpx;
				height: 66rpx;
				line-height: 66rpx;
				text-align: center;
				font-weight: bold;
				font-size: 30rpx;
				color: #1BB394;
				border-radius: 40rpx;
				border: 1rpx solid #1BB394;
			}

			.btn {
				width: 204rpx;
				height: 66rpx;
				line-height: 66rpx;
				text-align: center;
				font-weight: bold;
				font-size: 30rpx;
				color: #1BB394;
				border-radius: 40rpx;
				border: 1rpx solid #1BB394;

				&:first-child {
					margin-right: 170rpx;
				}
			}
		}
	}
</style>