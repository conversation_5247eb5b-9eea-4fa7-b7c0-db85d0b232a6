{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/personage/personage.vue?99a5", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/personage/personage.vue?88c0", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/personage/personage.vue?36ea", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/personage/personage.vue?30d6", "uni-app:///pages/me_all/personage/personage.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/personage/personage.vue?8db0", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/personage/personage.vue?5856"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "int", "name", "cell", "radiolist1", "disabled", "radiovalue1", "show", "value1", "birthday", "img", "logincode", "onLoad", "uni", "title", "icon", "onShow", "computed", "count", "methods", "groupChange", "console", "radioChange", "time", "<PERSON><PERSON><PERSON>", "uploadImg", "sub", "username", "mobile", "gender", "file", "user", "duration", "success", "setTimeout", "url", "getphonenumber", "binding", "index", "loginApi", "code", "e", "cut"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,uVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzEA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACmGvnB;AAGA;AAGA;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAOA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QACAF;QACAG;MACA,GACA;QACAH;QACAG;MACA,EACA;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;MACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;EACA;EACAC;IACA;MACA;MACAH;QACAC;QACAC;MACA;MACA;IACA;MACA;IACA;EACA;EACAE;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IACAC;MACAD;IACA;IACAE;MACAF;MACA;MACAA;MACA;IACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;gBACA;gBAAA;gBAAA,OACAI;cAAA;gBAAAzB;gBACAqB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAL;gBAAA;gBAAA,OACA;kBACAM;kBACAC;kBACAnB;kBACAoB;kBACAC;gBACA;cAAA;gBANA9B;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACAa;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAgB;gBACAlB;gBACAA;kBACAC;kBACAC;kBACAiB;kBACAC;oBACAC;sBACArB;wBACAsB;sBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAtB;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAqB;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBACA;gBACAC;gBACAA;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBAAAzC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA+B;gBACAlB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6B;MACA7B;MACAA;QACAsB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvQA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/me_all/personage/personage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/me_all/personage/personage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./personage.vue?vue&type=template&id=2303e769&\"\nvar renderjs\nimport script from \"./personage.vue?vue&type=script&lang=js&\"\nexport * from \"./personage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./personage.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/me_all/personage/personage.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./personage.vue?vue&type=template&id=2303e769&\"", "var components\ntry {\n  components = {\n    height: function () {\n      return import(\n        /* webpackChunkName: \"components/height/height\" */ \"@/components/height/height.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio/u-radio\" */ \"uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = Number(new Date())\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.show = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.show = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./personage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./personage.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"hade_vip\" v-if=\"!int.openid\">\r\n\t\t\t完善资料特权\r\n\t\t</view>\r\n\t\t<view class=\"content_box\">\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<button class=\"content_img\" open-type=\"chooseAvatar\" @chooseavatar=\"chooseavatar\">\r\n\t\t\t\t\t\t<image :src=\"img\" mode=\"\"></image>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t\t<height :hg=\"80\"></height>\r\n\t\t\t\t<view class=\"content_input\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t昵称\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<input type=\"nickname\" v-model=\"name\" placeholder=\"请填写\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content_input\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t手机号\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<input style=\"color: #000000;\" type=\"text\" v-model=\"cell\" placeholder=\"请填写手机号\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content_input\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t性别\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<u-radio-group v-model=\"radiovalue1\" :iconSize='20' :labelSize='50' :size='30' placement=\"row\"\r\n\t\t\t\t\t\t\t@change=\"groupChange\">\r\n\t\t\t\t\t\t\t<u-radio :customStyle=\"{marginBottom: '8px'}\" v-for=\"(item, index) in radiolist1\"\r\n\t\t\t\t\t\t\t\t:key=\"index\" :label=\"item.name\" :name=\"item.name\" @change=\"radioChange\">\r\n\t\t\t\t\t\t\t</u-radio>\r\n\t\t\t\t\t\t</u-radio-group>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content_input\" style=\"border-bottom: none;\" @tap=\"show=true\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t生日\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"display: flex; align-items: center;\">\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t{{birthday||'请选择生日'}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#303030\" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"account\">\r\n\t\t\t<view class=\"content_input\" style=\"border-bottom: none; padding: 10rpx 0;\">\r\n\t\t\t\t<view style=\"width: 80rpx; height: 80rpx;\">\r\n\t\t\t\t\t<image src=\"../../../static/WeChat.png\" mode=\"\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"display: flex; align-items: center;\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<!-- {{count}} -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<text v-if=\"int.openid\" @tap=\"binding(0)\">已绑定</text>\r\n\t\t\t\t\t\t<button v-else open-type=\"getPhoneNumber\" @getphonenumber=\"getphonenumber\">\r\n\t\t\t\t\t\t\t<text>未绑定</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- \t<view class=\"account\" @tap=\"cut\">\r\n\t\t\t<view class=\"content_input\" style=\"border-bottom: none; \">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t退出登录\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"display: flex; align-items: center;\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#303030\" size=\"28\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t<view class=\"save\" @tap=\"sub\">\r\n\t\t\t保存\r\n\t\t</view>\r\n\t\t<u-datetime-picker :show=\"show\" minDate=\"\" :maxDate=\"Number(new Date())\" @cancel=\"show=false\"\r\n\t\t\t@close='show=false' @confirm='time' v-model=\"value1\" mode=\"date\"></u-datetime-picker>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\ttoDate\r\n\t} from \"@/utils/time.js\"\r\n\timport\r\n\tuploadImg\r\n\tfrom \"@/utils/wxApi.js\"\r\n\timport {\r\n\t\tuser_profile,\r\n\t\tuserInfo\r\n\t} from \"@/api/public.js\"\r\n\timport {\r\n\t\tuser_remove,\r\n\t\tuser_binding\r\n\t} from \"@/api/user.js\"\r\n\timport\r\n\tloginApi\r\n\tfrom '@/utils/wxApi.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tint: {},\r\n\t\t\t\tname: '', //用户名\r\n\t\t\t\tcell: '', //电话号\r\n\t\t\t\tradiolist1: [{\r\n\t\t\t\t\t\tname: '男',\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '女',\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t// u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中\r\n\t\t\t\tradiovalue1: '男',\r\n\t\t\t\t// 生日\r\n\t\t\t\tshow: false,\r\n\t\t\t\tvalue1: Number(new Date()),\r\n\t\t\t\tbirthday: '请选择',\r\n\t\t\t\timg: '', //头像\r\n\t\t\t\tlogincode: ''\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// loginApi.loginApi().then(res => {\r\n\t\t\t// \tthis.logincode = res.code\r\n\t\t\t// })\r\n\t\t\tlet user = uni.getStorageSync('user') //初始化信息\r\n\t\t\tthis.int = user\r\n\t\t\tthis.name = user.username\r\n\t\t\tthis.cell = user.mobile\r\n\t\t\tthis.radiovalue1 = user.mobile = 0 ? '女' : '男'\r\n\t\t\tthis.birthday = user.birthday\r\n\t\t\tthis.img = user.avatar\r\n\r\n\t\t\tif (!uni.getStorageSync('user')) {\r\n\t\t\t\tthis.enter = true\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '未登录',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tif (!uni.getStorageSync('user')) {\r\n\t\t\t\tthis.enter = true\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '未登录',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t} else {\r\n\t\t\t\tthis.enter = false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tcount() {\r\n\t\t\t\tif (this.value1) {\r\n\t\t\t\t\treturn toDate(this.value1, 2)\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn '请选择'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgroupChange(n) {\r\n\t\t\t\tconsole.log('groupChange', n);\r\n\t\t\t},\r\n\t\t\tradioChange(n) {\r\n\t\t\t\tconsole.log('radioChange', n);\r\n\t\t\t},\r\n\t\t\ttime(time) {\r\n\t\t\t\tconsole.log(time);\r\n\t\t\t\tthis.birthday = toDate(time.value, 2)\r\n\t\t\t\tconsole.log(this.value1);\r\n\t\t\t\tthis.show = false\r\n\t\t\t},\r\n\t\t\tasync chooseavatar(e) {\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t\tthis.img = e.detail.avatarUrl\r\n\t\t\t\tlet data = await uploadImg.uploadImg(this.img)\r\n\t\t\t\tconsole.log(data, '返回的网络图片');\r\n\t\t\t\tthis.img = data\r\n\t\t\t},\r\n\t\t\tasync sub() {\r\n\t\t\t\tconsole.log(this.img);\r\n\t\t\t\tlet data = await user_profile({\r\n\t\t\t\t\tusername: this.name,\r\n\t\t\t\t\tmobile: this.cell,\r\n\t\t\t\t\tbirthday: this.birthday,\r\n\t\t\t\t\tgender: this.radiovalue = '女' ? 0 : 1,\r\n\t\t\t\t\tfile: this.img\r\n\t\t\t\t})\r\n\t\t\t\tif (data.code == 1) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: data.msg,\r\n\t\t\t\t\t\ticon: \"success\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet user = await userInfo()\r\n\t\t\t\t\tuni.setStorageSync('user', user.data)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: data.msg,\r\n\t\t\t\t\t\ticon: \"success\",\r\n\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\tsetTimeout(res => {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/me/me'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}, 2000)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: data.msg,\r\n\t\t\t\t\t\ticon: \"error\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetphonenumber(e) {\r\n\t\t\t\tthis.binding(1, e.detail)\r\n\t\t\t},\r\n\t\t\tasync binding(index, e) {\r\n\t\t\t\tif (index) {\r\n\t\t\t\t\tlet code = await loginApi.loginApi()\r\n\t\t\t\t\tthis.logincode = code.code\r\n\t\t\t\t\te.code = this.logincode\r\n\t\t\t\t\te.iv = encodeURIComponent(e.iv)\r\n\t\t\t\t\te.encryptedData = encodeURIComponent(e.encryptedData)\r\n\t\t\t\t\tlet data = await user_binding(e)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet data = await user_remove()\r\n\t\t\t\t}\r\n\t\t\t\tlet user = await userInfo()\r\n\t\t\t\tuni.setStorageSync('user', user.data)\r\n\t\t\t\tthis.int = user.data\r\n\t\t\t},\r\n\t\t\tcut() {\r\n\t\t\t\tuni.clearStorageSync()\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.u-radio {\r\n\t\tmargin-right: 20rpx;\r\n\t\tmargin-bottom: 0 !important;\r\n\t}\r\n\r\n\t.u-radio text {\r\n\t\tfont-size: 30rpx !important;\r\n\t}\r\n\r\n\t.hade_vip {\r\n\t\theight: 73rpx;\r\n\t\tbackground: #F1FBFF;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 400;\r\n\t\tcolor: #00C8FF;\r\n\t\tline-height: 73rpx;\r\n\t\ttext-indent: 2em;\r\n\t}\r\n\r\n\t.content_box {\r\n\t\twidth: 705rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 16rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tmargin-top: 100rpx;\r\n\r\n\t\t.content {\r\n\t\t\twidth: 90%;\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t.content_img {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\ttop: -80rpx;\r\n\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\twidth: 138rpx;\r\n\t\t\t\theight: 138rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.content_input {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tpadding: 30rpx 0;\r\n\t\t\t\tborder-bottom: 1rpx solid #eaeaea42;\r\n\r\n\r\n\t\t\t\tview:nth-child(1) {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tview:nth-child(2) {\r\n\t\t\t\t\tinput {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #979797;\r\n\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.account {\r\n\t\twidth: 705rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 16rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tmargin-top: 10rpx;\r\n\r\n\t\t.content_input {\r\n\t\t\twidth: 90%;\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding: 30rpx 0;\r\n\t\t\tborder-bottom: 1rpx solid #EAEAEA;\r\n\r\n\t\t\tview:nth-child(1) {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t}\r\n\r\n\t\t\tview:nth-child(2) {\r\n\t\t\t\tinput {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #979797;\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.save {\r\n\t\twidth: 704rpx;\r\n\t\theight: 102rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tbackground: #00CCFF;\r\n\t\tborder-radius: 9rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 400;\r\n\t\tcolor: #F6FDFF;\r\n\t\tline-height: 102rpx;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 80rpx;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./personage.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./personage.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557559335\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}