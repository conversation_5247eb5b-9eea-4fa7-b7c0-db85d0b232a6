<view class="u-number-box data-v-18418972"><block wx:if="{{showMinus&&$slots.minus}}"><view data-event-opts="{{[['tap',[['clickHandler',['minus']]]],['touchstart',[['onTouchStart',['minus']]]],['touchend',[['clearTimeout',['$event']]]]]}}" class="u-number-box__slot data-v-18418972" catchtap="__e" bindtouchstart="__e" catchtouchend="__e"><slot name="minus"></slot></view></block><block wx:else><block wx:if="{{showMinus}}"><view class="{{['u-number-box__minus','data-v-18418972',($root.m0)?'u-number-box__minus--disabled':'']}}" style="{{$root.s0}}" hover-class="u-number-box__minus--hover" hover-stay-time="150" data-event-opts="{{[['tap',[['clickHandler',['minus']]]],['touchstart',[['onTouchStart',['minus']]]],['touchend',[['clearTimeout',['$event']]]]]}}" catchtap="__e" bindtouchstart="__e" catchtouchend="__e"><u-icon vue-id="4486b0ed-1" name="minus" color="{{$root.m1?'#c8c9cc':'#323233'}}" size="15" bold="{{true}}" customStyle="{{iconStyle}}" class="data-v-18418972" bind:__l="__l"></u-icon></view></block></block><block wx:if="{{$slots.input}}"><slot name="input"></slot></block><block wx:else><input class="{{['u-number-box__input','data-v-18418972',(disabled||disabledInput)?'u-number-box__input--disabled':'']}}" style="{{$root.s1}}" disabled="{{disabledInput||disabled}}" cursor-spacing="{{getCursorSpacing}}" type="number" data-event-opts="{{[['blur',[['onBlur',['$event']]]],['focus',[['onFocus',['$event']]]],['input',[['__set_model',['','currentValue','$event',[]]],['onInput',['$event']]]]]}}" value="{{currentValue}}" bindblur="__e" bindfocus="__e" bindinput="__e"/></block><block wx:if="{{showPlus&&$slots.plus}}"><view data-event-opts="{{[['tap',[['clickHandler',['plus']]]],['touchstart',[['onTouchStart',['plus']]]],['touchend',[['clearTimeout',['$event']]]]]}}" class="u-number-box__slot data-v-18418972" catchtap="__e" bindtouchstart="__e" catchtouchend="__e"><slot name="plus"></slot></view></block><block wx:else><block wx:if="{{showPlus}}"><view class="{{['u-number-box__plus','data-v-18418972',($root.m2)?'u-number-box__minus--disabled':'']}}" style="{{$root.s2}}" hover-class="u-number-box__plus--hover" hover-stay-time="150" data-event-opts="{{[['tap',[['clickHandler',['plus']]]],['touchstart',[['onTouchStart',['plus']]]],['touchend',[['clearTimeout',['$event']]]]]}}" catchtap="__e" bindtouchstart="__e" catchtouchend="__e"><u-icon vue-id="4486b0ed-2" name="plus" color="{{$root.m3?'#c8c9cc':'#323233'}}" size="15" bold="{{true}}" customStyle="{{iconStyle}}" class="data-v-18418972" bind:__l="__l"></u-icon></view></block></block></view>