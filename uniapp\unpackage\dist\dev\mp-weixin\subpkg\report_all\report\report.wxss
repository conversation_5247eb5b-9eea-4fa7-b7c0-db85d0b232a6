@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 容器样式 */
.container.data-v-32fd93bb {
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #CBF2E0 0%, #EEF7F6 30%, #FFFFFF 61%, #FFFFFF 100%);
  height: 100%;
  padding: 0 32rpx;
  min-height: 100vh;
  padding-bottom: 20px;
}
.container-bg.data-v-32fd93bb {
  background: linear-gradient(180deg, #CBF2E0 0%, #EEF7F6 30%, #F6F7FB 100%);
}
.swiper.data-v-32fd93bb {
  height: 68vh;
}
/* 顶部标题样式 */
.header.data-v-32fd93bb {
  margin-top: 70rpx;
  text-align: center;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
}
.top.data-v-32fd93bb {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.left-icon.data-v-32fd93bb {
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.alert-msg.data-v-32fd93bb {
  margin-top: 28rpx;
  padding: 10rpx 0;
  padding-left: 20rpx;
  border-radius: 16rpx;
  background-color: #F5FFFD;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-weight: 400;
  font-size: 28rpx;
  color: #5A5A5A;
}
.alert-msg .info.data-v-32fd93bb {
  margin-left: 4rpx;
}
.sound-img.data-v-32fd93bb {
  width: 50rpx;
  height: 50rpx;
}
.main-title.data-v-32fd93bb {
  font-weight: bold;
  font-size: 34rpx;
  color: #2D2D2D;
}
.subtitle.data-v-32fd93bb {
  font-size: 26rpx;
  color: #999;
  margin-top: 10rpx;
}
/* 步骤导航样式 */
.steps.data-v-32fd93bb {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.tip.data-v-32fd93bb {
  margin-top: 36rpx;
}
.tip .tip-text.data-v-32fd93bb {
  position: relative;
  display: inline-block;
  font-weight: 800;
  font-size: 30rpx;
  color: #060606;
  z-index: 100;
}
.tip .tip-text.data-v-32fd93bb::after {
  content: "";
  position: absolute;
  bottom: -6rpx;
  left: 0;
  width: 100%;
  height: 20rpx;
  /* 指定高度 */
  background-color: #DBFF9C;
  /* 底部背景颜色 */
  z-index: -1;
}
.step.data-v-32fd93bb {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #ccc;
  text-align: center;
  line-height: 40rpx;
  color: #fff;
  font-size: 24rpx;
  gap: 0;
}
.step.active.data-v-32fd93bb {
  background-color: #FF9D3E;
  width: 43rpx;
  height: 43rpx;
}
.step-line.data-v-32fd93bb {
  flex: 1;
  height: 20rpx;
  background-color: #fff;
}
.line-bg.data-v-32fd93bb {
  background-color: #FFBD3A;
}
/* 表单样式 */
.form.data-v-32fd93bb {
  background-color: transparent;
  border-radius: 10rpx;
  padding: 20rpx 0;
}
.form-item-box.data-v-32fd93bb {
  display: flex;
  flex-direction: column;
  min-height: 106rpx;
  border-bottom: 1px solid #eee;
}
.form-item-box .form-item.data-v-32fd93bb {
  min-height: 80rpx;
  border-bottom: none;
  margin-top: 20rpx;
}
.form-item-box .cursor.data-v-32fd93bb {
  color: #f56c6c;
  font-size: 22rpx;
}
.form-item.data-v-32fd93bb {
  display: flex;
  min-height: 106rpx;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}
.form-item .right.data-v-32fd93bb {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.label.data-v-32fd93bb {
  min-width: 140rpx;
  font-size: 28rpx;
  color: #666;
}
.label .red.data-v-32fd93bb {
  color: #f56c6c;
}
.value.data-v-32fd93bb {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  min-width: 440rpx;
  margin-right: 20rpx;
}
.data-v-32fd93bb .checklist-group {
  justify-content: flex-end;
}
.tag.data-v-32fd93bb {
  font-size: 24rpx;
  color: #1aad19;
  background-color: #e8f5e9;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  margin-right: 10rpx;
}
/* 底部按钮样式 */
.footer.data-v-32fd93bb {
  width: 100%;
  margin-top: 40rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.footers.data-v-32fd93bb {
  width: 100%;
  margin-top: 40rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.next-button.data-v-32fd93bb {
  width: 80%;
  height: 80rpx;
  background-color: #1BB394;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  line-height: 80rpx;
  text-align: center;
}
.prev-button.data-v-32fd93bb {
  width: 280rpx;
  height: 80rpx;
  background: #F6F7FB;
  border-radius: 40rpx;
  font-weight: bold;
  font-size: 30rpx;
  color: #5A5A5A;
  text-align: center;
  line-height: 80rpx;
}
.white.data-v-32fd93bb {
  width: 280rpx;
  height: 80rpx;
  background: #fff;
  border-radius: 40rpx;
  font-weight: bold;
  font-size: 30rpx;
  color: #5A5A5A;
  text-align: center;
  line-height: 80rpx;
}
.form-item-step-three.data-v-32fd93bb {
  height: 200rpx;
  background: #FFFFFF;
  border-radius: 17rpx;
  padding: 26rpx 28rpx;
  margin-bottom: 26rpx;
}
.form-item-step-three .label.data-v-32fd93bb {
  font-weight: bold;
  font-size: 30rpx;
  color: #504E4E;
}
.value-container.data-v-32fd93bb {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}
.value-input.data-v-32fd93bb {
  flex: 1;
  padding: 10rpx;
  font-size: 28rpx;
  color: #333;
  border: 0;
  border-radius: 8rpx;
  outline: none;
}
.value-input.data-v-32fd93bb:focus {
  border-color: #1BB394;
}
.data-v-32fd93bb .checkbox__inner {
  border-radius: 16rpx !important;
}
@-webkit-keyframes rotate-data-v-32fd93bb {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes rotate-data-v-32fd93bb {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@-webkit-keyframes rotateCounter-data-v-32fd93bb {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
    /* 负值实现逆时针旋转 */
}
}
@keyframes rotateCounter-data-v-32fd93bb {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
    /* 负值实现逆时针旋转 */
}
}
.mask.data-v-32fd93bb {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.mask .bg-container.data-v-32fd93bb {
  height: 550rpx;
  width: 550rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.mask .ai3-bg-container.data-v-32fd93bb {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.mask .ai3-bg.data-v-32fd93bb {
  background-image: url("https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai3.png");
  height: 550rpx;
  width: 550rpx;
  background-repeat: no-repeat;
  background-size: contain;
}
.mask .ai3-bg-animation.data-v-32fd93bb {
  -webkit-animation: rotate-data-v-32fd93bb 1.5s linear infinite;
          animation: rotate-data-v-32fd93bb 1.5s linear infinite;
}
.mask .ai2-bg-container.data-v-32fd93bb {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.mask .ai2-bg.data-v-32fd93bb {
  background-image: url("https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai2.png");
  height: 420rpx;
  width: 494rpx;
  background-repeat: no-repeat;
  background-size: contain;
  z-index: 99;
}
.mask .ai2-bg-animation.data-v-32fd93bb {
  -webkit-animation: rotateCounter-data-v-32fd93bb 1.5s linear infinite;
          animation: rotateCounter-data-v-32fd93bb 1.5s linear infinite;
}
.mask .ai-bg.data-v-32fd93bb {
  background-image: url("https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai.png");
  height: 306rpx;
  width: 306rpx;
  background-repeat: no-repeat;
  background-size: contain;
  z-index: 100;
}
.mask .submit.data-v-32fd93bb {
  margin-top: 108rpx;
  width: 550rpx;
  height: 80rpx;
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  border-radius: 40rpx 40rpx 40rpx 40rpx;
  font-weight: bold;
  font-size: 30rpx;
  line-height: 80rpx;
  text-align: center;
  color: #FFFFFF;
}

