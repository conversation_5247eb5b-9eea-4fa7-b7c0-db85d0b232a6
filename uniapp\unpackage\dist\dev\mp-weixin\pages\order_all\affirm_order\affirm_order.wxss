@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-5c301432 {
  padding-bottom: 120rpx;
}
.container view.data-v-5c301432 {
  box-sizing: border-box;
}
.container.data-v-5c301432  .u-popup__content {
  border-radius: 16rpx 16rpx 0rpx 0rpx;
}
.container .coupon-conainer .title.data-v-5c301432 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 32rpx;
  color: #0A0A0A;
  position: relative;
  padding: 50rpx 0;
}
.container .coupon-conainer .title.data-v-5c301432  .u-icon--right {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 60rpx;
}
.container .coupon-conainer .coupon-list.data-v-5c301432 {
  max-height: 700rpx;
  overflow-y: scroll;
  padding-bottom: 120rpx;
}
.container .coupon-conainer .coupon-list .num.data-v-5c301432 {
  font-size: 28rpx;
  color: #0A0A0A;
  text-align: left;
  padding: 0 30rpx;
}
.container .coupon-conainer .coupon-list .coupon-container.data-v-5c301432 {
  margin-top: 36rpx;
  padding: 0 30rpx;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon.data-v-5c301432 {
  margin-bottom: 28rpx;
  height: 170rpx;
  background-size: contain;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .left.data-v-5c301432 {
  width: 192rpx;
  height: 100%;
  background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-left.png);
  background-repeat: no-repeat;
  background-size: contain;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .left text.data-v-5c301432:first-child {
  font-weight: bold;
  font-size: 40rpx;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .left text.data-v-5c301432:last-child {
  color: #CDF3E7;
  font-size: 26rpx;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .right.data-v-5c301432 {
  background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-right.png);
  background-repeat: no-repeat;
  background-size: contain;
  width: 500rpx;
  height: 100%;
  padding: 24rpx 34rpx;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .right .name.data-v-5c301432 {
  color: #4C5370;
  font-size: 30rpx;
  font-weight: bold;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .right .detail-info.data-v-5c301432 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .right .detail-info .detail-left.data-v-5c301432 {
  position: relative;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .right .detail-info .detail-left text.date.data-v-5c301432 {
  color: #AFAFAF;
  font-size: 24rpx;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .right .detail-info .detail-left .rule.data-v-5c301432 {
  margin-top: 16rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .right .detail-info .detail-left .rule text.data-v-5c301432 {
  font-size: 24rpx;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .right .detail-info .detail-left .rules.data-v-5c301432 {
  width: 299rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(0, 0, 0, 0.13);
  border-radius: 8rpx;
  position: absolute;
  top: 100rpx;
  left: -20rpx;
  padding: 20rpx 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #A0A0A0;
  z-index: 99999;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .right .detail-info .detail-right.data-v-5c301432 {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.container .coupon-conainer .coupon-list .coupon-container .coupon .right .detail-info .detail-right .use-btn.data-v-5c301432 {
  width: 132rpx;
  height: 54rpx;
  border-radius: 32rpx;
  border: 1rpx solid #01997A;
  color: #01997A;
  font-weight: 500;
  line-height: 54rpx;
  text-align: center;
  font-size: 26rpx;
}
.container .coupon-conainer .submit.data-v-5c301432 {
  width: 530rpx;
  height: 92rpx;
  line-height: 92rpx;
  text-align: center;
  position: fixed;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  font-weight: bold;
  font-size: 32rpx;
  color: #FFFFFF;
  margin: 50rpx auto;
  background: #01997A;
  border-radius: 61rpx;
}
.container .container-info.data-v-5c301432 {
  padding: 0 30rpx;
  padding-top: 36rpx;
  padding-bottom: 40rpx;
}
.container .container-info .address.data-v-5c301432 {
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 34rpx 28rpx;
  border-radius: 12rpx;
}
.container .container-info .address .info.data-v-5c301432 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  text-align: left;
}
.container .container-info .address .info .user-address.data-v-5c301432 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #060606;
  font-weight: bold;
  font-size: 28rpx;
}
.container .container-info .address .info .user-address .tag.data-v-5c301432 {
  width: 67rpx;
  height: 40rpx;
  background: #EEFAF6;
  border-radius: 10rpx;
  line-height: 40rpx;
  text-align: center;
  color: #09CC8C;
  font-size: 22rpx;
  margin-left: 18rpx;
}
.container .container-info .address .info text.detail.data-v-5c301432 {
  width: 100%;
  margin-top: 14rpx;
  color: #5A5A5A;
  font-weight: bold;
  font-size: 24rpx;
}
.container .container-info .add-address.data-v-5c301432 {
  height: 150rpx;
  background: #FFFFFF;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.container .container-info .add-address .add-address-btn.data-v-5c301432 {
  width: 570rpx;
  height: 76rpx;
  background: #CDF3E7;
  border-radius: 12rpx;
  color: #01997A;
  line-height: 76rpx;
  text-align: center;
}
.container .container-info .course-info.data-v-5c301432 {
  margin-top: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 22rpx 28rpx;
}
.container .container-info .course-info .title.data-v-5c301432 {
  font-size: 30rpx;
  color: #201E2E;
  font-weight: bold;
}
.container .container-info .course-info .info-container.data-v-5c301432 {
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f1f1f1;
}
.container .container-info .course-info .info-container.data-v-5c301432:last-child {
  border-bottom: none;
}
.container .container-info .course-info .info-container .price.data-v-5c301432 {
  margin-top: 30rpx;
  color: #060606;
  font-size: 26rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .container-info .course-info .info-container .course-detail.data-v-5c301432 {
  margin-top: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .container-info .course-info .info-container .course-detail text.data-v-5c301432:first-child {
  color: #A4A4A4;
  font-size: 24rpx;
}
.container .container-info .course-info .info-container .course-detail .num.data-v-5c301432 {
  font-size: 20rpx;
  font-weight: 400;
  color: #818181;
}
.container .container-info .course-info .info-container .teacher-info.data-v-5c301432 {
  margin-top: 16rpx;
  color: #818181;
  font-size: 22rpx;
}
.container .container-info .pay-info.data-v-5c301432 {
  margin-top: 30rpx;
  padding: 22rpx 28rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  border-radius: 12rpx;
}
.container .container-info .pay-info .h1-title.data-v-5c301432 {
  width: 100%;
  color: #201E2E;
  font-size: 30rpx;
  font-weight: bold;
  text-align: left;
}
.container .container-info .pay-info .info.data-v-5c301432 {
  margin-top: 30rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #201E2E;
  font-size: 26rpx;
  font-weight: bold;
}
.container .container-info .pay-info .info .choose-coupon.data-v-5c301432 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .container-info .pay-info .color-red.data-v-5c301432 {
  color: #E62E2E;
}
.container .container-info .pay-info .pay-type.data-v-5c301432 {
  margin-top: 30rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .container-info .pay-info .pay-type.data-v-5c301432  .u-radio-group--row {
  justify-content: flex-end;
}
.container .container-info .author-info.data-v-5c301432 {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.container .container-info .author-info text.data-v-5c301432 {
  font-size: 20rpx;
  margin-right: 10rpx;
  color: #A4A4A4;
}
.container .container-info .author-info text.data-v-5c301432:last-child {
  color: #01997A;
}
.container .container-info .tip.data-v-5c301432 {
  color: #E62E2E;
  font-size: 20rpx;
  padding-left: 50rpx;
}
.container .bottom-opt.data-v-5c301432 {
  position: fixed;
  bottom: 0;
  padding: 0 22rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 120rpx;
  background-color: #fff;
}
.container .bottom-opt .left.data-v-5c301432 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
}
.container .bottom-opt .left .real-money.data-v-5c301432 {
  font-size: 26rpx;
  color: #201E2E;
  font-weight: bold;
}
.container .bottom-opt .left .real-money text.data-v-5c301432 {
  color: #E62E2E;
  font-size: 32rpx;
}
.container .bottom-opt .left .preferential.data-v-5c301432 {
  margin-top: 6rpx;
  width: 100%;
  color: #201E2E;
  font-size: 22rpx;
  text-align: left;
}
.container .bottom-opt .btn.data-v-5c301432 {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-weight: bold;
  font-size: 30rpx;
  color: #FFFFFF;
  text-align: center;
  border-radius: 40rpx;
  background-color: #01997A;
}

