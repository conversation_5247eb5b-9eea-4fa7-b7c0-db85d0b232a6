<view class="content data-v-3ad965a2"><height vue-id="2053cfa4-1" hg="{{System_height}}" class="data-v-3ad965a2" bind:__l="__l"></height><view class="content_header data-v-3ad965a2"><view class="nav-title data-v-3ad965a2" style="{{'top:'+(System_height+'rpx')+';'}}"><text class="data-v-3ad965a2">AI考研择校报告</text></view><uni-icons class="back-left data-v-3ad965a2" style="{{'top:'+(System_height+'rpx')+';'}}" vue-id="2053cfa4-2" type="left" size="24" color="#2D2D2D" data-event-opts="{{[['^tap',[['back']]]]}}" bind:tap="__e" bind:__l="__l"></uni-icons></view><view class="notification data-v-3ad965a2"><image class="notification-icon data-v-3ad965a2" src="/static/select_school/notification_icon-56586a.png"></image><text class="notification-text data-v-3ad965a2">请认真完善信息，以便于精准生成报告！</text></view><view class="step-indicator data-v-3ad965a2"><view class="step-item data-v-3ad965a2"><view class="step-number data-v-3ad965a2">1</view></view><view class="step-item active data-v-3ad965a2"><view class="step-number data-v-3ad965a2">2</view></view><view class="step-item data-v-3ad965a2"><view class="step-number data-v-3ad965a2">3</view></view><view class="step-item data-v-3ad965a2"><view class="step-number data-v-3ad965a2">4</view></view><view class="step-item data-v-3ad965a2"><view class="step-number data-v-3ad965a2">5</view></view></view><view class="form-title data-v-3ad965a2"><view class="title-bg data-v-3ad965a2"></view><text class="title-text data-v-3ad965a2">本科成绩情况</text></view><view data-event-opts="{{[['tap',[['addSubject',['$event']]]]]}}" class="create-btn data-v-3ad965a2" bindtap="__e"><uni-icons vue-id="2053cfa4-3" type="plus" size="20" color="#FFFFFF" class="data-v-3ad965a2" bind:__l="__l"></uni-icons><text class="data-v-3ad965a2">创建</text></view><view class="form-content data-v-3ad965a2"><block wx:for="{{subjectList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="form-item data-v-3ad965a2"><view class="form-label data-v-3ad965a2">{{item.name}}</view><input class="form-input data-v-3ad965a2" placeholder="请输入成绩" type="number" data-event-opts="{{[['input',[['__set_model',['$0','score','$event',[]],[[['subjectList','',index]]]]]]]}}" value="{{item.score}}" bindinput="__e"/><block wx:if="{{item.canDelete}}"><view data-event-opts="{{[['tap',[['deleteSubject',[index]]]]]}}" class="delete-btn data-v-3ad965a2" bindtap="__e"><uni-icons vue-id="{{'2053cfa4-4-'+index}}" type="trash" size="16" color="#989898" class="data-v-3ad965a2" bind:__l="__l"></uni-icons><text class="data-v-3ad965a2">删除</text></view></block></view></block></view><view class="bottom-buttons data-v-3ad965a2"><view data-event-opts="{{[['tap',[['prevStep',['$event']]]]]}}" class="prev-btn data-v-3ad965a2" bindtap="__e"><text class="data-v-3ad965a2">上一步</text></view><view data-event-opts="{{[['tap',[['nextStep',['$event']]]]]}}" class="next-btn data-v-3ad965a2" bindtap="__e"><text class="data-v-3ad965a2">下一步</text></view></view></view>