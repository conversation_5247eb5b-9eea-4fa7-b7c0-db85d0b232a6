<template>
	<view class="container">

		<view class="container-info">

			<!-- 地址信息 -->
			<view class="address" v-if="addressDetail.addr">
				<view class="info">
					<view class="user-address">
						<text>{{addressDetail.name}} +86 {{addressDetail.phone}}</text>
						<!-- <text class="tag">默认</text> -->
					</view>
					<text class="detail">{{addressDetail.addr}}</text>
				</view>
				<u-icon name="arrow-right" size="20" @click="editClick"></u-icon>
			</view>
			<view class="add-address" v-else>
				<view class="add-address-btn" @click="addAddress">
					+添加地址
				</view>
			</view>
			<!-- 课程信息 -->
			<view class="course-info">
				<view class="title">
					课程信息
				</view>
				<view class="info-container" v-for="(item,index) in goodsList" :key="index">

					<view class="price">
						<text>{{item.productName}}</text>
						<text>￥{{item.unitPrice}} </text>
					</view>
					<view class="course-detail">
						<!-- <text class="tag">考研数学</text> -->
						<text v-show="item.startTime&&item.hours">{{item.startTime}}-{{item.endTime}} |
							共{{item.hours}}节</text>
						<text class="num"> × {{item.quantity}}</text>
					</view>
					<view class="teacher-info">
						主讲老师：<text v-for="(t,i) in item.teachers">{{t.title}}、</text>
					</view>
				</view>

			</view>

			<!-- 付款信息 -->
			<view class="pay-info">
				<view class="h1-title">
					付款信息
				</view>
				<view class="info">
					<text>商品金额</text>
					<!-- <text>￥{{total}}</text> -->
					<text>￥{{totalPrice}}</text>
				</view>
				<!-- <view class="info">
					<text>活动优惠</text>
					<text class="color-red">￥33800.00</text>
				</view>
				<view class="info">
					<text>抵扣优惠</text>
					<text class="color-red">￥0</text>
				</view> -->
				<view class="info">
					<text>优惠券优惠</text>
					<view class="choose-coupon" @click="open">
						<!-- <text class="color-red">￥{{couponMoney}}</text> -->
						<text class="color-red" v-if="couponAmount!=0"> - ￥{{couponAmount}}</text>
						<u-icon name="arrow-right" size="18"></u-icon>
					</view>

				</view>
			</view>

			<!-- 支付方式 -->
			<view class="pay-info">
				<view class="h1-title">
					支付方式
				</view>
				<view class="pay-type">
					<u-icon name="weixin-fill" color="rgb(40  183 97)" size="48" label="微信" labelSize="14px"></u-icon>
					<radio style="transform:scale(0.8)" value="1" activeBackgroundColor="#1BB394" :checked="true" />
				</view>

			</view>

			<view class="author-info">
				<radio-group @change="radioChange" class="radio-group">
					<radio style="transform:scale(0.6)" value="1" activeBackgroundColor="#1BB394" />
					<text>我已阅读 </text>
					<text>备案内容承诺公式、退款协议</text>
				</radio-group>
			</view>
			<view class="tip">
				*涉及优惠活动的订单发起退款时，实际退费金额需要重新核算
			</view>


		</view>
		<view class="bottom-opt">
			<view class="left">
				<!-- <text class="real-money">实付金额<text>￥{{total}}</text></text> -->
				<text class="real-money">实付金额<text>￥{{checkoutPrice}}</text></text>
				<!-- <text class="preferential">已优惠￥3800.00</text> -->
			</view>

			<view class="btn" @click="pagClick">
				立即购买
			</view>
		</view>
		<!-- 优惠券底部弹出层 -->
		<u-popup :show="showCoupon" mode="bottom" @close="close">
			<view class="coupon-conainer">
				<view class="title">
					<text>优惠券详情</text>
					<u-icon class="close" @click="close" name="close"></u-icon>
				</view>
				<view class="coupon-list">
					<view class="num">
						<!-- 可用优惠券({{num}}) -->
						可用优惠券({{validCouponList.length}})
					</view>
					<view class="coupon-container">
						<u-radio-group placement="column" @change="groupChange" v-model="couponDeafultSel">
							<view class="coupon" v-for="(item,index) in validCouponList" :key="item.id">
								<!--  -->
								<view class="left">
									<text>{{item.couponValue}} <text style="font-size: 32rpx;">元</text></text>
									<text v-if="item.hasMinOrderAmount">满{{item.minOrderAmount}}可用</text>
									<text v-else>无门槛</text>
								</view>
								<view class="right">
									<view class="name">
										{{item.name}}
									</view>
									<view class="detail-info">
										<view class="detail-left">
											<!-- <text class="date">有效期 {{item.date}}</text> -->
											<text class="date">有效期至
												<text>{{item.expireTime}}</text></text>
											<view class="rule" @click="rule(index)">
												<text>使用规则</text>
												<u-icon name="arrow-right" color="#009c7b" size="24"></u-icon>
											</view>
											<view class="rules" v-show="item.show" v-html="item.description">
											</view>
										</view>
										<view class="detail-right">
											<!-- :name="item.id" -->
											<u-radio :name="item.id" size="20px" activeColor="#009c7b" iconSize="18px">
											</u-radio>
										</view>
									</view>
								</view>
							</view>
							<view class="submit" @click="select">
								提交
							</view>
						</u-radio-group>
					</view>

				</view>
			</view>
		</u-popup>

	</view>
</template>

<script>
	import {
		getShippingAddress,
		updateShippingAddress,
		getDefaultShippingAddress,
		preOrder,
		wxOrder,
		xcxPay
	} from '@/api/comm.js'
	export default {
		data() {
			return {
				showCoupon: false,
				showAdress: true,
				payCheck: true,
				readCheck: false,
				couponvalue: "",
				num: 4,

				couponDeafultSel: '', //默认选中的优惠券
				selcoupon: {}, //选中的优惠券
				couponMoney: 0, //优惠金额
				addressDetail: {},
				goodsList: [],
				total: 0,
				value: '',
				openid: false,
				show: false,
				externalUrl: '',
				href: '',
				isRead: 0,
				code: '',
				productId: '', // 产品id
				campusId: '', //校区id
				cart: [], //购物车数据
				totalPrice: 0, //商品总金额
				checkoutPrice: 0, //折后商品金额
				validCouponList: [], //可用的优惠券列表
				couponAmount: '', //优惠金额
			};
		},
		onShow() {

			this.$forceUpdate()
			console.log(this.addressDetail)
		},
		onLoad(option) {
			const pages = getCurrentPages()
			if (pages.length >= 2) {
				const prevPage = pages[pages.length - 2] // 获取上一页实例
				if (prevPage.route === 'pages/order_all/affirm_order/affirm_order') {
					this.addressDetail = JSON.parse(option.query.item)
				}
			}
			this.campusId = uni.getStorageSync('campusId')
			let arr = uni.getStorageSync('cartItems')
			this.cart = arr.map(item => {
				return {
					productId: item.id,
					quantity: item.quantity
				}
			})
			this.getAddress()
			this.getDetail()
		},
		created() {},
		methods: {
			// 点击添加地址
			addAddress() {
				uni.navigateTo({
					url: '/pages/order_all/shipping_address/shipping_address'
				})
			},
			// 点击提交优惠券
			select() {
				if (!this.couponDeafultSel) {
					this.showCoupon = false
				} else {
					this.getDetail().then(res => {
						this.showCoupon = false
					})
				}
			},
			rule(index) {
				if (this.validCouponList[index].show) {
					this.validCouponList[index].show = 0
				} else {
					this.validCouponList[index].show = 1
				}
			},
			// 获取结算详情
			async getDetail() {
				const {
					data,
					errCode,
					msg
				} = await preOrder({
					campusId: this.campusId,
					cart: this.cart,
					useCouponId: this.couponDeafultSel,
				})
				if (errCode == 0) {
					this.goodsList = data.orderProducts
					this.totalPrice = data.totalPrice
					this.checkoutPrice = data.checkoutPrice
					this.couponAmount = data.couponAmount
					this.validCouponList = data.validCouponList.map(item => {
						return {
							...item,
							show: 0
						}
					})

				}
			},
			async getAddress() {
				const {
					data,
					errCode,
					msg
				} = await getDefaultShippingAddress()
				if (errCode == 0) {
					if (data) {
						this.addressDetail = data
						console.log('1', this.addressDetail)
					}

				}
			},

			// // 点击立即支付
			async pagClick() {
				if (!this.addressDetail.addr) {
					uni.showToast({
						title: '请先添加地址再进行支付',
						icon: 'none', // 不显示图标
						duration: 2000 // 显示时长（毫秒）
					});
					return
				}

				if (this.isRead == 0) {
					uni.showToast({
						title: '请先阅读并同意隐私协议',
						icon: 'none', // 不显示图标
						duration: 2000 // 显示时长（毫秒）
					});
					return
				}
				uni.showLoading({
					title: '操作中...'
				})
				try {
					const {
						data,
						errCode,
						msg
					} = await wxOrder({
						campusId: this.campusId,
						cart: this.cart,
						useCouponId: this.couponDeafultSel,
						shoppingAddressId: this.addressDetail.id
					})
					if (errCode == 0) {
						if (data.checkoutPrice == 0) {
							uni.navigateTo({
								url: `/page/order_all/submit_order/submit_order?show=1`
							})
							uni.removeStorageSync('cartItems')
						} else {
							this.getmpPay(data.id)
						}

					} else {
						uni.tip(msg)
					}
				} catch (e) {
					//TODO handle the exception
				}
			},
			//小程序支付接口
			async getmpPay(val) {
				uni.showLoading({
					title: '操作中...'
				})
				try {
					const {
						data,
						errCode,
						msg
					} = await xcxPay({
						orderId: val,

					})

					if (errCode == 0) {
						this.callWeChatPay(data)

					} else {
						uni.tip(msg)
					}
				} catch (e) {
					//TODO handle the exception
				}
			},
			// // 调用微信支付
			callWeChatPay(payParams) {
				// 3. 调用微信支付
				wx.requestPayment({
					timeStamp: payParams.timeStamp,
					nonceStr: payParams.nonceStr,
					package: payParams.package,
					signType: payParams.signType,
					paySign: payParams.paySign,
					success: (res) => {
						// 支付成功回调
						uni.navigateTo({
							url: `/page/order_all/submit_order/submit_order?show=1`
						})
						uni.removeStorageSync('cartItems')
					},
					fail: (err) => {
						// 支付失败回调
						this.handlePaymentFailure(err);
					}
				})

			},
			// 支付成功处理
			handlePaymentSuccess() {
				// console.log('支付成功，订单ID:', orderId);
				wx.showToast({
					title: '支付成功',
					icon: 'success',
					duration: 2000
				});

				// 这里可以添加支付成功后的业务逻辑
				// 例如：跳转到订单详情页
				// wx.navigateTo({
				// 	url: `/pages/order/detail?id=${orderId}`
				// });
			},

			// 支付失败处理
			handlePaymentFailure(err) {
				console.warn('支付失败:', err);

				if (err.errMsg.includes('cancel')) {
					// 用户取消支付
					wx.showToast({
						title: '支付已取消',
						icon: 'none'
					});
				} else {
					// 支付失败
					wx.showToast({
						title: '支付失败，请重试',
						icon: 'none'
					});
				}
			},

			radioChange(e) {
				this.isRead = e.detail.value
			},

			editClick() {
				uni.navigateTo({
					url: `/pages/order_all/shipping_address/shipping_address?type=1`
				})
			},
			close() {
				this.showCoupon = false
				this.couponDeafultSel = ''
			},
			open() {
				this.showCoupon = true
			},
			groupChange(n) {
				// //选中的优惠券
				// this.selcoupon = this.couponList.find(item => item.id == n)
				// this.couponMoney = this.selcoupon.value
				// this.showCoupon = false
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding-bottom: 120rpx;

		view {
			box-sizing: border-box;
		}

		::v-deep .u-popup__content {
			border-radius: 16rpx 16rpx 0rpx 0rpx;
		}

		.coupon-conainer {
			.title {
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 800;
				font-size: 32rpx;
				color: #0A0A0A;
				position: relative;
				padding: 50rpx 0;

				::v-deep .u-icon--right {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					right: 60rpx;

				}

			}

			.coupon-list {
				max-height: 700rpx;
				overflow-y: scroll;
				padding-bottom: 120rpx;

				.num {
					font-size: 28rpx;
					color: #0A0A0A;
					text-align: left;
					padding: 0 30rpx;
				}

				.coupon-container {
					margin-top: 36rpx;
					padding: 0 30rpx;

					.coupon {
						margin-bottom: 28rpx;
						height: 170rpx;
						background-size: contain;
						background-repeat: no-repeat;
						display: flex;
						align-items: center;
						justify-content: flex-start;

						.left {
							width: 192rpx;
							height: 100%;
							background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-left.png);
							background-repeat: no-repeat;
							background-size: contain;
							color: #fff;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;

							text {
								&:first-child {
									font-weight: bold;
									font-size: 40rpx;
								}

								&:last-child {
									color: #CDF3E7;
									font-size: 26rpx;
								}
							}
						}

						.right {
							background-image: url(https://test-1300870289.cos.ap-nanjing.myqcloud.com/coupon-right.png);
							background-repeat: no-repeat;
							background-size: contain;
							width: 500rpx;
							height: 100%;
							padding: 24rpx 34rpx;

							.name {
								color: #4C5370;
								font-size: 30rpx;
								font-weight: bold;
							}

							.detail-info {
								display: flex;
								align-items: center;
								justify-content: space-between;

								.detail-left {
									position: relative;

									text.date {
										color: #AFAFAF;
										font-size: 24rpx;
									}

									.rule {
										margin-top: 16rpx;
										display: flex;
										align-items: center;
										justify-content: flex-start;

										text {
											font-size: 24rpx;
											// color: $main-color;
										}
									}

									.rules {
										width: 299rpx;
										background: #FFFFFF;
										box-shadow: 0rpx 2rpx 10rpx 0rpx rgba(0, 0, 0, 0.13);
										border-radius: 8rpx;
										position: absolute;
										top: 100rpx;
										left: -20rpx;
										padding: 20rpx 20rpx;
										font-size: 24rpx;
										font-weight: 400;
										color: #A0A0A0;
										z-index: 99999;
									}
								}

								.detail-right {
									height: 100%;
									display: flex;
									align-items: center;
									justify-content: center;

									.use-btn {
										width: 132rpx;
										height: 54rpx;
										border-radius: 32rpx;
										border: 1rpx solid #01997A;
										color: #01997A;
										font-weight: 500;
										line-height: 54rpx;
										text-align: center;
										font-size: 26rpx;
									}
								}
							}
						}
					}
				}
			}

			.submit {
				width: 530rpx;
				height: 92rpx;
				line-height: 92rpx;
				text-align: center;
				position: fixed;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				font-weight: bold;
				font-size: 32rpx;
				color: #FFFFFF;
				margin: 50rpx auto;
				background: #01997A;
				border-radius: 61rpx;
			}
		}

		.container-info {
			// background-color: $container-bg-color;
			padding: 0 30rpx;
			padding-top: 36rpx;

			padding-bottom: 40rpx;

			.address {
				background-color: #fff;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 34rpx 28rpx;
				border-radius: 12rpx;

				.info {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					flex-direction: column;
					text-align: left;

					.user-address {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: flex-start;
						color: #060606;
						font-weight: bold;
						font-size: 28rpx;

						.tag {
							width: 67rpx;
							height: 40rpx;
							background: #EEFAF6;
							border-radius: 10rpx;
							line-height: 40rpx;
							text-align: center;
							color: #09CC8C;
							font-size: 22rpx;
							margin-left: 18rpx;
						}
					}

					text.detail {
						width: 100%;
						margin-top: 14rpx;
						color: #5A5A5A;
						font-weight: bold;
						font-size: 24rpx;
					}
				}
			}

			.add-address {
				height: 150rpx;
				background: #FFFFFF;
				border-radius: 12rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.add-address-btn {
					width: 570rpx;
					height: 76rpx;
					background: #CDF3E7;
					border-radius: 12rpx;
					color: #01997A;
					line-height: 76rpx;
					text-align: center;
				}
			}

			.course-info {
				margin-top: 30rpx;
				background-color: #fff;
				border-radius: 12rpx;
				padding: 22rpx 28rpx;

				.title {
					font-size: 30rpx;
					color: #201E2E;
					font-weight: bold;
				}

				.info-container {
					padding-bottom: 24rpx;
					border-bottom: 1rpx solid #f1f1f1;

					&:last-child {
						border-bottom: none;
					}

					.price {
						margin-top: 30rpx;
						color: #060606;
						font-size: 26rpx;
						font-weight: bold;
						display: flex;
						align-items: center;
						justify-content: space-between;

						text {
							// &:last-child {
							// 	font-weight: normal;
							// 	font-size: 30rpx;
							// }
						}


					}

					.course-detail {
						margin-top: 24rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;

						text {
							// &:first-child {
							// 	display: inline-block;
							// 	background-color: #EEFAF6;
							// 	color: #09CC8C;
							// 	font-size: 22rpx;
							// 	font-weight: bold;
							// 	padding: 10rpx 2rpx;
							// 	border-radius: 10rpx;
							// }

							&:first-child {
								// margin-left: 8rpx;
								color: #A4A4A4;
								font-size: 24rpx;
							}
						}

						.num {
							font-size: 20rpx;
							font-weight: 400;
							color: #818181;
						}
					}

					.teacher-info {
						margin-top: 16rpx;
						color: #818181;
						font-size: 22rpx;



					}
				}

			}

			.pay-info {
				margin-top: 30rpx;
				padding: 22rpx 28rpx;
				background-color: #fff;
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-direction: column;
				border-radius: 12rpx;

				.h1-title {
					width: 100%;
					color: #201E2E;
					font-size: 30rpx;
					font-weight: bold;
					text-align: left;
				}

				.info {
					margin-top: 30rpx;
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					color: #201E2E;
					font-size: 26rpx;
					font-weight: bold;

					.choose-coupon {
						display: flex;
						align-items: center;
						justify-content: space-between;
						// margin-right: -28rpx;
					}
				}

				.color-red {
					color: #E62E2E;
				}

				.pay-type {
					margin-top: 30rpx;
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;

					::v-deep .u-radio-group--row {
						justify-content: flex-end;
					}
				}

			}

			.author-info {
				margin-top: 20rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;


				text {
					font-size: 20rpx;
					// margin-left: 6rpx;
					margin-right: 10rpx;
					color: #A4A4A4;


					&:last-child {
						color: #01997A;
					}
				}
			}

			.tip {
				color: #E62E2E;
				font-size: 20rpx;
				padding-left: 50rpx;
			}


		}

		.bottom-opt {
			position: fixed;
			bottom: 0;
			padding: 0 22rpx;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 120rpx;
			background-color: #fff;

			.left {
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-direction: column;

				.real-money {
					font-size: 26rpx;
					color: #201E2E;
					font-weight: bold;

					text {
						color: #E62E2E;
						font-size: 32rpx;
					}
				}

				.preferential {
					margin-top: 6rpx;
					width: 100%;
					color: #201E2E;
					font-size: 22rpx;
					text-align: left;

				}

			}

			.btn {
				width: 240rpx;
				height: 80rpx;
				line-height: 80rpx;
				font-weight: bold;
				font-size: 30rpx;
				color: #FFFFFF;
				text-align: center;
				border-radius: 40rpx;
				background-color: #01997A;
			}
		}
	}
</style>