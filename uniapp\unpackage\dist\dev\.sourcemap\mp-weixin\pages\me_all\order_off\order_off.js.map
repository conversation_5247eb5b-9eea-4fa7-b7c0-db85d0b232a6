{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/order_off/order_off.vue?abd9", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/order_off/order_off.vue?b7e0", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/order_off/order_off.vue?762f", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/order_off/order_off.vue?7353", "uni-app:///pages/me_all/order_off/order_off.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/order_off/order_off.vue?992e", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/order_off/order_off.vue?d423"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "visible", "detail", "code", "order", "methods", "redeem", "errCode", "msg", "uni", "title", "icon", "close", "now", "get<PERSON><PERSON>j", "fields", "result", "startScan", "onlyFromCamera", "scanType", "success", "fail", "console", "checkCameraPermission", "scope", "content", "resolve", "reject", "verifyCode", "redeemCode"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACgDvnB;AAGA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA,2CACA,aACA;cAAA;gBAAA;gBALAN;gBACAO;gBACAC;gBAIA;kBACAC;oBACAC;oBACAC;kBACA;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACAC;QACAC;MACA;MAEA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACAtB;UACAuB;UAAA;UACAC;UAAA;UACAC;YACA;YACA;UACA;;UACAC;YACAC;YACA3B;cACAe;cACAC;YACA;UACA;QACA;MACA;QACAW;MACA;IACA;IAEA;IACAC;MACA;QACA5B;UACA6B;UACAJ;UACAC;YACA1B;cACAe;cACAe;cACAL;gBACA;kBACAzB;oBACAyB;sBACA,2BACA;wBACAM;sBACA;wBACAC;sBACA;oBACA;kBACA;gBACA;kBACAA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA,0BAIA;UACAC;QACA;QALA7B;QACAO;QACAC;MAIA;QACA;QACA;MACA;QACAb;UACAe;UACAC;QACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1OA;AAAA;AAAA;AAAA;AAAsqC,CAAgB,4oCAAG,EAAC,C;;;;;;;;;;;ACA1rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/me_all/order_off/order_off.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/me_all/order_off/order_off.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order_off.vue?vue&type=template&id=d2166d5e&scoped=true&\"\nvar renderjs\nimport script from \"./order_off.vue?vue&type=script&lang=js&\"\nexport * from \"./order_off.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order_off.vue?vue&type=style&index=0&id=d2166d5e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d2166d5e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/me_all/order_off/order_off.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_off.vue?vue&type=template&id=d2166d5e&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_off.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_off.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"top\">\r\n\t\t\t<view class=\"tip\">\r\n\t\t\t\t<text class=\"tip-text\">扫码核销</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ma\" @click=\"startScan\">\r\n\t\t\t\t<image src=\"../../../static/erweima.png\" mode=\"\"></image>\r\n\t\t\t\t<text>扫码核销</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"top\">\r\n\t\t\t<view class=\"tip\">\r\n\t\t\t\t<text class=\"tip-text\">核销码</text>\r\n\t\t\t</view>\r\n\t\t\t<input type=\"text\" v-model=\"code\" placeholder=\"请输入核销码\" />\r\n\t\t\t<view class=\"btns\" @click=\"now\">立刻核销</view>\r\n\t\t</view>\r\n\t\t<u-popup :show=\"visible\" :round=\"10\" mode=\"center\" @close=\"close\" @open=\"open\">\r\n\t\t\t<view class=\"popupContent\">\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"title\">{{detail.productName}}</view>\r\n\t\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t\t<!-- <text class=\"subject\">考研英语</text> -->\r\n\t\t\t\t\t\t<text class=\"endTime\" v-if=\"detail.startTime\">{{detail.startTime}}-{{detail.startEnd}}</text>\r\n\t\t\t\t\t\t<text class=\"num\" v-if=\"detail.hours\">共{{detail.hours}}节</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bottom\">\r\n\t\t\t\t\t\t<view class=\"teacher-list\" v-if=\"detail.teachers\">\r\n\t\t\t\t\t\t\t<view class=\"teacher-info\" v-for=\"(item,index) in detail.teachers\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.img\"></image>\r\n\t\t\t\t\t\t\t\t<text>{{item.title}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn\" @click=\"redeem\">立即核销</view>\r\n\t\t\t\t<view class=\"btn\" @click=\"close\">取消</view>\r\n\t\t\t</view>\r\n\r\n\t\t</u-popup>\r\n\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tqueryInfoForRedeem,\r\n\t\twxOrderRedeem\r\n\t} from '@/api/comm.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tvisible: false,\r\n\t\t\t\tdetail: {},\r\n\t\t\t\tcode: '',\r\n\t\t\t\torder: {}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 立即核销\r\n\t\t\tasync redeem() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await wxOrderRedeem({\r\n\t\t\t\t\t...this.order\r\n\t\t\t\t})\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '核销成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.visible = false\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 取消核销\r\n\t\t\tclose() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t},\r\n\t\t\t// 立即核销\r\n\t\t\tnow() {\r\n\t\t\t\tthis.verifyCode(this.code)\r\n\t\t\t},\r\n\t\t\tgetObj(str) {\r\n\t\t\t\tconst delimiter = \"_\";\r\n\t\t\t\tconst fields = [\"orderId\", \"orderProductId\", \"redeemCode\"];\r\n\t\t\t\t// 分割字符串\r\n\t\t\t\tconst parts = str.split(delimiter);\r\n\r\n\t\t\t\t// 创建结果对象\r\n\t\t\t\tconst result = {};\r\n\r\n\t\t\t\t// 将分割后的部分映射到对象属性\r\n\t\t\t\tfields.forEach((field, index) => {\r\n\t\t\t\t\tresult[field] = parts[index] || '';\r\n\t\t\t\t});\r\n\r\n\t\t\t\tthis.order = result\r\n\t\t\t\tthis.verifyCode(this.order.redeemCode);\r\n\t\t\t},\r\n\t\t\t// 开始扫码\r\n\t\t\tstartScan() {\r\n\t\t\t\t// 检查权限\r\n\t\t\t\tthis.checkCameraPermission().then(() => {\r\n\t\t\t\t\t// 开始扫码\r\n\t\t\t\t\twx.scanCode({\r\n\t\t\t\t\t\tonlyFromCamera: true, // 只允许从相机扫码\r\n\t\t\t\t\t\tscanType: ['qrCode'], // 只识别二维码\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tthis.getObj(res.result)\r\n\t\t\t\t\t\t\t// 验证核销码\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\tconsole.error('扫码失败:', err);\r\n\t\t\t\t\t\t\twx.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '扫码失败，请重试',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.error('摄像头权限错误:', err);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 检查摄像头权限\r\n\t\t\tcheckCameraPermission() {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\twx.authorize({\r\n\t\t\t\t\t\tscope: 'scope.camera',\r\n\t\t\t\t\t\tsuccess: resolve,\r\n\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\twx.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '权限提示',\r\n\t\t\t\t\t\t\t\tcontent: '需要摄像头权限进行扫码，是否去设置开启？',\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\twx.openSetting({\r\n\t\t\t\t\t\t\t\t\t\t\tsuccess: (settingRes) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (settingRes.authSetting[\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t'scope.camera']) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\treject('用户未授权摄像头权限');\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treject('用户拒绝授权');\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 验证核销码\r\n\t\t\tverifyCode(val) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = queryInfoForRedeem({\r\n\t\t\t\t\tredeemCode: val\r\n\t\t\t\t})\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tthis.detail = data\r\n\t\t\t\t\tthis.visible = true\r\n\t\t\t\t} else {\r\n\t\t\t\t\twx.showToast({\r\n\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// wx.showLoading({\r\n\t\t\t\t// \ttitle: '核销中...',\r\n\t\t\t\t// \tmask: true\r\n\t\t\t\t// });\r\n\r\n\t\t\t\t// // 调用后端API验证核销码\r\n\t\t\t\t// wx.request({\r\n\t\t\t\t// \turl: 'https://your-api.com/api/verify',\r\n\t\t\t\t// \tmethod: 'POST',\r\n\t\t\t\t// \theader: {\r\n\t\t\t\t// \t\t'Content-Type': 'application/json',\r\n\t\t\t\t// \t\t'Authorization': `Bearer ${wx.getStorageSync('token')}`\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \tdata: {\r\n\t\t\t\t// \t\tcode: code,\r\n\t\t\t\t// \t\tmerchantId: wx.getStorageSync('merchantId')\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \tsuccess: (res) => {\r\n\t\t\t\t// \t\twx.hideLoading();\r\n\t\t\t\t// \t\tif (res.data.success) {\r\n\t\t\t\t// \t\t\tthis.setData({\r\n\t\t\t\t// \t\t\t\tresult: res.data.data\r\n\t\t\t\t// \t\t\t});\r\n\t\t\t\t// \t\t\twx.showToast({\r\n\t\t\t\t// \t\t\t\ttitle: '核销成功',\r\n\t\t\t\t// \t\t\t\ticon: 'success'\r\n\t\t\t\t// \t\t\t});\r\n\r\n\t\t\t\t// \t\t\t// 播放成功音效\r\n\t\t\t\t// \t\t\twx.playBackgroundAudio({\r\n\t\t\t\t// \t\t\t\tdataUrl: '/sounds/success.mp3'\r\n\t\t\t\t// \t\t\t});\r\n\t\t\t\t// \t\t} else {\r\n\t\t\t\t// \t\t\twx.showToast({\r\n\t\t\t\t// \t\t\t\ttitle: res.data.message || '核销失败',\r\n\t\t\t\t// \t\t\t\ticon: 'none'\r\n\t\t\t\t// \t\t\t});\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \tfail: (err) => {\r\n\t\t\t\t// \t\twx.hideLoading();\r\n\t\t\t\t// \t\tconsole.error('核销请求失败:', err);\r\n\t\t\t\t// \t\twx.showToast({\r\n\t\t\t\t// \t\t\ttitle: '网络错误，请重试',\r\n\t\t\t\t// \t\t\ticon: 'none'\r\n\t\t\t\t// \t\t});\r\n\t\t\t\t// \t}\r\n\t\t\t\t// });\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t\t.top {\r\n\t\t\twidth: 690rpx;\r\n\t\t\tmargin: 30rpx auto;\r\n\t\t\tpadding: 30rpx 30rpx 45rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t.tip {\r\n\t\t\t\t.tip-text {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #060606;\r\n\t\t\t\t\tz-index: 100;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.tip-text::after {\r\n\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: -6rpx;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\twidth: 104%;\r\n\t\t\t\t\theight: 20rpx;\r\n\t\t\t\t\t/* 指定高度 */\r\n\t\t\t\t\tbackground-color: #DBFF9C;\r\n\t\t\t\t\t/* 底部背景颜色 */\r\n\t\t\t\t\tz-index: -1;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.ma {\r\n\t\t\t\twidth: 360rpx;\r\n\t\t\t\theight: 360rpx;\r\n\t\t\t\t// text-align: center;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tmargin: 30rpx auto 0;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\twidth: 360rpx;\r\n\t\t\t\t\theight: 360rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 220rpx;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\tz-index: 9;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tinput {\r\n\t\t\t\twidth: 563rpx;\r\n\t\t\t\theight: 106rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #5A5A5A;\r\n\t\t\t\tmargin: 50rpx auto 80rpx;\r\n\t\t\t\tbackground: #F6F7FB;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.btns {\r\n\t\t\t\twidth: 550rpx;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tline-height: 80rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tbackground: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\r\n\t\t::v-deep .u-popup__content {\r\n\t\t\tbackground-color: transparent !important;\r\n\t\t}\r\n\r\n\t\t.popupContent {\r\n\t\t\twidth: 555rpx;\r\n\t\t\theight: 587rpx;\r\n\t\t\tpadding: 40rpx 30rpx 50rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tbackground: url(https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/ticket_bg.png) no-repeat;\r\n\t\t\tbackground-size: 100%;\r\n\t\t}\r\n\r\n\t\t.content {\r\n\t\t\theight: 250rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #060606;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\r\n\t\t\t.detail {\r\n\t\t\t\tmargin-top: 12rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tcolor: #A4A4A4;\r\n\r\n\t\t\t\ttext {\r\n\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #A4A4A4;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.subject {\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tcolor: #09CC8C;\r\n\t\t\t\t\tpadding: 9rpx 7rpx;\r\n\t\t\t\t\tbackground: #EEFAF6;\r\n\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.endTime {\r\n\t\t\t\t\tmargin: 0 12rpx;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\twidth: 1rpx;\r\n\t\t\t\t\t\theight: 25rpx;\r\n\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\t\tright: -12rpx;\r\n\t\t\t\t\t\tbackground-color: #A4A4A4;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.num {\r\n\t\t\t\t\tmargin-left: 12rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.bottom {\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.teacher-list {\r\n\t\t\t\t\tpadding-left: 10rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\t\tflex-wrap: wrap;\r\n\r\n\t\t\t\t\t.teacher-info {\r\n\t\t\t\t\t\tmargin-right: 15rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #818181;\r\n\t\t\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.avatar {\r\n\t\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\t\t\tborder-radius: 100%;\r\n\t\t\t\t\t\t\tmargin-bottom: 2rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.btn {\r\n\t\t\twidth: 390rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tmargin: 50rpx auto 25rpx;\r\n\t\t\tbackground: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);\r\n\t\t\tborder-radius: 40rpx;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tcolor: #989898;\r\n\t\t\t\tmargin: 0 auto 0;\r\n\t\t\t\tbackground: #F6F7FB;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_off.vue?vue&type=style&index=0&id=d2166d5e&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_off.vue?vue&type=style&index=0&id=d2166d5e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557560034\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}