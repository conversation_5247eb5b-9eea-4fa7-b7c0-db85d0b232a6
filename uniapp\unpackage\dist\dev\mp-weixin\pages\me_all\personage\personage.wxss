@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-radio {
  margin-right: 20rpx;
  margin-bottom: 0 !important;
}
.u-radio text {
  font-size: 30rpx !important;
}
.hade_vip {
  height: 73rpx;
  background: #F1FBFF;
  font-size: 26rpx;
  font-weight: 400;
  color: #00C8FF;
  line-height: 73rpx;
  text-indent: 2em;
}
.content_box {
  width: 705rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  margin: 0 auto;
  margin-top: 100rpx;
}
.content_box .content {
  width: 90%;
  margin: 0 auto;
  position: relative;
}
.content_box .content .content_img {
  position: absolute;
  left: 50%;
  top: -80rpx;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 138rpx;
  height: 138rpx;
}
.content_box .content .content_img image {
  border-radius: 50%;
}
.content_box .content .content_input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eaeaea42;
}
.content_box .content .content_input view:nth-child(1) {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
}
.content_box .content .content_input view:nth-child(2) input {
  font-size: 28rpx;
  font-weight: 400;
  color: #979797;
  text-align: right;
}
.account {
  width: 705rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  margin: 0 auto;
  margin-top: 10rpx;
}
.account .content_input {
  width: 90%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #EAEAEA;
}
.account .content_input view:nth-child(1) {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
}
.account .content_input view:nth-child(2) input {
  font-size: 28rpx;
  font-weight: 400;
  color: #979797;
  text-align: right;
}
.save {
  width: 704rpx;
  height: 102rpx;
  margin: 0 auto;
  background: #00CCFF;
  border-radius: 9rpx;
  font-size: 32rpx;
  font-weight: 400;
  color: #F6FDFF;
  line-height: 102rpx;
  text-align: center;
  margin-top: 80rpx;
}

