import {
	getUserInfo,
	setUserInfo,
	getToken,
	setToken,
	setOrg,
	// getOrg
} from "@/utils/storage.js";
import {
	prepareReport,
	getUserInfo as getUserInfoDetails,
	orgUser
} from "@/api/user.js"
import {
	getOrgTheme
} from '@/api/user.js'
let user = {
	namespaced: true,
	state: {
		userInfo: getUserInfo(), // 从 storage 中获取用户信息
		// token: getToken(), // 从 storage 中获取 token
		// org: getOrg(), //从 storage 中获取 org
		reportInfo: {},
		reportOrgInfo: {},
		reportUserInfo: {},
		Plan: {},
		redirectPath: '', // 保存需要重定向的页面路径
		backgroundImages: {}, //页面背景图
		typeList: [], //Ai生成错误存储
		listenList: {}, //试听
		stuDetail: {}, //开课学员详情
		payDetail: {}, //立即支付课程详情
	},
	mutations: {
		setPayDetail(state, item) {
			state.payDetail = item
		},
		setStuDetail(state, item) {
			state.stuDetail = item
		},
		getListen(state, item) {
			state.listen = item
		},
		//添加ai错误类型
		setType(state, type) {
			state.typeList.push(type)
			console.log(state.typeList)
		},
		// 点击重试移除错误类型
		removeType(state, type) {
			let index = state.typeList.indexOf(type)
			state.typeList.splice(index, 1)
		},
		// 存储背景图
		setImg(state, images) {
			state.backgroundImages = Object.fromEntries(
				Object.entries(images).map(([key, value]) => [key, value.url])
			)
			console.log(state.backgroundImages)
		},
		//更新品牌方信息
		setOrgUser(state, user) {

			setUserInfo({
				...state.userInfo,
				...user
			});
		},
		// 设置用户信息
		setUserInfo(state, user) {
			let obj = {}
			obj = user
			state.userInfo = user;
			setUserInfo(obj); // 将用户信息保存到 storage
		},
		// 设置机构加密编码
		// 设置 token
		setOrg(state, org) {
			state.org = org;
			setOrg(org); // 将 ORG 保存到 storage
		},
		// 设置 token
		// setToken(state, token) {
		// 	state.token = token;
		// 	setToken(token); // 将 token 保存到 storage
		// },
		// 清除用户信息和 token
		clearUser(state) {
			state.userInfo = {};
			state.token = '';
			state.org = '';
			state.backgroundImages = {};
			setUserInfo({}); // 清空存储的用户信息
			setToken(''); // 清空存储的 token
			setOrg(''); // 清空存储的 org
		},
		clearToken(state) {
			state.token = ''; // 将token置空
		},
		//报告的基本信息
		setReportInfo(state, reportInfo) {
			state.reportInfo = reportInfo
		},
		// 机构端存储基本信息
		setOrgReportInfo(state, reportInfo) {
			state.reportOrgInfo = reportInfo
		},
		//报告的学员信息
		setReportUserInfo(state, reportUserInfo) {
			state.reportUserInfo = reportUserInfo
		},
		setPlan(state, payload) {
			state.Plan = {
				...state.Plan,
				...payload,

			}
			// console.log(state.Plan)
		},
		clearPlan(state) {
			state.Plan = {}
		},
		setRedirectPath(state, path) {
			state.redirectPath = path
		},
		clearRedirectPath(state) {
			state.redirectPath = ''
		},

	},
	actions: {
		initOrg({
			commit
		}) {
			// 应用启动时读取本地存储
			const orgId = getOrg()
			if (orgId) {
				commit('setOrg', orgId || '')
			}
		},
		async getImg({
			commit
		}) {
			try {
				const {
					data,
					errCode,
					msg
				} = await getOrgTheme()
				if (errCode == 0) {
					commit('setImg', data)

				} else {
					uni.tip(result.msg)
				}

			} catch (e) {}
		},
		async getReportData({
			commit
		}) {
			if (!getToken()) return
			try {
				const res = await getUserInfoDetails()
				if (res.errCode === 0) {
					commit('setUserInfo', res.data)
				} else if (res.errCode == 401) {
					uni.removeStorageSync('USER-TOKEN')
					this.$store.commit('clearToken');
				} else {
					uni.tip(res.msg)
				}
			} catch (e) {
				uni.removeStorageSync('USER-TOKEN')
				this.$store.commit('clearToken');

			}
		},
		async getUser({
			commit
		}) {
			if (!getToken()) return
			try {
				const res = await orgUser()
				if (res.errCode === 0) {
					commit('setOrgUser', res.data)
				} else if (res.errCode == 401) {
					uni.removeStorageSync('USER-TOKEN')
					this.$store.commit('clearToken');
				} else {
					uni.tip(res.msg)
				}
			} catch (e) {
				uni.removeStorageSync('USER-TOKEN')
				this.$store.commit('clearToken');

			}

		}
	},
};

export default user;