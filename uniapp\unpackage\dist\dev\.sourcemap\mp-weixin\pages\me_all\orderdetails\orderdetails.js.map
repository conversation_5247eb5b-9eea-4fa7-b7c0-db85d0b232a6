{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/orderdetails/orderdetails.vue?2d6b", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/orderdetails/orderdetails.vue?b5b1", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/orderdetails/orderdetails.vue?e530", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/orderdetails/orderdetails.vue?e90c", "uni-app:///pages/me_all/orderdetails/orderdetails.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/orderdetails/orderdetails.vue?f3fb", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/orderdetails/orderdetails.vue?48e0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "detail", "display", "verificationCode", "expiryTime", "qrCodeUrl", "showCanvas", "methods", "createQRCode", "qr", "setTimeout", "uni", "canvasId", "success", "fail", "console", "title", "icon", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,goBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkD1nB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IAEA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;UACA;UACA;;UAEA;UACAC;UACAA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACAA;;UAEA;UACA;;UAEA;UACAA;UACAA;;UAEA;UACAC;YACAC;cACAC;cACAC;gBACA;gBACA;cACA;;cACAC;gBACAC;gBACAJ;kBACAK;kBACAC;gBACA;cACA;YACA;UACA;QACA;UACAF;UACAJ;YACAK;YACAC;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC9HA;AAAA;AAAA;AAAA;AAAipC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACArqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/me_all/orderdetails/orderdetails.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/me_all/orderdetails/orderdetails.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderdetails.vue?vue&type=template&id=393a8c53&\"\nvar renderjs\nimport script from \"./orderdetails.vue?vue&type=script&lang=js&\"\nexport * from \"./orderdetails.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderdetails.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/me_all/orderdetails/orderdetails.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderdetails.vue?vue&type=template&id=393a8c53&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderdetails.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderdetails.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"title\">\r\n\t\t\t<image src=\"../../../static/clock.png\" mode=\"\"></image>\r\n\t\t\t<text>产品待核销</text>\r\n\t\t</view>\r\n\t\t<view class=\"desc\">您的订单已生成，请尽快使用！</view>\r\n\t\t<view class=\"content\">\r\n\t\t\t<view class=\"class-detail\">\r\n\t\t\t\t<image :src=\"detail.productCover\" mode=\"\"></image>\r\n\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t<view class=\"title\">{{detail.productName}}</view>\r\n\t\t\t\t\t<view class=\"desc\">好老师 好资料 好服务</view>\r\n\t\t\t\t\t<view class=\"money\">￥{{detail.totalPrice}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <image class=\"ma\" src=\"../../../static/ma.png\" mode=\"\"></image> -->\r\n\t\t\t<canvas v-if=\"showCanvas\" canvas-id=\"qrcode-canvas\" id=\"qrcode-canvas\"\r\n\t\t\t\tstyle=\"width: 300px; height: 300px; position: absolute; left: -9999px\"></canvas>\r\n\r\n\t\t\t<!-- 显示的二维码图片 -->\r\n\t\t\t<image v-if=\"qrCodeUrl\" :src=\"qrCodeUrl\" mode=\"widthFix\" class=\"ma\" />\r\n\t\t\t<view class=\"num\">券码{{detail.redeemCode}}</view>\r\n\t\t\t<view class=\"time\" v-if=\"detail.validYear\">{{detail.validYear}}到期</view>\r\n\t\t</view>\r\n\t\t<!-- <view class=\"order-detail\">\r\n\t\t\t<view class=\"title\">订单信息</view>\r\n\t\t\t<view class=\"detail\">\r\n\t\t\t\t<view class=\"every\">\r\n\t\t\t\t\t<text>订单编号</text>\r\n\t\t\t\t\t<text>{{detail.sn}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"every\">\r\n\t\t\t\t\t<text>下单时间</text>\r\n\t\t\t\t\t<text>{{detail.transTime}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"every\">\r\n\t\t\t\t\t<text>优惠券优惠</text>\r\n\t\t\t\t\t<text>{{detail.couponAmount}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"total\">实际支付：<text>￥{{detail.checkoutPrice}}</text></view>\r\n\t\t</view> -->\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 引入uqrcode组件\r\n\timport UQRCode from '@/uni_modules/Sansnn-uQRCode/js_sdk/uqrcode/uqrcode.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdetail: {},\r\n\t\t\t\tdisplay: false,\r\n\t\t\t\tverificationCode: '', // 后端返回的核销码\r\n\t\t\t\texpiryTime: '', // 核销码有效期\r\n\t\t\t\tqrCodeUrl: '', // 生成的二维码图片路径\r\n\t\t\t\tshowCanvas: false,\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// / 创建二维码 (修复后的方法)\r\n\t\t\tcreateQRCode(content) {\r\n\t\t\t\t// 显示canvas（微信小程序需要）\r\n\t\t\t\tthis.showCanvas = true\r\n\r\n\t\t\t\t// 等待DOM更新\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\t// 创建UQRCode实例\r\n\t\t\t\t\t\tconst qr = new UQRCode()\r\n\r\n\t\t\t\t\t\t// 设置二维码参数\r\n\t\t\t\t\t\tqr.data = content // 使用后端返回的核销码\r\n\t\t\t\t\t\tqr.size = 300 // 二维码大小\r\n\t\t\t\t\t\tqr.margin = 10 // 边距\r\n\t\t\t\t\t\tqr.foregroundColor = '#000000' // 前景色\r\n\t\t\t\t\t\tqr.backgroundColor = '#FFFFFF' // 背景色\r\n\t\t\t\t\t\tqr.errorCorrectLevel = UQRCode.errorCorrectLevel.H // 容错级别\r\n\r\n\t\t\t\t\t\t// 关键修复：先调用make()方法\r\n\t\t\t\t\t\tqr.make()\r\n\r\n\t\t\t\t\t\t// 获取canvas上下文\r\n\t\t\t\t\t\tconst ctx = uni.createCanvasContext('qrcode-canvas', this)\r\n\r\n\t\t\t\t\t\t// 绘制二维码\r\n\t\t\t\t\t\tqr.canvasContext = ctx\r\n\t\t\t\t\t\tqr.drawCanvas()\r\n\r\n\t\t\t\t\t\t// 获取临时图片路径 (微信小程序特殊处理)\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\t\t\t\tcanvasId: 'qrcode-canvas',\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tthis.qrCodeUrl = res.tempFilePath\r\n\t\t\t\t\t\t\t\t\tthis.showCanvas = false // 生成后隐藏canvas\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\tconsole.error('生成二维码失败', err)\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '生成二维码失败',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}, this)\r\n\t\t\t\t\t\t}, 300)\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error('生成二维码异常:', error)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '生成二维码异常',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.detail = JSON.parse(options.query.obj)\r\n\t\t\tlet str = this.detail.orderId + '_' + this.detail.id + '_' + this.detail.redeemCode\r\n\t\t\tthis.createQRCode(str)\r\n\t\t}\r\n\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.container {\r\n\t\twidth: 750rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground: linear-gradient(180deg, #00C2A0 0%, #00C2A0 16%, #F6F7FB 38%, #F6F7FB 100%);\r\n\r\n\t\t.title {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 34rpx;\r\n\t\t\tcolor: #FFFFFF;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 52rpx;\r\n\t\t\t\theight: 52rpx;\r\n\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.desc {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tmargin-top: 8rpx;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t}\r\n\r\n\t\t.content {\r\n\t\t\twidth: 690rpx;\r\n\t\t\theight: 606rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t.class-detail {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tpadding-bottom: 15rpx;\r\n\t\t\t\tborder-bottom: 1rpx dashed #D1D1D1;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 148rpx;\r\n\t\t\t\t\theight: 110rpx;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.right {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #414141;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.desc {\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\t\tcolor: #777777;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.money {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #343434;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.ma {\r\n\t\t\t\twidth: 254rpx;\r\n\t\t\t\theight: 254rpx;\r\n\t\t\t\tmargin: 40rpx auto;\r\n\t\t\t\tmargin-left: 50%;\r\n\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t}\r\n\r\n\t\t\t.num {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #343434;\r\n\t\t\t}\r\n\r\n\t\t\t.time {\r\n\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #606060;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.order-detail {\r\n\t\t\twidth: 690rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tpadding: 25rpx 30rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tmargin-bottom: 100rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #201E2E;\r\n\t\t\t\tpadding-bottom: 18rpx;\r\n\t\t\t\tborder-bottom: 1rpx dashed #D1D1D1;\r\n\t\t\t}\r\n\r\n\t\t\t.detail {\r\n\t\t\t\t// height: 300rpx;\r\n\t\t\t\tpadding: 30rpx 0;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tborder-bottom: 1rpx dashed #D1D1D1;\r\n\r\n\t\t\t\t.every {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #A2A2A2;\r\n\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #201E2E;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.total {\r\n\t\t\t\tmargin-top: 25rpx;\r\n\t\t\t\tcolor: #A4A4A4;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-end;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\tcolor: #E62E2E;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderdetails.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderdetails.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557559281\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}