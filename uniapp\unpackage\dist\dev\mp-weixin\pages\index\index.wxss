@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.beianhao.data-v-57280228 {
  margin: 20px 0;
  text-align: center;
  font-size: 24rpx;
  color: #b5b5b5;
}
.scrollBox.data-v-57280228 {
  padding: 0 30rpx;
  box-sizing: border-box;
}
.scrollBox .menu.data-v-57280228 {
  display: flex;
  align-items: center;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.scrollBox .menu.data-v-57280228::-webkit-scrollbar {
  display: none;
}
.scrollBox .menu-item.data-v-57280228 {
  display: inline-block;
  height: 46rpx;
  line-height: 46rpx;
  text-align: center;
  font-size: 35rpx;
  color: #636363;
  margin-right: 30rpx;
  transition: all 0.3s ease;
}
.scrollBox .menu-item.data-v-57280228:last-child {
  margin-right: 0;
}
.scrollBox .active.data-v-57280228 {
  color: #343434;
  font-size: 32rpx;
  font-weight: bold;
  border-bottom: 6rpx solid #00C2A0;
}
.noenList.data-v-57280228 {
  width: 690rpx;
  height: 300rpx;
  line-height: 300rpx;
  text-align: center;
  background: #FFFFFF;
  border-radius: 14rpx;
  background: #FFF;
  border-radius: 16rpx;
  margin: 0rpx auto 100rpx;
  padding: 0rpx 20rpx;
}
.productList.data-v-57280228 {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
}
.productList .product.data-v-57280228 {
  width: 338rpx;
}
.productList .product .da.data-v-57280228 {
  width: 338rpx;
  height: 394rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
}
.productList .product .productItem.data-v-57280228 {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
}
.productList .product .productItem.data-v-57280228:first-child {
  margin-bottom: 0;
}
.productList .product .productItem .head.data-v-57280228 {
  width: 338rpx;
  height: 252rpx;
}
.productList .product .productItem .bottom.data-v-57280228 {
  padding: 0 20rpx;
}
.productList .product .productItem .bottom .title.data-v-57280228 {
  width: 100%;
  margin-top: 24rpx;
  margin-bottom: 8rpx;
  font-weight: 800;
  font-size: 30rpx;
  color: #414141;
}
.productList .product .productItem .bottom .desc.data-v-57280228 {
  font-weight: 400;
  font-size: 22rpx;
  color: #777777;
}
.productList .product .productItem .bottom .money.data-v-57280228 {
  margin-top: 60rpx;
  font-size: 32rpx;
  color: #FB4E44;
  padding-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.productList .product .productItem .bottom .money .add.data-v-57280228 {
  width: 84rpx;
  height: 45rpx;
  line-height: 45rpx;
  text-align: center;
  font-weight: bold;
  font-size: 22rpx;
  color: #FFFFFF;
  background: #00C2A0;
  border-radius: 30rpx;
}
.grid.data-v-57280228 {
  width: 690rpx;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: center;
  font-size: 24rpx;
  margin-bottom: 38rpx;
  color: #323232;
  background-color: #fff;
  border-radius: 18rpx;
  padding-bottom: 30rpx;
}
.grid .grid-item.data-v-57280228 {
  width: 25%;
  margin-top: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.grid .grid-item image.data-v-57280228 {
  width: 100rpx;
}
.img_box.data-v-57280228 {
  width: 750rpx;
  height: 570rpx;
}
.img_box.data-v-57280228  .u-swiper__indicator {
  position: absolute;
  bottom: 80rpx !important;
}
.img_box .img_box.data-v-57280228 {
  position: absolute;
}
.img_box .user.data-v-57280228 {
  width: 704rpx;
  height: 136rpx;
  background: #FFFFFF;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  margin: 0 auto;
  position: relative;
  top: 400rpx;
}
.img_box .user .user_head_portrait.data-v-57280228 {
  width: 89rpx;
  height: 89rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-left: 32rpx;
}
.img_box .user .user_2.data-v-57280228 {
  width: 54%;
  margin-left: 30rpx;
}
.img_box .user .user_2 .user_2_1.data-v-57280228 {
  width: 200px;
  font-size: 30rpx;
  font-weight: 600;
  color: #201E2E;
  margin-bottom: 5rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.img_box .user .user_2 .user_2_2.data-v-57280228 {
  font-size: 26rpx;
  color: #777777;
  margin-bottom: 5rpx;
}
.img_box .user .user_2 .user_2_2 .num.data-v-57280228 {
  color: #000;
  font-weight: 500;
  margin-left: 10rpx;
  margin-right: 5rpx;
}
.img_box .user .user_2 .user_2_2 .left.data-v-57280228 {
  margin-left: 10rpx;
}
.img_box .user .user_2 .user_2_2 .left .num.data-v-57280228 {
  color: #000;
}
.img_box .user .user_2 .user_2_3.data-v-57280228 {
  margin-top: 10rpx;
}
.img_box .user .user_3.data-v-57280228 {
  position: absolute;
  right: 30rpx;
}
.img_box .user .user_3 .user_code.data-v-57280228 {
  width: 54rpx;
  height: 54rpx;
  margin: 0 auto;
}
.img_box .user .user_3 text.data-v-57280228 {
  font-size: 24rpx;
  font-weight: 400;
  color: #2F2F2F;
}
.card.data-v-57280228 {
  width: 704rpx;
  margin: 0 auto;
}
.card .card_1.data-v-57280228 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card .card_1 .card_1_left.data-v-57280228 {
  width: 345rpx;
  height: 348rpx;
  background: #FEFAEE;
  border-radius: 16rpx;
  position: relative;
}
.card .card_1 .card_1_left .card_1_left_title.data-v-57280228 {
  margin: 50rpx 0 0 20rpx;
}
.card .card_1 .card_1_left .card_1_left_title view.data-v-57280228:nth-child(1) {
  font-size: 34rpx;
  font-weight: 600;
}
.card .card_1 .card_1_left .card_1_left_title view.data-v-57280228:nth-child(2) {
  font-size: 24rpx;
  font-weight: 400;
  margin-top: 20rpx;
}
.card .card_1 .card_1_left .card_1_left_img.data-v-57280228 {
  width: 85%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
}
.card .card_1 .card_1_right.data-v-57280228 {
  width: 345rpx;
  height: 348rpx;
  background: #FFF9F9;
  border-radius: 16rpx;
  position: relative;
}
.card .card_1 .card_1_right .card_1_left_title.data-v-57280228 {
  margin: 50rpx 0 0 20rpx;
}
.card .card_1 .card_1_right .card_1_left_title view.data-v-57280228:nth-child(1) {
  font-size: 34rpx;
  font-weight: 600;
}
.card .card_1 .card_1_right .card_1_left_title view.data-v-57280228:nth-child(2) {
  font-size: 24rpx;
  font-weight: 400;
  margin-top: 20rpx;
}
.card .card_1 .card_1_right .card_1_left_img.data-v-57280228 {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}
.card .card_2.data-v-57280228 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx 0;
}
.card .card_2 .card_2_content.data-v-57280228 {
  width: 342rpx;
  height: 161rpx;
  background: #FFFFFF;
  border-radius: 36rpx 13rpx 13rpx 13rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card .card_2 .card_2_content .card_2_content_left.data-v-57280228 {
  margin-left: 29rpx;
  text-align: left;
}
.card .card_2 .card_2_content .card_2_content_left view.data-v-57280228:nth-child(1) {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
}
.card .card_2 .card_2_content .card_2_content_left view.data-v-57280228:nth-child(2) {
  font-size: 24rpx;
  font-weight: 400;
  color: #AAAAAA;
  margin-top: 5rpx;
}
.card .card_2 .card_2_content .card_2_content_right.data-v-57280228 {
  margin-right: 30rpx;
  width: 85rpx;
  height: 66rpx;
}
.card .card_3.data-v-57280228 {
  width: 100%;
}

