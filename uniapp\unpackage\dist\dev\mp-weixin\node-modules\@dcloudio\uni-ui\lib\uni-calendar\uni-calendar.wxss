@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-calendar.data-v-5fe32366 {
  display: flex;
  flex-direction: column;
}
.uni-calendar__mask.data-v-5fe32366 {
  position: fixed;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.4);
  transition-property: opacity;
  transition-duration: 0.3s;
  opacity: 0;
  z-index: 99;
}
.uni-calendar--mask-show.data-v-5fe32366 {
  opacity: 1;
}
.uni-calendar--fixed.data-v-5fe32366 {
  position: fixed;
  left: 0;
  right: 0;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  transition-duration: 0.3s;
  -webkit-transform: translateY(460px);
          transform: translateY(460px);
  bottom: calc(0px);
  z-index: 99;
}
.uni-calendar--ani-show.data-v-5fe32366 {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.uni-calendar__content.data-v-5fe32366 {
  background-color: #fff;
}
.uni-calendar__header.data-v-5fe32366 {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 50px;
  border-bottom-color: #EDEDED;
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
.uni-calendar--fixed-top.data-v-5fe32366 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-top-color: #EDEDED;
  border-top-style: solid;
  border-top-width: 1px;
}
.uni-calendar--fixed-width.data-v-5fe32366 {
  width: 50px;
}
.uni-calendar__backtoday.data-v-5fe32366 {
  position: absolute;
  right: 0;
  top: 25rpx;
  padding: 0 5px;
  padding-left: 10px;
  height: 25px;
  line-height: 25px;
  font-size: 12px;
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  color: #333;
  background-color: #f1f1f1;
}
.uni-calendar__header-text.data-v-5fe32366 {
  text-align: center;
  width: 100px;
  font-size: 14px;
  color: #333;
}
.uni-calendar__header-btn-box.data-v-5fe32366 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
}
.uni-calendar__header-btn.data-v-5fe32366 {
  width: 10px;
  height: 10px;
  border-left-color: #808080;
  border-left-style: solid;
  border-left-width: 2px;
  border-top-color: #555555;
  border-top-style: solid;
  border-top-width: 2px;
}
.uni-calendar--left.data-v-5fe32366 {
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}
.uni-calendar--right.data-v-5fe32366 {
  -webkit-transform: rotate(135deg);
          transform: rotate(135deg);
}
.uni-calendar__weeks.data-v-5fe32366 {
  position: relative;
  display: flex;
  flex-direction: row;
}
.uni-calendar__weeks-item.data-v-5fe32366 {
  flex: 1;
}
.uni-calendar__weeks-day.data-v-5fe32366 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 45px;
  border-bottom-color: #F5F5F5;
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
.uni-calendar__weeks-day-text.data-v-5fe32366 {
  font-size: 14px;
}
.uni-calendar__box.data-v-5fe32366 {
  position: relative;
}
.uni-calendar__box-bg.data-v-5fe32366 {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.uni-calendar__box-bg-text.data-v-5fe32366 {
  font-size: 200px;
  font-weight: bold;
  color: #999;
  opacity: 0.1;
  text-align: center;
  line-height: 1;
}

