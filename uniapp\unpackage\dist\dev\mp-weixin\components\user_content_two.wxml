<view class="container data-v-777afb4d"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info data-v-777afb4d"><view class="info-title data-v-777afb4d"><image class="logo data-v-777afb4d" src="../static/imgs/rhomb.png" mode></image><text class="title-text data-v-777afb4d">{{''+item.$orig.title+''}}</text></view><block wx:for="{{item.l0}}" wx:for-item="obj" wx:for-index="key" wx:key="key"><view class="info-content data-v-777afb4d"><view class="content-title data-v-777afb4d"><view class="cricle data-v-777afb4d"></view><view class="content-title-text data-v-777afb4d">{{''+obj.$orig.title+''}}</view></view><view class="subtitle-content-container data-v-777afb4d"><block wx:if="{{obj.g0}}"><block wx:for="{{obj.$orig.list}}" wx:for-item="value" wx:for-index="k" wx:key="k"><view class="subtitle-content data-v-777afb4d"><view class="subtitle-text data-v-777afb4d">{{''+value.name+''}}</view><view class="subtitle-content-text data-v-777afb4d"><rich-text nodes="{{value.content}}" class="data-v-777afb4d"></rich-text></view></view></block></block><block wx:else><view class="data-v-777afb4d"><rich-text nodes="{{obj.$orig.content}}"></rich-text></view></block><block wx:if="{{obj.g1}}"><text class="loading data-v-777afb4d"><view class="loading-spinner data-v-777afb4d"></view></text></block><block wx:if="{{obj.g2}}"><text style="color:red;" class="data-v-777afb4d">服务器繁忙，请重试</text></block><block wx:if="{{obj.g3}}"><view data-event-opts="{{[['tap',[['again',['$0'],[[['list','',index],['content','',key,'type']]]]]]]}}" class="again data-v-777afb4d" bindtap="__e">重新生成</view></block></view></view></block></view></block></view>