<template>
	<view class="container">
		<view class="info" v-for="(item, index) in data" :key="index">
			<view class="info-title">
				<image class="logo" src="../static/imgs/rhomb.png" mode=""></image>
				<text class="title-text">
					{{ item.title }}
				</text>
			</view>
			<view class="info-content">
				<rich-text :nodes="item.content"></rich-text>
				<text class="loading" v-if="!item.content&&!typeList.includes(item.type)"><svg viewBox="0 0 36 36"
						version="1.1" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" data-icon="spin">
						<defs>
							<linearGradient x1="0%" y1="100%" x2="100%" y2="100%" id="linearGradient-1">
								<stop stop-color="currentColor" stop-opacity="0" offset="0%"></stop>
								<stop stop-color="currentColor" stop-opacity="0.50" offset="39.9430698%"></stop>
								<stop stop-color="currentColor" offset="100%"></stop>
							</linearGradient>
						</defs>
						<g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
							<rect fill-opacity="0.01" fill="none" x="0" y="0" width="36" height="36"></rect>
							<path
								d="M34,18 C34,9.163444 26.836556,2 18,2 C11.6597233,2 6.18078805,5.68784135 3.59122325,11.0354951"
								stroke="url(#linearGradient-1)" stroke-width="4" stroke-linecap="round"></path>
						</g>
					</svg></text>
				<text style="color:red;" v-if="typeList.includes(item.type)"> 服务器繁忙，请重试</text>
				<view class="again" v-if="typeList.includes(item.type)" @click="againClick(item)">重新生成
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import sseEvent from "@/utils/sse_event.js"
	import {
		getAdviceList,
		getOrgAdviceList
	} from "@/api/user.js"
	import {
		mapState
	} from "vuex"
	export default {
		data() {
			return {
				data: [],
				urls: '',
				role: '',
			};
		},
		props: {
			msgtype: {
				type: Array,
				default: () => []
			},
			isSse: {
				type: Boolean,
				default: true
			}
		},
		created() {
			// this.role = JSON.parse(uni.getStorageSync('USER-INFO-KEY')).role
			// if (this.role == 'user') {
			// 	this.urls = '/my_dxt/report/queryBlock2'
			// }
			// if (this.role == 'student') {
			this.urls = '/stu/report/queryBlock2'
			// }
			if (this.isSse) {
				this.fetchData()
			} else {
				// if (this.role == 'student') {
				this.getData()
				// } else {
				// 	this.getOrgData()
				// }
				//普通http 获取数据

			}
		},
		computed: {
			...mapState('user', ['typeList']),
		},
		methods: {
			async fetchData() {
				console.log(this.$store.state.user.reportInfo)
				for (let [index, item] of this.msgtype.entries()) {
					this.data.push({
						type: item.type,
						title: item.label,
						content: ' '
					});
					let requestUrl = ''
					if (this.role == 'student') {
						requestUrl =
							`${uni.http.baseUrl}${this.urls}?
									type=${item.type}
									&token=${this.$store.getters.token}
									&reportId=${this.$store.state.user.reportInfo.report.id}
									`;
					}
					if (this.role == 'user') {
						let params = {
							...this.$store.state.user.reportInfo,
							reportId: this.$store.state.user.reportInfo.report.id,
							type: item.type,
							token: this.$store.getters.token,
							postGraduation: JSON.stringify(this.$store.state.user.reportInfo.postGraduation),
						}
						let queryStr = Object.keys(params)
							.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
							.join('&');
						requestUrl = `${uni.http.baseUrl}${this.urls}?${queryStr}`;
					}


					try {
						const abortController = new AbortController();
						// 20秒总超时
						const timeout20s = setTimeout(() => {
							abortController.abort();
						}, 20000);

						// 10秒无响应中断
						let responseTimeout = setTimeout(() => {
							abortController.abort();
						}, 10000);

						await sseEvent(requestUrl, item.type, (buffer, eventType) => {
							const idx = this.data.findIndex(o => o.type === item.type);

							// 每次收到数据重置10秒中断计时器
							clearTimeout(responseTimeout);
							responseTimeout = setTimeout(() => {
								abortController.abort();
							}, 10000);
							if (eventType === "say") {
								if (timeout20s) {
									clearTimeout(timeout20s);
								}
								let content = this.data[idx].content;
								content += buffer;
								this.data[idx].content = content.replace(/\n{1,2}/g, "<br>")
									.replace(/\*\*([^*]+)\*\*/g, "<b>$1</b>");
							} else if (eventType === "reply") {
								if (timeout20s) {
									clearTimeout(timeout20s);
								}
								this.data[idx].content = buffer;
							}

							this.$store.commit('user/setPlan', {
								[item.type]: this.data[idx].content
							});
						}, {
							signal: abortController.signal
						});

						// 请求成功完成，清除定时器
						clearTimeout(timeout20s);
						clearTimeout(responseTimeout);

					} catch (error) {
						this.$store.commit('user/setType', item.type);
						console.error('请求错误:', error);
					}
				}
			},
			// 点击重试
			async againClick(item) {
				item.content = ''
				// 
				this.$store.commit('user/removeType', item.type)
				let requestUrl = ''
				if (this.role == 'student') {
					requestUrl =
						`${uni.http.baseUrl}${this.urls}?
								type=${item.type}
								&token=${this.$store.getters.token}
								&reportId=${this.$store.state.user.reportInfo.report.id}
								`;
				}
				if (this.role == 'user') {
					let params = {
						...this.$store.state.user.reportInfo,
						reportId: this.$store.state.user.reportInfo.report.id,
						type: item.type,
						token: this.$store.getters.token,
						postGraduation: JSON.stringify(this.$store.state.user.reportInfo.postGraduation),

					}
					let queryStr = Object.keys(params)
						.map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
						.join('&');
					requestUrl = `${uni.http.baseUrl}${this.urls}?${queryStr}`;
				}

				try {
					const abortController = new AbortController();

					// 20秒总超时
					const timeout20s = setTimeout(() => {
						abortController.abort();
					}, 20000);

					// 10秒无响应中断
					let responseTimeout = setTimeout(() => {
						abortController.abort();
					}, 10000);

					await sseEvent(requestUrl, item.type, (buffer, eventType) => {
						const idx = this.data.findIndex(o => o.type === item.type);

						// 每次收到数据重置10秒中断计时器
						clearTimeout(responseTimeout);
						responseTimeout = setTimeout(() => {
							abortController.abort();
						}, 10000);

						if (eventType === "say") {
							let content = this.data[idx].content;
							content += buffer;
							this.data[idx].content = content.replace(/\n{1,2}/g, "<br>")
								.replace(/\*\*([^*]+)\*\*/g, "<b>$1</b>");
						} else if (eventType === "reply") {
							this.data[idx].content = buffer;
						}

						this.$store.commit('user/setPlan', {
							[item.type]: this.data[idx].content
						});
					}, {
						signal: abortController.signal
					});

					// 请求成功完成，清除定时器
					clearTimeout(timeout20s);
					clearTimeout(responseTimeout);

				} catch (error) {
					this.$store.commit('user/setType', item.type);
				}
			},
			async getData() {
				const data = {
					reportId: this.$store.state.user.reportInfo.report.id,
				}
				const result = await getAdviceList(data)
				if (result.errCode == 0) {
					let arr = []
					result.data.forEach(item => {

						if (item.children) {
							let obj = {}
							obj.id = item.id
							obj.title = item.name
							obj.content = item.children.map(i => i.content).join("<br />");
							arr.push(obj)
						}


					})
					this.data = arr
					let adviceData = []
					result.data.forEach(item => {
						if (item.children) {
							let obj = {}
							obj.id = item.id
							obj.title = item.name
							obj.content = item.children.map(i => i.content).join("<br />");
							adviceData.push(obj)
						}
					})
					this.$store.commit('user/setPlan', {
						'advice': adviceData
					});
				}
			},

			async getOrgData() {
				const data = {
					studentId: this.$store.state.user.reportInfo.studentId,
				}
				const result = await getOrgAdviceList(data)
				if (result.errCode == 0) {
					let arr = []
					result.data.forEach(item => {

						if (item.children) {
							let obj = {}
							obj.id = item.id
							obj.title = item.name
							obj.content = item.children.map(i => i.content).join("<br />");
							arr.push(obj)
						}


					})
					this.data = arr
					let adviceData = []
					result.data.forEach(item => {
						if (item.children) {
							let obj = {}
							obj.id = item.id
							obj.title = item.name
							obj.content = item.children.map(i => i.content).join("<br />");
							adviceData.push(obj)
						}
					})
					this.$store.commit('user/setPlan', {
						'advice': adviceData
					});
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background: linear-gradient(180deg, rgba(203, 242, 224, 0.2) 0%, rgba(255, 255, 255, 0) 20%);
		border-radius: 14rpx;
		border: 2rpx dashed #1BB394;
		padding: 24rpx 28rpx;

		@keyframes icon-loading {
			0% {
				transform: rotate(0);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		.loading {
			color: #909090;
		}

		.loading svg {
			will-change: transform;
			width: 1em;
			height: 1em;
			animation: 0.6s linear infinite icon-loading;
		}

		.again {
			display: inline-block;
			width: 100rpx;
			height: 35rpx;
			line-height: 35rpx;
			border-radius: 10rpx;
			text-align: center;
			font-size: 16rpx;
			color: #fff;
			background: #1bb394;
			cursor: pointer;
			margin-left: 10rpx;
		}

		.info {
			padding-top: 30rpx;

			&:first-child {
				padding-top: 0rpx;
			}

			.info-title {
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.logo {
					width: 32rpx;
					height: 24rpx;
					margin-right: 8rpx;
				}

				.title-text {
					font-weight: 800;
					font-size: 16rpx;
					color: #4C5370;
				}

			}

			.info-content {
				font-weight: 400;
				font-size: 16rpx;
				color: #504E4E;
				line-height: 24rpx;
			}
		}
	}
</style>