@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.back {
  position: absolute;
  width: 100%;
  height: 100vh;
}
.nav-tab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 2;
}
.nav-tab .nav-tab_left {
  padding: 30rpx;
}
.nav-tab .nav-tab_content {
  font-size: 34rpx;
  font-weight: 500;
  color: #FFFFFF;
  z-index: 2;
  text-align: center;
  padding-right: 100rpx;
}
.qr {
  width: 700rpx;
  height: 714rpx;
  background: #FFFFFF;
  border-radius: 27rpx;
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  top: 30%;
}
.qr .qr_1 {
  margin: 0 auto;
  text-align: center;
}
.qr .qr_1 .qr_1_img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  bottom: 50rpx;
  margin: 0 auto;
}
.qr .qr_1 text {
  font-size: 32rpx;
  font-weight: 400;
  color: #343434;
  position: relative;
  bottom: 50rpx;
}
.qr .qr_2 {
  width: 340rpx;
  height: 340rpx;
  margin: 0 auto;
}
.qr .qr_3 {
  font-size: 24rpx;
  font-weight: 400;
  color: #9B9B9B;
  text-align: center;
  margin-top: 50rpx;
}

