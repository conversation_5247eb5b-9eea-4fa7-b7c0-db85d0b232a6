<view><block wx:if="{{!int.openid}}"><view class="hade_vip">完善资料特权</view></block><view class="content_box"><view class="content"><view><button class="content_img" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['chooseavatar',['$event']]]]]}}" bindchooseavatar="__e"><image src="{{img}}" mode></image></button></view><height vue-id="3d7986bd-1" hg="{{80}}" bind:__l="__l"></height><view class="content_input"><view>昵称</view><view><input type="nickname" placeholder="请填写" data-event-opts="{{[['input',[['__set_model',['','name','$event',[]]]]]]}}" value="{{name}}" bindinput="__e"/></view></view><view class="content_input"><view>手机号</view><view><input style="color:#000000;" type="text" placeholder="请填写手机号" data-event-opts="{{[['input',[['__set_model',['','cell','$event',[]]]]]]}}" value="{{cell}}" bindinput="__e"/></view></view><view class="content_input"><view>性别</view><view><u-radio-group vue-id="3d7986bd-2" iconSize="{{20}}" labelSize="{{50}}" size="{{30}}" placement="row" value="{{radiovalue1}}" data-event-opts="{{[['^change',[['groupChange']]],['^input',[['__set_model',['','radiovalue1','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{radiolist1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-radio vue-id="{{('3d7986bd-3-'+index)+','+('3d7986bd-2')}}" customStyle="{{({marginBottom:'8px'})}}" label="{{item.name}}" name="{{item.name}}" data-event-opts="{{[['^change',[['radioChange']]]]}}" bind:change="__e" bind:__l="__l"></u-radio></block></u-radio-group></view></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="content_input" style="border-bottom:none;" bindtap="__e"><view>生日</view><view style="display:flex;align-items:center;"><view>{{''+(birthday||'请选择生日')+''}}</view><view><u-icon vue-id="3d7986bd-4" name="arrow-right" color="#303030" size="28" bind:__l="__l"></u-icon></view></view></view></view></view><view class="account"><view class="content_input" style="border-bottom:none;padding:10rpx 0;"><view style="width:80rpx;height:80rpx;"><image src="../../../static/WeChat.png" mode></image></view><view style="display:flex;align-items:center;"><view></view><view><block wx:if="{{int.openid}}"><text data-event-opts="{{[['tap',[['binding',[0]]]]]}}" bindtap="__e">已绑定</text></block><block wx:else><button open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getphonenumber',['$event']]]]]}}" bindgetphonenumber="__e"><text>未绑定</text></button></block></view></view></view></view><view data-event-opts="{{[['tap',[['sub',['$event']]]]]}}" class="save" bindtap="__e">保存</view><u-datetime-picker vue-id="3d7986bd-5" show="{{show}}" minDate maxDate="{{$root.m0}}" mode="date" value="{{value1}}" data-event-opts="{{[['^cancel',[['e1']]],['^close',[['e2']]],['^confirm',[['time']]],['^input',[['__set_model',['','value1','$event',[]]]]]]}}" bind:cancel="__e" bind:close="__e" bind:confirm="__e" bind:input="__e" bind:__l="__l"></u-datetime-picker></view>