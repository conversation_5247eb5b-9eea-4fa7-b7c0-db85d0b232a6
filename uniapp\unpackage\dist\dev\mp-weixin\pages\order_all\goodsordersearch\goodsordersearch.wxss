@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 颜色变量 */
.container {
  padding-bottom: 120rpx;
  background-color: #F6F7FB;
  min-height: 100vh;
}
.container .course-detail {
  padding: 0 30rpx;
  box-sizing: border-box;
}
.container  .u-popup__content {
  border-radius: 16rpx 16rpx 0rpx 0rpx;
}
.container .coupon-conainer {
  position: relative;
  height: 400rpx;
}
@-webkit-keyframes moveUp {
0% {
    -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
}
50% {
    -webkit-transform: translate(-50%, -220rpx);
            transform: translate(-50%, -220rpx);
}
100% {
    -webkit-transform: translate(-50%, -100rpx);
            transform: translate(-50%, -100rpx);
    /* 上移80px */
}
}
@keyframes moveUp {
0% {
    -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
}
50% {
    -webkit-transform: translate(-50%, -220rpx);
            transform: translate(-50%, -220rpx);
}
100% {
    -webkit-transform: translate(-50%, -100rpx);
            transform: translate(-50%, -100rpx);
    /* 上移80px */
}
}
.container .title-img {
  position: absolute;
  /* 初始位置 */
  top: 250rpx;
  /* 可根据需要调整 */
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  /* 动画设置 */
  -webkit-animation: moveUp 1s ease-in-out forwards;
          animation: moveUp 1s ease-in-out forwards;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #4A4A4C;
}
.container .title-img image {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}
.container .btn-bottom {
  position: fixed;
  bottom: 10rpx;
  width: 690rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  display: flex;
  align-items: center;
}
.container .btn-bottom .btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  color: #fff;
  background-color: #26C8AC;
  border-radius: 0 36rpx 36rpx 0;
}
.container .btn-bottom .btn:first-child {
  color: #26C8AC;
  background-color: #E7FBF7;
  border-radius: 36rpx 0 0 36rpx;
}
.container .custom-navbar {
  height: 88rpx;
  padding: 0 32rpx;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-bottom: 1rpx solid #E5E5E5;
}
.container .custom-navbar .nav-icon {
  width: 48rpx;
  height: 48rpx;
}
.container .custom-navbar .nav-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  color: #333333;
  font-weight: 500;
}
.container .tabsBox {
  background-color: #fff;
  margin-bottom: 20rpx;
}
.container  .u-border-left {
  height: 25rpx !important;
  line-height: 20rpx;
}
.container  .u-border-bottom {
  border-bottom: none !important;
}
.container .u-tabs__wrapper__nav__line {
  width: 40px !important;
  height: 10px !important;
}
.container  .u-tabs__wrapper__nav__item {
  padding: 0 !important;
  width: 25% !important;
  text-align: center !important;
}
.container  .uni-collapse-item__title.uni-collapse-item-border {
  border-bottom: none !important;
}
.container  .uni-collapse {
  border-radius: 20rpx !important;
}
.container  .uni-collapse-item__wrap-content.uni-collapse-item--border {
  border: none !important;
}
.container  .uni-collapse-item__wrap-content .content {
  font-size: 26rpx;
  color: #4A4A4C;
  padding: 25rpx 35rpx;
  box-sizing: border-box;
  border-top: 1rpx solid #B9B9B9;
}
.container  .uni-collapse-item__title {
  border-radius: 24rpx !important;
}
.container  .uni-collapse-item__title-arrow {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background: #26C8AC;
}
.container  .uni-collapse-item__title-arrow .uni-icons {
  color: #fff !important;
}
.container .uni-collapse-item__title.is-open {
  border-radius: 24rpx 24rpx 0 0 !important;
}
.container  .uni-collapse-item__title-text {
  font-weight: bold;
  font-size: 28rpx;
  color: #4A4A4C;
  margin-left: 30rpx;
}
.container  .uni-collapse-item__title-box {
  position: relative;
}
.container  .uni-collapse-item__title-box:before {
  content: '';
  position: absolute;
  width: 14rpx;
  height: 28rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  left: 30rpx;
  background: #26C8AC;
  z-index: 999;
  border-radius: 0rpx 8rpx 0rpx 8rpx;
}
.container view {
  box-sizing: border-box;
}
.container .m-t-30 {
  margin-top: 30rpx;
}
.container .h1-title {
  width: 100%;
  padding: 26rpx 0;
  color: #201E2E;
  text-align: left;
  font-weight: bold;
  font-size: 32rpx;
  text-align: left;
}
.container .top-banner {
  display: flex;
  align-items: center;
  justify-content: center;
}
.container .top-banner image {
  width: 625rpx;
}
.container .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  background-color: #fff;
  padding: 0 30rpx;
  padding-top: 22rpx;
  width: 100%;
  text-align: left;
  color: #060606;
  font-size: 32rpx;
  font-weight: bold;
}
.container .title .title-name {
  width: 100%;
}
.container .title .center {
  font-weight: normal;
  margin-top: 12rpx;
  color: #777777;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  text-align: left;
}
.container .title .center text:first-child {
  padding-right: 13rpx;
}
.container .title .center text:last-child {
  padding-left: 13rpx;
}
.container .title .money {
  margin-top: 25rpx;
  display: inline-block;
  color: #E16965;
  font-size: 34rpx;
  font-weight: bold;
  width: 100%;
  text-align: left;
  padding-bottom: 22rpx;
  font-weight: 400 !important;
}
.container .title .last {
  background-color: #fffae8;
  padding: 15rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-bottom: 20rpx;
  color: #706E6E;
  font-weight: bold;
}
.container .title .extra-info {
  width: 100%;
  font-size: 26rpx;
  color: #706E6E;
  font-weight: normal;
  padding: 26rpx 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.container .title .extra-info text {
  margin-right: 8rpx;
}
.container .title .course-list {
  margin-bottom: 22rpx;
  background-color: #fff;
  border-radius: 16rpx;
  width: 100%;
}
.container .title .course-list .mid {
  margin-top: 6rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.container .title .course-list .mid .tag {
  padding: 0 8rpx;
  height: 48rpx;
  background-color: #EEFAF6;
  color: #009c7b;
  line-height: 48rpx;
  font-size: 22rpx;
  font-weight: bold;
  border-radius: 10rpx;
}
.container .title .course-list .mid .date {
  margin-left: 8rpx;
  font-size: 22rpx;
  color: #A4A4A4;
  font-weight: 400;
}
.container .title .course-list .bottom {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .title .course-list .bottom .teacher-list {
  padding-left: 36rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 20rpx;
}
.container .title .course-list .bottom .teacher-list .teacher-info {
  margin-right: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #818181;
}
.container .title .course-list .bottom .teacher-list .teacher-info .avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 100%;
  margin-bottom: 2rpx;
}
.container .title .course-list .bottom .teacher-list .teacher-info text {
  font-weight: 400;
}
.container .title .course-list .bottom .course-money {
  color: #E16965;
  font-size: 38rpx;
  margin-right: 34rpx;
}
.container .title .teachers {
  width: 100%;
  padding-bottom: 26rpx;
}
.container .title .teachers .all-teacher-info {
  width: 100%;
  white-space: nowrap;
}
.container .title .teachers .all-teacher-info .teacher-item {
  width: 30%;
  display: inline-block;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
}
.container .title .teachers .all-teacher-info .teacher-item .top-info {
  display: flex;
  align-items: center;
  justify-content: center;
}
.container .title .teachers .all-teacher-info .teacher-item .top-info image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 6rpx;
}
.container .title .teachers .all-teacher-info .teacher-item .top-info text {
  font-weight: 400;
}
.container .title .teachers .all-teacher-info .teacher-item .top-info .teacher-extra {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
.container .title .teachers .all-teacher-info .teacher-item .top-info .teacher-extra text {
  font-size: 22rpx;
}
.container .title .teachers .all-teacher-info .teacher-item .top-info .teacher-extra text:first-child {
  color: #818181;
}
.container .title .teachers .all-teacher-info .teacher-item .top-info .teacher-extra text:last-child {
  color: #201E2E;
}
.container .title .teachers .all-teacher-info .teacher-item .bottom-info {
  margin-top: 6rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
}
.container .title .teachers .all-teacher-info .teacher-item .bottom-info text {
  color: #201E2E;
  font-size: 16rpx;
}
.container .tabs {
  width: 100%;
  margin-top: 16rpx;
}
.container .course-dir {
  background-color: #F6F7FB;
  padding: 0 30rpx;
}
.container .course-dir .dir-tab {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.container .course-dir .dir-tab text {
  width: 110rpx;
  height: 60rpx;
  border-radius: 12rpx;
  text-align: center;
  line-height: 60rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #201E2E;
  background-color: #fff;
  margin-right: 20rpx;
}
.container .course-dir .dir-tab .sel {
  background: #26C8AC;
  color: #fff;
}
.container .course-dir .dir-tab-index {
  margin-bottom: 0;
  padding: 0 30rpx;
  box-sizing: border-box;
}
.container .course-dir .live-info {
  margin-top: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx 28rpx;
}
.container .course-dir .live-info .live-title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #0A0A0A;
  font-size: 30rpx;
  font-weight: bold;
}
.container .course-dir .live-info .live-title .green-block {
  width: 14rpx;
  height: 28rpx;
  line-height: 28rpx;
  background: #01997A;
  border-radius: 0rpx 8rpx 0rpx 8rpx;
  margin-right: 6rpx;
}
.container .course-dir .live-info .live-name {
  color: #4A4A4C;
  font-size: 30rpx;
  text-align: left;
}
.container .course-dir .live-info .live-date {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .course-dir .live-info .live-date .date {
  color: #A2A2A2;
  font-size: 24rpx;
}
.container .course-dir .live-info .live-date .date text {
  margin-right: 16rpx;
}
.container .course-dir .live-info .live-date .btn-enter {
  width: 129rpx;
  height: 44rpx;
  background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
  border-radius: 28rpx;
  line-height: 44rpx;
  text-align: center;
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}
.container .course-dir .dir-tabs .u-tabs__wrapper__nav__line {
  width: 40px !important;
  height: 10px !important;
}
.container .course-dir .dir-tabs  .u-tabs__wrapper__nav__item {
  padding: 0 !important;
  width: 20% !important;
  text-align: center !important;
}
.container .course-dir .course-container {
  width: 690rpx;
  margin: 30rpx auto;
  background-color: #fff;
}
.container .course-dir .course-container .title-name {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 28rpx;
}
.container .course-dir .course-container .title-name .left {
  color: #4A4A4C;
  font-size: 28rpx;
  font-weight: bold;
}
.container .course-dir .course-container .title-name .left .green-block {
  display: inline-block;
  width: 14rpx;
  height: 28rpx;
  line-height: 28rpx;
  background: #01997A;
  border-radius: 0rpx 8rpx 0rpx 8rpx;
  margin-right: 6rpx;
}
.container .course-dir .course-container .title-name .expand {
  width: 30rpx;
  height: 30rpx;
  background: #01997A;
  border-radius: 50%;
  display: flex;
  align-items: center;
  transition: -webkit-transform 0.5s ease;
  transition: transform 0.5s ease;
  transition: transform 0.5s ease, -webkit-transform 0.5s ease;
  justify-content: center;
}
.container .course-dir .course-container .title-name .expand .rotate {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.container .course-dir .course-list {
  height: auto;
  overflow: hidden;
  transition: height 0.8s ease;
}
.container .course-dir .course-list .list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 28rpx;
}
.container .course-dir .course-list .list-item .title-text {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 26rpx;
  color: #4A4A4C;
}
.container .course-dir .course-list .list-item .enter {
  width: 130rpx;
  height: 44rpx;
  background: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);
  border-radius: 60rpx;
  color: #fff;
  text-align: center;
  line-height: 44rpx;
  font-weight: bold;
  font-size: 26rpx;
}
.container .padding-dir {
  padding: 0;
}
.container .bottom-opt {
  width: 100%;
  padding: 0 22rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  height: 120rpx;
  background-color: #fff;
  z-index: 9999;
  box-shadow: 0rpx -6rpx 6rpx 1rpx rgba(180, 180, 180, 0.16);
}
.container .bottom-opt .left {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .bottom-opt .left .price {
  font-size: 32rpx;
  color: #E16965;
  margin-right: 24rpx;
}
.container .bottom-opt .btnBox {
  display: flex;
}
.container .bottom-opt .btn,
.container .bottom-opt .car {
  width: 204rpx;
  height: 66rpx;
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  line-height: 66rpx;
  font-weight: bold;
  font-size: 30rpx;
  color: #FFFFFF;
  text-align: center;
  border-radius: 40rpx;
}
.container .bottom-opt .car {
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  margin-right: 24rpx;
}
.container .dir-tab {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 30rpx;
}
.container .dir-tab text {
  width: 110rpx;
  height: 60rpx;
  border-radius: 12rpx;
  text-align: center;
  line-height: 60rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #201E2E;
  background-color: #fff;
  margin-right: 20rpx;
}
.container .dir-tab .sel {
  background: #26C8AC;
  color: #fff;
}
.container .copy-content {
  width: 690rpx;
  box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 30rpx;
  background-color: #fff;
}
.container .copy-content ._p  ._img {
  width: 100% !important;
  border-radius: 20rpx;
}
.container .copy-content .copy-title {
  width: 170rpx;
  height: 40rpx;
  line-height: 36rpx;
  text-align: center;
  font-size: 26rpx;
  color: #01997A;
  border-radius: 363rpx;
  border: 1rpx solid #01997A;
  margin-left: auto;
  justify-content: center;
  margin-bottom: 10rpx;
}
.container .copy-content .content-list {
  font-weight: 400;
  font-size: 26rpx;
  color: #4A4A4C;
  line-height: 1.5;
  margin-bottom: 20rpx;
}
.container .copy-content .content-list .question {
  position: relative;
}
.container .copy-content .content-list .question:before {
  content: "";
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  top: 18rpx;
  left: -15rpx;
  border-radius: 50%;
  background: #01997A;
}
.container .copy-content .content-list .answer {
  margin-top: 5rpx;
  color: #01997A;
}
.container .friends-content {
  width: 690rpx;
  margin: 20rpx auto;
  box-sizing: border-box;
  padding: 20rpx 30rpx 20rpx 40rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  background-color: #fff;
}
.container .friends-content .friends-title {
  font-weight: bold;
  font-size: 28rpx;
  color: #404040;
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}
.container .friends-content .friends-title .green {
  width: 11rpx;
  height: 30rpx;
  background: #01997A;
  margin-right: 8rpx;
  border-radius: 0rpx 8rpx 0rpx 8rpx;
}
.container .friends-content .title-copy {
  font-size: 26rpx;
  color: #01997A;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .friends-content .title-copy text {
  margin-left: 20rpx;
  position: relative;
}
.container .friends-content .copyText {
  width: 140rpx;
  color: #01997A;
  height: 45rpx;
  line-height: 45rpx;
  text-align: center;
  border-radius: 363rpx;
  border: 1rpx solid #01997A;
}
.container .friends-content .contentText {
  width: 400rpx;
  text-align: center;
  line-height: 2;
}
.container .friends-content .contentImg {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-top: 15rpx;
  margin-bottom: 20rpx;
}
.container .friends-content .contentImg .image-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  width: 400rpx;
}
.container .friends-content .contentImg .image-box image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}
.container .mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 99999;
}
.container .mask .share {
  width: 340rpx;
  height: 360rpx;
  position: absolute;
  right: 40rpx;
  top: 20rpx;
}

