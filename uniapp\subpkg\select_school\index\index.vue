<template>
  <view class="content">
    <height :hg="System_height"></height>
    <view class="content_header">
      <view class="nav-title" :style="{ top: System_height + 'rpx' }">
        <text>考研择校</text>
      </view>
      <uni-icons
        type="left"
        size="24"
        color="#fff"
        class="back-left"
        :style="{ top: System_height + 'rpx' }"
        @tap="back"
      ></uni-icons>
      <image
        src="/static/select_school/top_bg.png"
        mode="aspectFit"
        class="header-image"
      ></image>
    </view>

    <view class="ai-section">
      <view class="ai-container">
        <view class="ai-images">
          <image
            src="/static/select_school/ai4-56586a.png"
            class="ai-bg-4"
          ></image>
          <image
            src="/static/select_school/ai1-56586a.png"
            class="ai-bg-1"
          ></image>
          <image
            src="/static/select_school/ai2-56586a.png"
            class="ai-bg-2"
          ></image>
          <view class="ai-center">
            <image
              src="/static/select_school/ai-56586a.png"
              class="ai-main"
            ></image>
            <image
              src="/static/select_school/ai_text-56586a.png"
              class="ai-text"
            ></image>
          </view>
        </view>
        <view class="generate-btn" @click="generateReport">
          <text>点击生成报告</text>
        </view>
        <view class="usage-count">可用次数：2</view>
      </view>
    </view>

    <view class="report-section">
      <view class="section-title">查看报告</view>
      <view class="report-list">
        <view
          class="report-item"
          v-for="(report, index) in reportList"
          :key="index"
          @click="viewReport(report)"
        >
          <view class="report-header">
            <view class="report-name">{{ report.name }}</view>
            <view
              class="report-status"
              :class="
                report.status === '已生成'
                  ? 'status-completed'
                  : 'status-pending'
              "
            >
              {{ report.status }}
            </view>
          </view>
          <view class="report-desc">{{ report.description }}</view>
          <view class="report-footer">
            <view class="report-time">生成时间：{{ report.createTime }}</view>
            <view class="report-detail" @click.stop="viewDetail(report)"
              >查看详情</view
            >
          </view>
        </view>
      </view>
    </view>

    <login :show="enter" @closepage="closepage"></login>
  </view>
</template>

<script>
import { mapState } from "vuex";
export default {
  data() {
    return {
      enter: false,
      reportList: [
        {
          id: 1,
          name: "AI考研择校报告1",
          status: "已生成",
          description: "报告包含专业分析、院校分析、学习计划等针对性综合分析",
          createTime: "2025年8月23日",
        },
        {
          id: 2,
          name: "AI考研择校报告2",
          status: "已生成",
          description: "报告包含专业分析、院校分析、学习计划等针对性综合分析",
          createTime: "2025年8月24日",
        },
      ],
    };
  },
  computed: {
    ...mapState(["System_height"]),
  },
  onLoad() {
    this.checkLogin();
  },
  methods: {
    back() {
      uni.navigateBack();
    },
    checkLogin() {
      const token = uni.getStorageSync("token");
      if (!token) {
        this.enter = true;
      }
    },
    closepage() {
      this.enter = false;
    },
    generateReport() {
      // 跳转到填写报告第一步
      uni.navigateTo({
        url: "/subpkg/select_school/form_step1/form_step1",
      });
    },
    viewReport(report) {
      // 查看报告详情
      console.log("查看报告:", report);
    },
    viewDetail(report) {
      // 查看详情
      console.log("查看详情:", report);
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  min-height: 100vh;
  background: #ffffff;
}

.content_header {
  position: relative;
  height: 489rpx;
  background: linear-gradient(180deg, #00c2a0 0%, #97d9c8 100%);
  overflow: hidden;

  .nav-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;

    text {
      font-size: 34rpx;
      color: #ffffff;
      font-weight: 400;
    }
  }

  .back-left {
    position: absolute;
    left: 30rpx;
    z-index: 10;
  }

  .header-image {
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.ai-section {
  padding: 60rpx 30rpx;
  background: #f6f7fb;

  .ai-container {
    position: relative;

    .ai-images {
      position: relative;
      height: 598rpx;
      margin-bottom: 60rpx;

      .ai-bg-4 {
        position: absolute;
        top: 0;
        left: 159rpx;
        width: 432rpx;
        height: 427rpx;
        z-index: 1;
      }

      .ai-bg-1 {
        position: absolute;
        top: 17rpx;
        left: 175rpx;
        width: 400rpx;
        height: 398rpx;
        z-index: 2;
      }

      .ai-bg-2 {
        position: absolute;
        top: 90rpx;
        left: 180rpx;
        width: 387rpx;
        height: 328rpx;
        z-index: 3;
      }

      .ai-center {
        position: absolute;
        top: 135rpx;
        left: 253rpx;
        width: 240rpx;
        height: 240rpx;
        z-index: 4;

        .ai-main {
          width: 100%;
          height: 100%;
        }

        .ai-text {
          position: absolute;
          top: 61rpx;
          left: 31rpx;
          width: 178rpx;
          height: 121rpx;
        }
      }
    }

    .generate-btn {
      width: 521rpx;
      height: 78rpx;
      background: linear-gradient(135deg, #26c8ac 0%, #19c990 100%);
      border-radius: 39rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20rpx;

      text {
        font-size: 28rpx;
        color: #ffffff;
        font-weight: 400;
      }
    }

    .usage-count {
      text-align: center;
      font-size: 26rpx;
      color: #989898;
    }
  }
}

.report-section {
  padding: 0 30rpx 60rpx;

  .section-title {
    font-size: 30rpx;
    color: #060606;
    margin-bottom: 30rpx;
    padding: 30rpx 0;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 120rpx;
      height: 19rpx;
      background: #dbff9c;
    }
  }

  .report-list {
    .report-item {
      background: #ffffff;
      border-radius: 16rpx;
      padding: 28rpx;
      margin-bottom: 30rpx;
      box-shadow: 0px 8px 8px 0px rgba(235, 235, 235, 1);

      .report-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .report-name {
          font-size: 34rpx;
          color: #00c2a0;
          font-weight: 400;
        }

        .report-status {
          padding: 8rpx 20rpx;
          border-radius: 28rpx;
          font-size: 28rpx;

          &.status-completed {
            background: #d6ffec;
            color: #00c2a0;
          }

          &.status-pending {
            background: #fff3e0;
            color: #ff9800;
          }
        }
      }

      .report-desc {
        font-size: 28rpx;
        color: #5a5a5a;
        line-height: 1.5;
        margin-bottom: 20rpx;
      }

      .report-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .report-time {
          font-size: 26rpx;
          color: #818181;
        }

        .report-detail {
          font-size: 26rpx;
          color: #00c2a0;
        }
      }
    }
  }
}
</style>
