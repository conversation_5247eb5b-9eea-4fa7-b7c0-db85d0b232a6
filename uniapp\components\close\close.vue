<template>
	<!-- 悬浮结算 -->
	<view>
		<view class="box">
			<view class="box_content" @tap="show=true">
				<view class="box_img">
					<image src="@/static/Project_drawing 27.png" mode=""></image>
					<text>{{count||0}}</text>
				</view>
				<view class="box_price">
					<text>总额</text>
					<text style="font-size: 34rpx;">¥</text><text> {{totalPrice||0}}</text>
				</view>
			</view>

			<view class="box_close" @tap="goback()">
				去结算
			</view>
		</view>
		<u-popup :show="show" :round="10" zIndex='10071' mode="bottom" @close="show=false">
			<view class="upcart">
				<view class="upcart_top">
					<view class="upcart_top_left">
						已选商品
					</view>
					<view class="upcart_top_right" @tap="remove()">
						<u-icon name="trash" color="#676767 " size="30"></u-icon>
						<text>清空</text>
					</view>
				</view>
				<view class="upcart_order">
					<view style="margin-top: 150rpx;" v-if="list<1">
						<u-empty mode="data" :iconSize='150' :textSize='24' text='购物车空空如也' icon=""></u-empty>
					</view>
					<scroll-view scroll-y="true" style="height: 570rpx;">
						<view class="upcart_order_content" v-for="(item,index) in list" :key="item">
							<view class="upcart_order_content_img">
								<image :src="item.course_cover" mode=""></image>
							</view>
							<view class="upcart_order_content_title">
								<view>{{item.name}}</view>
								<!-- <view class="flexc" style="height: 40rpx;" @click="specification(item)">
									<view>{{item.name}}</view>
									<view v-if=" item.name" class="" style="padding: 8rpx 0 0rpx 10rpx;">
										<u-icon name='arrow-right' size='24'></u-icon>
									</view>
								</view> -->
								<view class="">
									<text class="money">￥{{item.checkout_price}}</text>
									<view class="">
										<u-number-box v-model="value[index]" @change="valChange">
											<view slot="minus" class="minus"
												@tap="reduce(item,index,item.min_quantity)">
												<u-icon name="minus" labelSize='20px' color="#05B6F6"
													size="24"></u-icon>
											</view>
											<text slot="input" style="width: 50px;text-align: center;"
												class="input">{{value[index]||item.quantity}}</text>
											<view slot="plus" class="plus" @tap="add(item)">
												<u-icon name="plus" labelSize='20px' color="#FFFFFF" size="24"></u-icon>
											</view>
										</u-number-box>
									</view>
								</view>
							</view>
						</view>
						<view class="" style="height: 80rpx;"></view>
					</scroll-view>
				</view>
			</view>
		</u-popup>


	</view>
</template>

<script>
	import {
		order_joinCar,
		order_reduce,
		order_empty,
		jiac,
	} from "@/api/comm.js"
	export default {
		name: "close",
		props: {
			shopping_trolley_list: {},
			seatShow: {
				type: Boolean
			},
			addAdish: {
				default: false
			},
			isAdd: {
				type: Boolean,
				default: false
			},
			showCart: {
				type: Boolean,
				default: false
			},
			pageStatus: {}
		},
		data() {
			return {
				show: false,
				value: [],
				trolley_list: {},
				capsule_button: 2,
				tableNumber: uni.getStorageSync('tableNumber') || false,
				specificationShow: false,
				order_id: "",
				order_type: '',
				editList: [],
				cart_id: '',
				list: uni.getStorageSync('cartItems') || [],
				count: 0,
				totalPrice: 0,
			};
		},

		watch: {
			'pageStatus.onShow'(value) {
				if (value) {
					console.log('子组件：页面已显示')
					this.list = uni.getStorageSync('cartItems') || []
					this.addNum()
				}
			},
			isAdd: function(newval, oldval) {
				this.list = uni.getStorageSync('cartItems') || []
				console.log('list', this.list)
				this.addNum()
			},
			shopping_trolley_list: function(newval, oldval) {
				console.log(newval, oldval, 'watch监听数据变化');
				this.trolley_list = newval
				this.inputlenght()
			},
			showCart(newVal) {
				if (newVal) {
					this.show = true;
				}
			}
		},
		methods: {
			addNum() {
				this.count = 0
				if (!this.list.length) return
				console.log(this.list)
				this.totalPrice = this.list.reduce((total, item) => {
					return total + item.checkout_price * item.quantity;
				}, 0);
				this.list.forEach(item => {
					this.count += item.quantity
					console.log(this.count)
				})

			},

			carList() {
				this.$emit('update')
				this.specificationShow = false
			},
			specification(item) {
				if (item.item_name) {
					this.show = false
					this.order_id = item.goods_id
					this.cart_id = item.id
					this.order_type = item.type
					if (item.type == 2) {
						let arr = item.item_id.split('_')
						this.editList = arr
					} else {
						this.editList = item.spu_info_ys
					}
					setTimeout(() => {
						this.specificationShow = true
						this.$refs.specificationCard.specification()
					}, 500)
				}


			},

			inputlenght() {
				this.value = []
				if (this.trolley_list.data) {
					this.trolley_list.data.forEach((res, index) => {
						this.value.push(res.count)
					})
				}
			},
			reduce(val, index) {
				if (this.count == 1) {
					uni.removeStorageSync('cartItems')
					this.count = 0
					this.totalPrice = 0
				}
				if (val.quantity == 1) {
					this.list = this.list.filter(item => item.id != val.id)
					uni.setStorageSync('cartItems', this.list)

				} else {

					this.list.forEach(item => {
						if (item.id == val.id) {
							item.quantity -= item.min_quantity
						}
					})
					uni.setStorageSync('cartItems', this.list)

				}
				this.addNum()
			},
			add(val) {
				this.list.forEach(item => {
					if (item.id == val.id) {
						item.quantity += item.min_quantity
					}
				})
				uni.setStorageSync('cartItems', this.list)
				this.addNum()
			},
			valChange(e) {
				console.log(e);
			},
			async add_joinCar(item, info) {
				let data = await order_joinCar({
					store_id: item.store_id,
					goods_id: item.goods_id,
					spu_id: item.item_id,
					count: 1,
					spu_info: info,
				})
				this.$emit('update')
			},
			async order_reduce(item, info) {
				let data = await order_reduce({
					store_id: item.store_id,
					goods_id: item.goods_id,
					spu_id: item.item_id,
					count: 1,
					spu_info: info,
					cart_id: item.id
				})
				this.$emit('update')
			},

			// 清空购物车
			async remove() {
				// let data = await order_empty()
				// this.$emit('update')
				this.list = []
				this.count = 0
				this.totalPrice = 0
				uni.removeStorageSync('cartItems')

				// this.show = false
			},



			// 去结算
			goback() {
				if (this.count < 1) {
					uni.showToast({
						title: '请至少选择一个产品',
						icon: 'none'
					})
					return
				}
				console.log(this.count)
				this.$emit('goPay')
				// let user = uni.getStorageSync('user')
				// if (user.order_num == 1) {
				// uni.showModal({
				// 	title: '提示',
				// 	content: '你有未支付订单,请支付过后重新提交',
				// 	success: function(res) {
				// 		if (res.confirm) {
				// 			//未登录
				// 			uni.reLaunch({
				// 				url: '/pages/order_form/order_form'
				// 			})
				// 		} else if (res.cancel) {
				// 			console.log('用户点击取消');
				// 		}
				// 	}
				// });
				// } else {
				// if (this.shopping_trolley_list.count > 0) {
				// 	if (this.shopping_trolley_list.data[0].is_cw == 1) {
				// 		uni.showToast({
				// 			title: '请至少选择一个商品',
				// 			icon: 'none'
				// 		})
				// 	} else {
				// 		let url;
				// 		if (this.seatShow) {
				// 			url = '/pages/order_all/affirm_order/affirm_order'
				// 		} else {
				// let url = '/pages/order_all/affirm_order/affirm_order'

				// 		}
				// 		if (!uni.getStorageSync('userinfo')) {
				// 			this.$emit('register')
				// 			return
				// 		}
				// 		if (this.shopping_trolley_list.count > 0) {

				// 		} else {
				// 			uni.showToast({
				// 				title: '无商品可结算',
				// 				icon: 'none'
				// 			})
				// 		}
				// 	}
				// } else {
				// 	uni.showToast({
				// 		title: '请至少选择一个商品',
				// 		icon: 'none'
				// 	})
				// }


				// }
			}
		}
	}
</script>

<style lang="scss">
	.box {
		width: 700rpx;
		height: 101rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 3rpx 15rpx 0rpx rgba(191, 202, 211, 0.46);
		border-radius: 51rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: fixed;
		bottom: 20rpx;
		left: 50%;
		transform: translateX(-50%);
		z-index: 10072;

		.box_content {
			display: flex;
			align-items: center;
		}

		.box_img {
			width: 70rpx;
			height: 96rpx;
			margin: 0 30rpx;
			margin-left: 50rpx;
			position: relative;

			text {
				width: 40rpx;
				height: 40rpx;
				background-color: #00C2A0;
				border-radius: 50%;
				text-align: center;
				line-height: 40rpx;
				color: #fff;
				position: absolute;
				right: -20rpx;
				font-size: 24rpx;
			}
		}

		.box_price {
			text:nth-child(1) {
				width: 124rpx;
				font-size: 32rpx;
				font-weight: 400;
				color: #414141;
				line-height: 101rpx;
			}

			text:nth-child(2) {
				font-size: 32rpx;
				font-weight: 500;
				color: #FB4E44;
				line-height: 101rpx;
				margin-left: 10rpx;
			}

			text:nth-child(3) {
				font-size: 32rpx;
				font-weight: 500;
				color: #FB4E44;
				line-height: 101rpx;
				margin-left: 10rpx;
			}
		}

		.box_close {
			width: 173rpx;
			height: 100%;
			background: #00C2A0;
			border-radius: 0rpx 89rpx 89rpx 0rpx;
			line-height: 101rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: 600;
			color: #FDFEFF;
		}
	}

	.upcart {
		width: 100%;
		height: 700rpx;

		.upcart_top {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx 30rpx;

			.upcart_top_left {
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
			}

			.upcart_top_right {
				font-size: 24rpx;
				font-weight: 400;
				color: #676767;
				display: flex;
				align-items: center;
			}
		}

		.upcart_order_content {
			display: flex;
			align-items: center;
			padding: 0rpx 20rpx;
			box-sizing: border-box;
			height: 135rpx;
			margin-bottom: 30rpx;

			.upcart_order_content_img {
				width: 136rpx;
				height: 135rpx;
				border-radius: 7rpx;
				overflow: hidden;
			}

			.upcart_order_content_title {
				width: 540rpx;
				margin-left: 20rpx;

				view:nth-child(1) {
					font-size: 28rpx;
					font-weight: 400;
					color: #353535;
				}

				.money {
					height: 45rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #E45F3A;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
			}
		}
	}

	.minus {
		width: 22px;
		height: 22px;
		border-width: 1px;
		border-color: #05B6F6;
		border-style: solid;
		border-top-left-radius: 100px;
		border-top-right-radius: 100px;
		border-bottom-left-radius: 100px;
		border-bottom-right-radius: 100px;
		@include flex;
		justify-content: center;
		align-items: center;
	}

	.input {
		// padding: 0 10px;
	}

	.plus {
		width: 22px;
		height: 22px;
		background-color: #05B6F6;
		border-radius: 50%;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
	}
</style>