<view><view class="hade"><view class="hade_left"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" style="{{'color:'+(index_select==0?'#05B6F6':'')+';'}}" bindtap="__e">附近校区</view><view class="right selectCity"><select-city vue-id="27472ab3-1" type="{{type}}" districtCode="{{stuInfo.pcaCode}}" data-event-opts="{{[['^confirm',[['handleConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></select-city><uni-icons class="icon-right" vue-id="27472ab3-2" type="right" size="16" color="#C3C3C3" bind:__l="__l"></uni-icons></view></view></view><view class="content"><view data-event-opts="{{[['tap',[['position_selection',['$event']]]]]}}" class="content_hade" bindtap="__e"></view><view class="box"><map class="map" style="{{'height:'+(mapshow==0?'399rpx':'100rpx')+';'}}" latitude="{{location.latitude}}" longitude="{{location.longitude}}" markers="{{covers}}" show-location="{{true}}" data-event-opts="{{[['markertap',[['markertap',['$event']]]],['tap',[['tap',['$event']]]],['updated',[['updated',['$event']]]]]}}" bindmarkertap="__e" bindtap="__e" bindupdated="__e"></map><view data-event-opts="{{[['tap',[['mapdisplay',['$event']]]]]}}" class="display" bindtap="__e"><view class="display_text"><block wx:if="{{mapshow==0}}"><view class="display_text_content"><text>收起地图</text><u-icon vue-id="27472ab3-3" name="arrow-up" color="#252525" size="28" bind:__l="__l"></u-icon></view></block><block wx:if="{{mapshow==1}}"><view class="display_text_content"><text>展开地图</text><u-icon vue-id="27472ab3-4" name="arrow-down" color="#252525" size="28" bind:__l="__l"></u-icon></view></block></view></view></view><block wx:if="{{$root.g0<1}}"><view style="margin-top:150rpx;"><u-empty vue-id="27472ab3-5" mode="data" iconSize="{{150}}" textSize="{{24}}" text="暂无内容" icon bind:__l="__l"></u-empty></view></block><block wx:for="{{shoplist}}" wx:for-item="item" wx:for-index="index" wx:key="*this"><view class="position_box"><view class="chosen_position"><view class="chosen_position_left"><view class="chosen_position_left_1">{{''+item.name+''}}</view><view class="chosen_position_left_2"><u-icon vue-id="{{'27472ab3-6-'+index}}" name="map" color="#5A5A5A " size="28" bind:__l="__l"></u-icon><text style="margin-left:5rpx;">{{''+(item.addr||item.selectedPlace)}}</text></view><view class="chosen_position_left_4"><block wx:if="{{item.isOpen==1}}"><view>营业中</view></block><block wx:else><view>已歇业</view></block></view></view><view class="chosen_position_right"><view class="chosen_position_right_1"><label data-event-opts="{{[['tap',[['goplace',['$0',1],[[['shoplist','',index]]]]]]]}}" bindtap="__e" class="_span">去下单</label></view><block wx:if="{{item.distanceText}}"><view class="chosen_position_right_2">{{'距离'+item.distanceText+''}}</view></block><view class="chosen_position_right_3"><view data-event-opts="{{[['tap',[['openTel',['$0'],[[['shoplist','',index,'tel']]]]]]]}}" class="chosen_position_right_3_1" bindtap="__e"><image src="/static/Project_drawing 15.png" mode></image></view><view data-event-opts="{{[['tap',[['getopenLocation',['$0'],[[['shoplist','',index]]]]]]]}}" class="chosen_position_right_3_2" bindtap="__e"><image src="/static/Project_drawing 13.png" mode></image></view></view></view></view></view></block></view></view>