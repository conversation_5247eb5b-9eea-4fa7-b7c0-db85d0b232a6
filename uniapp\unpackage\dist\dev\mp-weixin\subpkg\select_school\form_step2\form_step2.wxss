@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content.data-v-3ad965a2 {
  min-height: 100vh;
  background: linear-gradient(180deg, #CBF2E0 0%, #F6F7FB 43.3%);
  padding-bottom: 160rpx;
}
.content_header.data-v-3ad965a2 {
  position: relative;
  height: 140rpx;
  background: #00C2A0;
}
.content_header .nav-title.data-v-3ad965a2 {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 10;
}
.content_header .nav-title text.data-v-3ad965a2 {
  font-size: 34rpx;
  color: #2D2D2D;
  font-weight: 400;
}
.content_header .back-left.data-v-3ad965a2 {
  position: absolute;
  left: 30rpx;
  z-index: 10;
}
.notification.data-v-3ad965a2 {
  display: flex;
  align-items: center;
  margin: 30rpx;
  padding: 14rpx 28rpx;
  background: #F5FFFD;
  border-radius: 31rpx;
}
.notification .notification-icon.data-v-3ad965a2 {
  width: 49rpx;
  height: 49rpx;
  margin-right: 28rpx;
}
.notification .notification-text.data-v-3ad965a2 {
  font-size: 28rpx;
  color: #5A5A5A;
  flex: 1;
}
.step-indicator.data-v-3ad965a2 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30rpx 0;
}
.step-indicator .step-item.data-v-3ad965a2 {
  margin: 0 81rpx;
}
.step-indicator .step-item .step-number.data-v-3ad965a2 {
  width: 43rpx;
  height: 43rpx;
  border-radius: 50%;
  background: #FF9B3A;
  color: #FFFFFF;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.step-indicator .step-item.active .step-number.data-v-3ad965a2 {
  background: #FF9B3A;
}
.form-title.data-v-3ad965a2 {
  position: relative;
  margin: 30rpx;
}
.form-title .title-bg.data-v-3ad965a2 {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 183rpx;
  height: 19rpx;
  background: #DBFF9C;
}
.form-title .title-text.data-v-3ad965a2 {
  font-size: 30rpx;
  color: #060606;
  font-weight: 400;
}
.create-btn.data-v-3ad965a2 {
  position: absolute;
  right: 30rpx;
  top: 303rpx;
  width: 154rpx;
  height: 66rpx;
  background: #00C2A0;
  border-radius: 33rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.create-btn text.data-v-3ad965a2 {
  font-size: 28rpx;
  color: #FFFFFF;
  margin-left: 10rpx;
}
.form-content.data-v-3ad965a2 {
  padding: 0 30rpx;
}
.form-content .form-item.data-v-3ad965a2 {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 31rpx 28rpx;
  margin-bottom: 30rpx;
  position: relative;
}
.form-content .form-item .form-label.data-v-3ad965a2 {
  font-size: 30rpx;
  color: #504E4E;
  width: 300rpx;
  flex-shrink: 0;
}
.form-content .form-item .form-input.data-v-3ad965a2 {
  flex: 1;
  font-size: 28rpx;
  color: #989898;
  text-align: center;
  margin-right: 20rpx;
  width: 100rpx;
}
.form-content .form-item .delete-btn.data-v-3ad965a2 {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}
.form-content .form-item .delete-btn text.data-v-3ad965a2 {
  font-size: 28rpx;
  color: #989898;
  margin-left: 8rpx;
}
.bottom-buttons.data-v-3ad965a2 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #F6F7FB;
  padding: 30rpx;
  display: flex;
  gap: 30rpx;
}
.bottom-buttons .prev-btn.data-v-3ad965a2 {
  flex: 1;
  height: 80rpx;
  background: #FFFFFF;
  border: 2rpx solid #5A5A5A;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bottom-buttons .prev-btn text.data-v-3ad965a2 {
  font-size: 30rpx;
  color: #5A5A5A;
  font-weight: 400;
}
.bottom-buttons .next-btn.data-v-3ad965a2 {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #26C8AC 0%, #19C990 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bottom-buttons .next-btn text.data-v-3ad965a2 {
  font-size: 30rpx;
  color: #FFFFFF;
  font-weight: 400;
}

