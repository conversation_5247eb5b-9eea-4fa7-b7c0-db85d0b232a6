<template>
	<!-- <view class="page1" v-if="public.isnews==0">
		<oldme :user='user'></oldme>
	</view> -->
	<view class="page">
		<newme ref='newme' :user='user' :asset="asset"></newme>
	</view>
</template>

<script>
	import {
		userInfo
	} from "@/api/public.js"
	export default {
		data() {
			return {
				user: uni.getStorageSync('user'),
				// public: uni.getStorageSync("public"),
				token: uni.getStorageSync("TOKEN"),
				asset: uni.getStorageSync('ASSET') || {}
			};
		},

		onLoad() {
			let token = uni.getStorageSync("TOKEN")
			if (token) {
				this.user = uni.getStorageSync('user')
				this.token = uni.getStorageSync("TOKEN")
				// this.getUser()
			} else {
				this.user = null
			}
		},
		onShow() {
			let token = uni.getStorageSync("TOKEN")
			this.asset = uni.getStorageSync("ASSET") || {}
			if (token) {
				this.user = uni.getStorageSync('user')
				this.token = uni.getStorageSync("TOKEN")
				// this.getUser()
			} else {
				this.$refs.newme.percentage = 0
				this.$refs.newme.token = null
				this.user = null
			}
		},
		methods: {
			// async getUser() {
			// 	let user = await userInfo()
			// 	if (user.code == 1) {
			// 		uni.setStorageSync('user', user.data)
			// 		this.$refs.newme.getUser()
			// 	}
			// },
			contact(e) {
				console.log(e);
			},


		}
	}
</script>
<style lang="scss">
	page {
		min-height: 100vh;
		background-color: #F7F7F7 !important;
	}
</style>