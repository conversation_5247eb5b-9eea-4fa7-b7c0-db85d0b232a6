{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/active/active.vue?79a5", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/active/active.vue?aed3", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/active/active.vue?0169", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/active/active.vue?92c9", "uni-app:///subpkg/report_all/active/active.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/active/active.vue?a16c", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/active/active.vue?3842"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "form", "phone", "smsCode", "activateCode", "codes", "codeParts", "focusIndex", "counting", "timer", "count", "loading", "selectedAgreement", "reportStatus", "from", "onLoad", "computed", "codeText", "fullCode", "methods", "onAgreementChange", "getCode", "uni", "title", "icon", "res", "startCount", "clearInterval", "submit", "role", "url", "console", "handleDelete", "handleInput", "handleH5Paste", "pasteText", "navigator", "e", "cleanText", "wechatPaste", "input", "document", "setTimeout", "resolve", "cleanInputText", "toString", "replace", "slice", "fillCodeInputs", "targetInput", "isInWechat", "viewPrivacyPolicy", "back", "<PERSON><PERSON><PERSON><PERSON>", "created"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACqCpnB;AAIA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MAEA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBAAA,iCACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,iCACAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;gBAAA,OAIA;kBACAtB;gBACA;cAAA;gBAFAuB;gBAIA;kBACAH;oBACAC;oBACAC;kBACA;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAE;MAAA;MACA;MACA;MACA;QACA;UACA;QACA;UACA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAN;kBACAC;kBACAC;gBACA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,KAOA;kBAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAF;kBACAC;kBACAC;gBACA;cAAA;gBAEA;gBACA;gBAAA;gBAGA;gBACAC;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBACAvB;kBACAC;kBACAC;gBACA;cAAA;gBAJAqB;gBAKA;gBACA;gBACA;gBACA,yEACAA;kBACAI;gBAAA,GACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;kBACAzB;gBACA;cAAA;gBAFAqB;cAAA;gBAKA;kBACAH;oBACAQ;kBACA;kBAEAR;oBACAC;oBACAC;kBACA;kBAGA;oBACA;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAO;gBACAT;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAQ;MAAA;MACA;MAEA;QACA;QACA;MACA;QACA;QACA;QACA;;QAEA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;MAEA;QACA;;QAEA;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC,gBAEA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAA;cAAA;gBAAAD;gBAAA;gBAAA;cAAA;gBAAA,KAGAE;kBAAA;kBAAA;gBAAA;gBACAF;gBAAA;gBAAA;cAAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAV;gBACAU;cAAA;gBAGA;gBACAG,8CAGA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAP;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IAEA;IACAQ;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kCACA;kBACA;kBACAC;kBACAA;kBACAC;kBACAD;kBAEAE;oBACA;oBACAD;oBACAE;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA,YACAC,WACAC;MAAA,CACAA;MAAA,CACAC;IACA;IAEA;IACAC;MAAA;MACA;QAAA,OACAV;MAAA,EACA;MACA;MACA;;MAEA;MACA;QAAA;QACA;QACA;UACAW;UACAA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IACAC;MACA;IAAA,CACA;IAEAC;MACA9B;QACAQ;MACA;IACA;EACA;EACA;EACAuB;IACA;MACA1B;IACA;EACA;EACA2B;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA,KACA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1XA;AAAA;AAAA;AAAA;AAAmqC,CAAgB,yoCAAG,EAAC,C;;;;;;;;;;;ACAvrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "subpkg/report_all/active/active.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg/report_all/active/active.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./active.vue?vue&type=template&id=7e26637b&scoped=true&\"\nvar renderjs\nimport script from \"./active.vue?vue&type=script&lang=js&\"\nexport * from \"./active.vue?vue&type=script&lang=js&\"\nimport style0 from \"./active.vue?vue&type=style&index=0&id=7e26637b&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7e26637b\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg/report_all/active/active.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./active.vue?vue&type=template&id=7e26637b&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./active.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./active.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\r\n\t\t<view class=\"head\">\r\n\t\t\t<uni-icons @tap=\"back\" class=\"back-icon\" type=\"left\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t</view>\r\n\t\t<view class=\"login-form\">\r\n\r\n\t\t\t<view class=\"active-code\">\r\n\t\t\t\t<text class=\"active-code-title\">激活码</text>\r\n\t\t\t\t<view class=\"active-code-btn-container\">\r\n\t\t\t\t\t<!-- 修改为 8 个 input 框 -->\r\n\t\t\t\t\t<!-- <input v-for=\"i in 8\" :key=\"i\"  v-model=\"form.activateCodeArray[i - 1]\"\r\n\t\t\t\t\t\ttype=\"text\" maxlength=\"1\" @input=\"onInput(i - 1)\"> -->\r\n\t\t\t\t\t<!-- <input v-for=\"(code, index) in codes\" :key=\"index\" v-model=\"codes[index]\" :ref=\"`input${index}`\"\r\n\t\t\t\t\t\tclass=\"active-code-btn\" maxlength=\"1\" @input=\"handleInput(index, $event)\"\r\n\t\t\t\t\t\****************=\"handleDelete(index, $event)\" /> -->\r\n\t\t\t\t\t<input v-for=\"(item, index) in 8\" :key=\"index\" v-model.trim=\"codeParts[index]\" :id=\"'input' + index\"\r\n\t\t\t\t\t\tclass=\"active-code-btn\" type=\"text\" maxlength=\"1\" :focus=\"focusIndex === index\"\r\n\t\t\t\t\t\t@input=\"handleInput(index, $event)\" @keydown.delete.prevent=\"handleDelete(index, $event)\"\r\n\t\t\t\t\t\***************=\"handleH5Paste\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 登录按钮 -->\r\n\t\t\t<button :loading=\"loading\" class=\"login-btn\" @click=\"submit\">立即激活</button>\r\n\t\t</view>\r\n\t\t<view class=\"enter-pay\">\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<image src=\"@/static/car.png\" mode=\"\"></image>\r\n\t\t\t\t<text>如无激活码，请购买大学入学发展报告</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn\">去购买</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetActivecode,\r\n\t\tsmsRegLogin,\r\n\t\treportActivate\r\n\t} from '@/api/user.js'\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tform: {\r\n\t\t\t\t\tphone: '',\r\n\t\t\t\t\tsmsCode: '',\r\n\t\t\t\t\tactivateCode: '',\r\n\r\n\t\t\t\t},\r\n\t\t\t\tcodes: ['', '', '', '', '', ''],\r\n\t\t\t\tcodeParts: Array(8).fill(''), // 8个空字符串\r\n\t\t\t\tfocusIndex: 0,\r\n\t\t\t\tcounting: false,\r\n\t\t\t\ttimer: null,\r\n\t\t\t\tcount: 60,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tselectedAgreement: '', // 存储选中状态\r\n\t\t\t\treportStatus: 0, //0: 未激活   1：服务已激活  2. 服务已使用\r\n\t\t\t\tfrom: 0, //0：客户录入  1：激活码注册 \r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tcodeText() {\r\n\t\t\t\treturn this.counting ? `${this.count}s后重新获取` : '获取验证码'\r\n\t\t\t},\r\n\t\t\tfullCode() {\r\n\t\t\t\treturn this.codeParts.join('');\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 单选框变更事件\r\n\t\t\tonAgreementChange(e) {\r\n\t\t\t\tthis.selectedAgreement = e.detail.value\r\n\t\t\t},\r\n\t\t\t// 获取验证码\r\n\t\t\tasync getCode() {\r\n\t\t\t\tif (this.counting) return\r\n\r\n\t\t\t\t// 验证手机号\r\n\t\t\t\tif (!this.form.phone) {\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.form.phone)) {\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await getActivecode({\r\n\t\t\t\t\t\tphone: this.form.phone\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\tif (res.errCode === 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '验证码已发送',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.startCount()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '发送失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '发送失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 启动倒计时\r\n\t\t\tstartCount() {\r\n\t\t\t\tthis.counting = true\r\n\t\t\t\tthis.count = 60\r\n\t\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\t\tif (this.count > 0) {\r\n\t\t\t\t\t\tthis.count--\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.counting = false\r\n\t\t\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\r\n\t\t\t// 提交激活\r\n\t\t\tasync submit() {\r\n\t\t\t\tif (this.loading) return\r\n\t\t\t\t// 如果用户有token就不需要验证手机号和验证码以及用户协议\r\n\t\t\t\tif (!this.$store.getters.token) {\r\n\t\t\t\t\t// 表单验证\r\n\t\t\t\t\tif (!this.form.phone) {\r\n\t\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请输入手机号',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.form.phone)) {\r\n\t\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请输入正确的手机号',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.form.smsCode) {\r\n\t\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请输入验证码',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!this.selectedAgreement) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请先同意协议',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t\t// 检查激活码数组是否都有值\r\n\t\t\t\tif (this.codeParts.some(code => code === '')) {\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入完整的激活码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// 将激活码数组拼接成字符串\r\n\t\t\t\tthis.form.activateCode = this.codeParts.join('');\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.loading = true\r\n\t\t\t\t\tlet res = {}\r\n\t\t\t\t\tif (!this.$store.getters.token) {\r\n\t\t\t\t\t\tres = await smsRegLogin({\r\n\t\t\t\t\t\t\tphone: this.form.phone,\r\n\t\t\t\t\t\t\tsmsCode: this.form.smsCode,\r\n\t\t\t\t\t\t\tactivateCode: this.form.activateCode\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t// 保存 token\r\n\t\t\t\t\t\tthis.$store.commit('user/setToken', res.data)\r\n\t\t\t\t\t\t// 更新用户信息\r\n\t\t\t\t\t\tthis.$store.commit('user/setUserInfo', {\r\n\t\t\t\t\t\t\t...res.student,\r\n\t\t\t\t\t\t\trole: 'student'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\r\n\t\t\t\t\t\tres = await reportActivate({\r\n\t\t\t\t\t\t\tactivateCode: this.form.activateCode\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (res.errCode == 0) {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/subpkg/report/report'\r\n\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '激活成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\r\n\r\n\t\t\t\t\t\tif (this.$store.getters.token) {\r\n\t\t\t\t\t\t\tthis.$store.dispatch('user/getReportData')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg || '激活失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.log(e)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '激活失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.loading = false\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\thandleDelete(index, e) {\r\n\t\t\t\tconst currentValue = this.codeParts[index];\r\n\r\n\t\t\t\tif (currentValue) {\r\n\t\t\t\t\t// 情况1：当前有值，直接删除\r\n\t\t\t\t\tthis.codeParts.splice(index, 1, '');\r\n\t\t\t\t} else if (index > 0) {\r\n\t\t\t\t\t// 情况2：当前为空，删除前一位\r\n\t\t\t\t\tthis.codeParts.splice(index - 1, 1, '');\r\n\t\t\t\t\tthis.focusIndex = index - 1;\r\n\r\n\t\t\t\t\t// 安全访问ref（关键修复）\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tconst refName = `input${index - 1}`;\r\n\t\t\t\t\t\tif (this.$refs[refName] && typeof this.$refs[refName].focus === 'function') {\r\n\t\t\t\t\t\t\tthis.$refs[refName].focus();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\thandleInput(index, e) {\r\n\t\t\t\tconst value = e.target.value.replace(/[^a-zA-Z0-9]/g, '');\r\n\t\t\t\tthis.codeParts.splice(index, 1, value.slice(0, 1));\r\n\r\n\t\t\t\tif (value && index < 7) {\r\n\t\t\t\t\tthis.focusIndex = index + 1;\r\n\r\n\t\t\t\t\t// 安全跳转（跨平台兼容）\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tconst refName = `input${index + 1}`;\r\n\t\t\t\t\t\tif (this.$refs[refName] && typeof this.$refs[refName].focus === 'function') {\r\n\t\t\t\t\t\t\tthis.$refs[refName].focus();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync handleH5Paste(e) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 1. 安全获取剪贴板数据\r\n\t\t\t\t\tlet pasteText = '';\r\n\r\n\t\t\t\t\t// 方法1：标准H5 API（需要HTTPS环境）\r\n\t\t\t\t\tif (navigator.clipboard && !this.isInWechat()) {\r\n\t\t\t\t\t\tpasteText = await navigator.clipboard.readText()\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 方法2：降级处理（兼容HTTP和微信浏览器）\r\n\t\t\t\t\telse if (e.clipboardData) {\r\n\t\t\t\t\t\tpasteText = e.clipboardData.getData('text')\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 方法3：兼容微信浏览器\r\n\t\t\t\t\telse if (this.isInWechat()) {\r\n\t\t\t\t\t\tconst res = await this.wechatPaste()\r\n\t\t\t\t\t\tpasteText = res\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 3. 数据清洗处理\r\n\t\t\t\t\tconst cleanText = this.cleanInputText(pasteText)\r\n\r\n\r\n\t\t\t\t\t// 4. 填充输入框\r\n\t\t\t\t\tthis.fillCodeInputs(cleanText)\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('粘贴失败:', error)\r\n\t\t\t\t} finally {\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 微信浏览器特殊处理\r\n\t\t\tasync wechatPaste() {\r\n\t\t\t\treturn new Promise((resolve) => {\r\n\t\t\t\t\tconst input = document.createElement('input')\r\n\t\t\t\t\tinput.style.position = 'fixed'\r\n\t\t\t\t\tinput.style.opacity = 0\r\n\t\t\t\t\tdocument.body.appendChild(input)\r\n\t\t\t\t\tinput.focus()\r\n\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tconst text = input.value\r\n\t\t\t\t\t\tdocument.body.removeChild(input)\r\n\t\t\t\t\t\tresolve(text)\r\n\t\t\t\t\t}, 100)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 数据清洗方法\r\n\t\t\tcleanInputText(text) {\r\n\t\t\t\treturn text\r\n\t\t\t\t\t.toString()\r\n\t\t\t\t\t.replace(/\\s/g, '') // 去除空格\r\n\t\t\t\t\t.replace(/[^a-zA-Z0-9]/g, '') // 过滤特殊字符\r\n\t\t\t\t\t.slice(0, 8)\r\n\t\t\t},\r\n\r\n\t\t\t// 填充输入框逻辑\r\n\t\t\tfillCodeInputs(cleanText) {\r\n\t\t\t\tthis.codeParts = Array(8).fill('').map((_, i) =>\r\n\t\t\t\t\tcleanText[i] || ''\r\n\t\t\t\t)\r\n\t\t\t\tconst filledLength = Math.min(cleanText.length, 7)\r\n\t\t\t\tthis.focusIndex = filledLength\r\n\r\n\t\t\t\t// 自动聚焦\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tconst targetInput = this.$refs[`input${filledLength}`]?.[0]?.$el\r\n\t\t\t\t\tif (targetInput) {\r\n\t\t\t\t\t\ttargetInput.focus()\r\n\t\t\t\t\t\ttargetInput.setSelectionRange(1, 1) // 光标定位末尾\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 判断是否在微信浏览器\r\n\t\t\tisInWechat() {\r\n\t\t\t\tconst ua = navigator.userAgent.toLowerCase()\r\n\t\t\t\treturn ua.includes('micromessenger')\r\n\t\t\t},\r\n\t\t\tviewPrivacyPolicy() {\r\n\t\t\t\t// 实现查看隐私政策的功能\r\n\t\t\t},\r\n\r\n\t\t\tback() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t},\r\n\t\t// 组件销毁时清除定时器\r\n\t\tbeforeDestroy() {\r\n\t\t\tif (this.timer) {\r\n\t\t\t\tclearInterval(this.timer)\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync created() {\r\n\t\t\tif (this.$store.getters.token) {\r\n\t\t\t\tawait this.$store.dispatch('user/getReportData') // 初始化 org\r\n\t\t\t}\r\n\t\t\tthis.from = this.$store.state.user.userInfo.from\r\n\t\t\tthis.reportStatus = this.$store.state.user.userInfo.reportStatus\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.container {\r\n\t\tbox-sizing: border-box;\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t.head {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 500rpx;\r\n\t\t\tbackground-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/login_header_img.png');\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: 100% 100%;\r\n\r\n\t\t\t.back-icon {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 30rpx;\r\n\t\t\t\tleft: 30rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.login-form {\r\n\t\tmargin-top: -134rpx;\r\n\t\tpadding: 0 28rpx;\r\n\t\tpadding-top: 80rpx;\r\n\t\tborder-top-left-radius: 36rpx;\r\n\t\tborder-top-right-radius: 36rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.form {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.input-group {\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\tinput {\r\n\t\tpadding: 10px;\r\n\t\tfont-size: 16px;\r\n\t\tborder: none;\r\n\t\tborder-bottom: 1px solid #ccc;\r\n\t\toutline: none;\r\n\t}\r\n\r\n\t.active-code {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tpadding-left: 20rpx;\r\n\t}\r\n\r\n\t.uni-input-placeholder {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #A5A5A5;\r\n\t}\r\n\r\n\t.active-code-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #A5A5A5;\r\n\t}\r\n\r\n\t.active-code-btn-container {\r\n\t\tmargin-top: 20px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\r\n\t\t.active-code-btn {\r\n\t\t\twidth: 70rpx;\r\n\t\t\theight: 70rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tbox-shadow: 0rpx 3rpx 6rpx 1rpx #D6D6D6, inset 0rpx 3rpx 6rpx 1rpx #C1F1DA;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tborder: 1rpx solid #26C8AC;\r\n\t\t\tfont-size: 34rpx;\r\n\t\t\tcolor: #5A5A5A;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 70rpx;\r\n\t\t\t/* 添加输入框样式 */\r\n\t\t\tpadding: 0;\r\n\t\t\tmargin: 0;\r\n\t\t\toutline: none;\r\n\t\t}\r\n\t}\r\n\r\n\tinput:focus {\r\n\t\tborder-bottom: 1px solid #1BB394;\r\n\t}\r\n\r\n\t.code-group {\r\n\t\tmargin-bottom: 20px;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.code-input {\r\n\t\tflex: 1;\r\n\t\t/* 使输入框占据剩余空间 */\r\n\t\tpadding: 10px;\r\n\t\tfont-size: 16px;\r\n\t\tborder: none;\r\n\t\tborder-bottom: 1px solid #ccc;\r\n\t\toutline: none;\r\n\t}\r\n\r\n\t.code-input:focus {\r\n\t\tborder-bottom: 1px solid #1BB394;\r\n\t}\r\n\r\n\t.code-btn {\r\n\t\twidth: 160rpx;\r\n\t\tpadding: 14rpx;\r\n\t\tbackground-color: #26C8AC;\r\n\t\tposition: absolute;\r\n\t\tright: 10rpx;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-65%);\r\n\t\tcolor: white;\r\n\t\tfont-size: 22rpx;\r\n\t\tmargin-left: 10px;\r\n\t\tborder-radius: 4px;\r\n\t\tcursor: pointer;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.code-btn:disabled {\r\n\t\tbackground-color: #ccc;\r\n\t}\r\n\r\n\t.login-btn {\r\n\r\n\t\twidth: 80%;\r\n\t\theight: 45px;\r\n\t\tpadding: 12px;\r\n\t\tfont-size: 16px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 30px;\r\n\t\tbackground: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);\r\n\t\tcolor: white;\r\n\t\tcursor: pointer;\r\n\t\tmargin: 120rpx auto;\r\n\t}\r\n\r\n\t.agreement {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 20px;\r\n\t\tmargin-top: 70rpx;\r\n\t}\r\n\r\n\t.radio-group {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.radio-label {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.agreement-text {\r\n\t\tcolor: red;\r\n\t\tcursor: pointer;\r\n\t\tmargin-left: 5px;\r\n\t}\r\n\r\n\t.enter-pay {\r\n\t\twidth: 750rpx;\r\n\t\theight: 126rpx;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tpadding: 0 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tbackground: #F6F7FB;\r\n\r\n\t\t.title {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #A5A5A5;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 70rpx;\r\n\t\t\t\theight: 70rpx;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.btn {\r\n\t\t\twidth: 145rpx;\r\n\t\t\theight: 60rpx;\r\n\t\t\tline-height: 60rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tbackground: #26C8AC;\r\n\t\t\tborder-radius: 14rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./active.vue?vue&type=style&index=0&id=7e26637b&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./active.vue?vue&type=style&index=0&id=7e26637b&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557566439\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}