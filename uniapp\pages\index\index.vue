<template>
	<view class="content">
		<view class="header">
			<!-- add高度组件 -->
			<view class="img_box">
				<view class="img_box">
					<u-swiper :list="banner" :indicator='true' keyName="image" indicatorMode='dot' height='455rpx'
						@click="hadeswp"></u-swiper>
				</view>
				<view>

				</view>
				<view class="user">
					<!-- @tap="routergo('/pages/me_all/personage/personage')" -->
					<view class="user_head_portrait">
						<image v-if="userall.avatar" :src="userall.avatar" mode=""></image>
						<image v-else src="../../static/wa.png" mode=""></image>
					</view>
					<!-- '/pages/me_all/personage/personage' -->
					<view class="user_2" @tap="routergo()">
						<view class="user_2_1">
							{{userall.nickname||'请登录'}}
						</view>
						<view class="user_2_2">
							蝌蚪币:<text class="num">
								<!-- {{userall.score||0}} -->
								{{asset.point || 0}}
							</text>
							<text class="left">优惠券:<text class="num">{{asset.couponCnt || 0}}</text></text>
						</view>
						<!-- <view class="user_2_3">
							<u-line-progress :percentage="userall.score/userall.upgrade*100" activeColor="#05B6F6"
								:showText="false"></u-line-progress>
						</view> -->
					</view>
					<view class="user_3" @tap="routergo('/pages/me_all/codeqr/codeqr')">
						<view class="user_code">
							<image src="@/static/Project_drawing 23.png" mode=""></image>
						</view>
						<text>会员码</text>
					</view>
				</view>
			</view>
			<view class="grid">
				<view class="grid-item" v-for="item in  gridList" :key="item.id" @click="toTarget(item)">
					<image mode="widthFix" :src="item.image"></image>
					<text>{{item.name}}</text>
				</view>
			</view>

			<!-- <view class="card">
				<view class="card_1 flexw">
					<view class="card_1_left" @tap="router(1)" v-if="common.iszq==1">
						<view class="card_1_left_title">
							<view>自取</view>
							<view>下单免排队</view>
						</view>
						<view class="card_1_left_img">
							<image src="@/static/Project_drawing 28.png" mode=""></image>
						</view>
					</view>

					<view class="card_1_right" @tap="router(2)" v-if="common.iswm==1">
						<view class="card_1_left_title">
							<view>外卖</view>
							<view>美食送到家</view>
						</view>
						<view class="card_1_left_img">
							<image src="@/static/Project_drawing 29.png" mode=""></image>
						</view>
					</view>

					<view class="card_1_right" @tap="scanCode(3)" v-if="common.ists==1"
						style="margin-top: 16rpx;background-color: #00acbb;color: #fff;">
						<view class="card_1_left_title">
							<view>扫码点餐</view>
							<view>扫码座号二维码即可点餐</view>
						</view>
						<view class="card_1_left_img">
							<image src="@/static/saoma_bg.png" mode=""></image>
						</view>
					</view>

					<view class="card_1_left" v-if="common.isyy==1" style="margin-top: 16rpx;background-color: #adeae6;"
						@tap="routerTo('/pages/reserved/reserved')">
						<view class="card_1_left_title">
							<view>提前预约</view>
							<view>提前预约，到店直接就餐</view>
						</view>
						<view class="card_1_left_img">
							<image src="@/static/yuyue_bg.png" mode=""></image>
						</view>
					</view>
				</view>
				<view class="card_2">
					<button open-type="contact" @contact="contact">
						<view class="card_2_content">
							<view class="card_2_content_left">
								<view>加盟咨询</view>
								<view>{{public.tel}}</view>
							</view>
							<view class="card_2_content_right">
								<image src="../../static/Project_drawing 3.svg" mode=""></image>
							</view>
						</view>
					</button>
					<view class="card_2_content" @tap="routerTo('/pages/me_all/integral/integral')">
						<view class="card_2_content_left">
							<view>积分商城</view>
							<view>礼券宝物全都有</view>
						</view>
						<view class="card_2_content_right">
							<image src="../../static/Project_drawing 4.svg" mode=""></image>
						</view>
					</view>

				</view> -->
			<!-- 	<view class="card_2">
					<view class="card_2_content" @tap="routergo('/pages/wallet/wallet')">
						<view class="card_2_content_left">
							<view>我的钱包</view>
							<view>礼券宝物全都有</view>
						</view>
						<view class="card_2_content_right">
							<image src="../../static/Project_drawing 4.svg" mode=""></image>
						</view>
					</view>
				</view> -->
			<!-- <view class="card_3">
					<u-swiper :list="list2" keyName="image" :indicator='true' indicatorMode='dot' :height='314'
						@click="swp"></u-swiper>
				</view>
				<view class="beianhao" v-if="common.mini_filings">
					备案号：{{common.mini_filings || ''}}
				</view>
			</view>

			<login :show="enter" @closepage='closepage'></login>
		</view>
		<height :hg='40'></height>
		<barcode id='1' class="barcode" autostart="true" ref="barcode" background="rgb(0,0,0)" frameColor="#1C86EE"
			scanbarColor="#1C86EE" :filters="fil" @marked="success1" @error="fail1"></barcode> -->
		</view>
		<view class="scrollBox">
			<scroll-view class="menu" scroll-x>
				<!-- 修改后的菜单项 -->
				<view :class="['menu-item',activeIndex === index ? 'active' : '']" v-for="(item, index) in menuItems"
					:key="item.id" @tap="setActiveIndex(index,item.id)">
					{{ item.name }}
				</view>
			</scroll-view>
		</view>
		<view class="productList">
			<view class="product" v-if="list.length>0">
				<image class="da" :src="activity[0].image" alt="" />
				<view class="productItem" v-for="(item,index) in list" :key="item.id"
					@tap="routergo('/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id,1)">
					<template v-if="index%2!=0">
						<image class="head" :src="item.course_cover" alt="" />
						<view class="bottom">
							<view class="title">{{item.name}}</view>
							<view class="desc">{{item.handout_type_name}} {{item.teaching_mode_name}}</view>
							<view class="money">
								<text>￥{{item.checkout_price}}</text>
								<view class="add" v-if="!userall.user">添加</view>
							</view>
						</view>
					</template>
				</view>

			</view>
			<view class="product" v-if="list.length>0">
				<view class="productItem" v-for="(item,index) in list" :key="item.id"
					@tap="routergo('/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id,1)">
					<template v-if="index%2==0">
						<image class="head" :src="item.course_cover" alt="" />
						<view class="bottom">
							<view class="title">{{item.name}}</view>
							<view class="desc">{{item.handout_type_name}} {{item.teaching_mode_name}}</view>
							<view class="money">
								<text>￥{{item.checkout_price}}</text>
								<view class="add" v-if="!userall.user">添加</view>
							</view>
						</view>
					</template>
				</view>

			</view>

		</view>
		<view class="noenList" v-if="!list.length">暂无产品内容</view>
		<uni-load-more v-if="list.length>0" @clickLoadMore="getMore" :contentText='contentText'
			:status="loadMoreStatus"></uni-load-more>
		<login :show="enter" @closepage='closepage'></login>
	</view>
</template>
<script>
	// import {
	// 	userInfo,
	// 	common
	// } from "@/api/public.js"
	import {
		getMainCategory,
		getProductList,
		getIndexContent
	} from "@/api/comm.js"
	import {
		assign,
		forEach
	} from "lodash"
	import route from 'uview-ui/libs/util/route'
	export default {
		data() {
			return {
				enter: false,
				title: 'Hello',
				// 轮播图
				list1: [],
				list2: [],
				gridList: [],
				menuItems: [],
				activeId: '',
				activeIndex: 0,
				list: [],
				pageInfo: {
					total: 0,
					page: 1
				},
				loadMoreStatus: 'more',
				contentText: {
					contentdown: "点击加载更多",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				userall: uni.getStorageSync('user'),
				public: {},
				display: false,
				enter: false,
				banner: [], //轮播
				activity: {}, //抽奖
				asset: uni.getStorageSync('ASSET') || {}
			}
		},
		onLoad() {



			// this.getDetails()
		},
		onShow() {
			this.list = []
			this.getDetails()
			this.asset = uni.getStorageSync("ASSET") || {}
			// this.userInfoApi()
			// this.enter = false
			// this.commonApi()
			// this.getUser()
			this.userall = uni.getStorageSync('user') || {}
			// if () {

			// }
			if (uni.getStorageSync('TOKEN')) {
				this.getCategory().then(() => {
					this.getList()
				})
			} else {
				// 轮询检查token
				const timer = setInterval(() => {
					if (uni.getStorageSync('TOKEN')) {
						clearInterval(timer);
						this.getCategory().then(() => {
							this.getList()
						})
					}
				}, 500);
			}
		},
		methods: {
			// 点击入口菜单
			toTarget(item) {
				this.userall = uni.getStorageSync('user')

				if (item.appid) {
					wx.navigateToMiniProgram({
						appId: item.appid, // 必填
					});
				} else if (item.link) {
					if (uni.getStorageSync('TOKEN') && uni.getStorageSync('user')) {
						uni.navigateTo({
							url: item.link
						})
					} else {
						this.enter = true
						return
					}
				}


				// if (item.name == "入学报告") {
				// 	if (this.userall.student.reportStatus == 0) {
				// 		uni.navigateTo({
				// 			url: '/subpkg/report_all/active/active'
				// 		})
				// 	} else if (this.userall.student.reportStatus == 1) {
				// 		uni.navigateTo({
				// 			url: '/subpkg/report_all/plan/plan'
				// 		})
				// 	} else if (this.userall.student.reportStatus == 2) {
				// 		uni.navigateTo({
				// 			url: '/subpkg/report_all/report_view/report_view'
				// 		})
				// 	}
				// }
				// if (item.name == '大学陪跑') {
				// 	uni.navigateTo({
				// 		url: '/subpkg/report_all/university_companion/university_companion'
				// 	})
				// }
				// } else {
				// uni.navigateTo({
				// 	url: item.path
				// })
				// }

			},
			//获取轮播
			async getDetails() {
				const {
					data,
					errCode,
					msg
				} = await getIndexContent()
				if (errCode == 0) {
					this.banner = data.banner.map(item => {
						return {
							...item,
							image: 'https://' + item.image
						}
					})
					console.log(this.banner)
					this.gridList = data.menu.map(item => {
						return {
							...item,
							image: 'https://' + item.image
						}
					})
					this.activity = data.activity.map(item => {
						return {
							...item,
							image: 'https://' + item.image
						}
					})
				} else {

				}
			},
			routergo(url, num) {
				if (num == 1) {
					uni.navigateTo({
						url: url
					})
					return
				} else {
					let token = uni.getStorageSync('TOKEN')
					let user = uni.getStorageSync('user')
					if (token && user) {
						if (url) {
							uni.navigateTo({
								url: url
							})
						}

					} else {
						this.enter = true
						console.log('243343443', this.enter);
					}
				}


			},
			closepage(data) {
				this.enter = false
				this.userall = uni.getStorageSync('user')
				this.asset = data
			},
			// 获取首页产品列表分类
			async getCategory() {
				const {
					data,
					errCode,
					msg
				} = await getMainCategory()
				if (errCode == 0) {
					this.menuItems = data
					console.log(this.menuItems)
					this.activeId = this.menuItems[0].id
				}
			},
			async getList() {
				const {
					data,
					errCode,
					msg
				} = await getProductList({
					categoryId: this.activeId || '',
					campusld: '',
					page: this.pageInfo.page,
					limit: 10,
				})
				if (errCode == 0) {
					this.list = [...this.list, ...data.data]
					this.pageInfo.total = data.total
					if (this.list.length >= this.pageInfo.total) {
						this.loadMoreStatus = "noMore"
					} else {
						this.loadMoreStatus = "more"
					}
				}
			},
			setActiveIndex(index, id) {
				this.pageInfo.page = 1
				this.list = []
				this.activeIndex = index
				this.activeId = id
				this.getList()
			},
			getMore() {
				if (this.list.length >= this.pageInfo.total) {
					this.loadMoreStatus = "noMore"
					return
				}
				this.pageInfo.page += 1
				this.getList()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.beianhao {
		margin: 20px 0;
		text-align: center;
		font-size: 24rpx;
		color: #b5b5b5;
	}

	.scrollBox {
		padding: 0 30rpx;
		box-sizing: border-box;

		.menu {
			display: flex;
			align-items: center;
			// white-space: nowrap;
			scrollbar-width: none;
			-ms-overflow-style: none;

			&::-webkit-scrollbar {
				display: none;
			}
		}

		.menu-item {
			display: inline-block;
			// 适当减小外边距
			// width: 195rpx;
			height: 46rpx;
			line-height: 46rpx;
			text-align: center;
			// font-weight: bold;
			// padding-bottom: 5rpx;
			font-size: 35rpx;
			color: #636363;
			margin-right: 30rpx;
			transition: all 0.3s ease;

			&:last-child {
				margin-right: 0;
			}


		}

		.active {
			color: #343434;
			font-size: 32rpx;
			font-weight: bold;
			border-bottom: 6rpx solid #00C2A0;
		}
	}

	.noenList {
		width: 690rpx;
		height: 300rpx;
		line-height: 300rpx;
		text-align: center;
		background: #FFFFFF;
		border-radius: 14rpx;
		background: #FFF;
		border-radius: 16rpx;
		margin: 0rpx auto 100rpx;
		padding: 0rpx 20rpx;

	}

	.productList {
		margin-top: 30rpx;
		display: flex;
		justify-content: space-between;
		padding: 0 30rpx;

		.product {
			width: 338rpx;
			// height: 482rpx;
			// background: #FFFFFF;

			.da {
				width: 338rpx;
				height: 394rpx;
				margin-bottom: 20rpx;
				border-radius: 20rpx;
			}

			.productItem {
				margin-bottom: 20rpx;
				background-color: #fff;
				border-radius: 20rpx;

				&:first-child {
					margin-bottom: 0;
				}

				.head {
					width: 338rpx;
					height: 252rpx;
				}

				.bottom {
					padding: 0 20rpx;

					.title {
						width: 100%;
						margin-top: 24rpx;
						margin-bottom: 8rpx;
						font-weight: 800;
						font-size: 30rpx;
						color: #414141;
					}

					.desc {
						font-weight: 400;
						font-size: 22rpx;
						color: #777777;
					}

					.money {
						margin-top: 60rpx;
						font-size: 32rpx;
						color: #FB4E44;
						padding-bottom: 20rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.add {
							width: 84rpx;
							height: 45rpx;
							line-height: 45rpx;
							text-align: center;
							font-weight: bold;
							font-size: 22rpx;
							color: #FFFFFF;
							background: #00C2A0;
							border-radius: 30rpx;
						}
					}

				}

			}
		}
	}

	.grid {
		width: 690rpx;
		margin: 0 auto;
		// padding: 36rpx 52rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		align-content: center;
		font-size: 24rpx;
		margin-bottom: 38rpx;
		color: #323232;
		background-color: #fff;
		border-radius: 18rpx;
		padding-bottom: 30rpx;

		.grid-item {

			width: 25%;
			margin-top: 16rpx;

			image {
				width: 100rpx;
			}

			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: center;
		}
	}

	.img_box {
		width: 750rpx;
		height: 570rpx;

		::v-deep .u-swiper__indicator {
			position: absolute;
			bottom: 80rpx !important;
		}

		.img_box {
			position: absolute;
		}

		.user {
			width: 704rpx;
			height: 136rpx;
			background: #FFFFFF;
			border-radius: 18rpx;
			display: flex;
			align-items: center;
			margin: 0 auto;
			position: relative;
			top: 400rpx;

			.user_head_portrait {
				width: 89rpx;
				height: 89rpx;
				border-radius: 50%;
				overflow: hidden;
				margin-left: 32rpx;
			}

			.user_2 {
				width: 54%;
				margin-left: 30rpx;

				.user_2_1 {
					width: 200px;
					font-size: 30rpx;
					font-weight: 600;
					color: #201E2E;
					margin-bottom: 5rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.user_2_2 {
					font-size: 26rpx;
					// font-weight: 600;
					color: #777777;
					margin-bottom: 5rpx;

					.num {
						color: #000;
						font-weight: 500;
						margin-left: 10rpx;
						margin-right: 5rpx;
					}

					.left {
						margin-left: 10rpx;

						.num {
							color: #000;
						}
					}
				}

				.user_2_3 {
					margin-top: 10rpx;
				}
			}

			.user_3 {
				position: absolute;
				right: 30rpx;

				.user_code {
					width: 54rpx;
					height: 54rpx;
					margin: 0 auto;
				}

				text {
					font-size: 24rpx;
					font-weight: 400;
					color: #2F2F2F;
				}
			}

		}
	}

	.card {
		width: 704rpx;
		margin: 0 auto;

		.card_1 {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.card_1_left {
				width: 345rpx;
				height: 348rpx;
				background: #FEFAEE;
				border-radius: 16rpx;
				position: relative;

				.card_1_left_title {
					margin: 50rpx 0 0 20rpx;

					view:nth-child(1) {
						font-size: 34rpx;
						font-weight: 600;
						// color: #000000;
					}

					view:nth-child(2) {
						font-size: 24rpx;
						font-weight: 400;
						// color: #797473;
						margin-top: 20rpx;
					}
				}

				.card_1_left_img {
					width: 85%;
					height: 100%;
					position: absolute;
					top: 0;
					right: 0;
				}
			}

			.card_1_right {
				width: 345rpx;
				height: 348rpx;
				background: #FFF9F9;
				border-radius: 16rpx;
				position: relative;

				.card_1_left_title {
					margin: 50rpx 0 0 20rpx;

					view:nth-child(1) {
						font-size: 34rpx;
						font-weight: 600;
						// color: #000000;
					}

					view:nth-child(2) {
						font-size: 24rpx;
						font-weight: 400;
						// color: #797473;
						margin-top: 20rpx;
					}
				}

				.card_1_left_img {
					width: 100%;
					height: 100%;
					position: absolute;
					top: 0;
				}
			}

		}

		.card_2 {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 20rpx 0;

			.card_2_content {
				width: 342rpx;
				height: 161rpx;
				background: #FFFFFF;
				border-radius: 36rpx 13rpx 13rpx 13rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.card_2_content_left {
					margin-left: 29rpx;
					text-align: left;

					view:nth-child(1) {
						font-size: 32rpx;
						font-weight: 500;
						color: #000000;
					}

					view:nth-child(2) {
						font-size: 24rpx;
						font-weight: 400;
						color: #AAAAAA;
						margin-top: 5rpx;
					}
				}

				.card_2_content_right {
					margin-right: 30rpx;
					width: 85rpx;
					height: 66rpx;
				}
			}
		}

		.card_3 {
			width: 100%;

		}
	}
</style>