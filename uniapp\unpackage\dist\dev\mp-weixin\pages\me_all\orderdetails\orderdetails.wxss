@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  width: 750rpx;
  padding: 30rpx;
  box-sizing: border-box;
  background: linear-gradient(180deg, #00C2A0 0%, #00C2A0 16%, #F6F7FB 38%, #F6F7FB 100%);
}
.container .title {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 34rpx;
  color: #FFFFFF;
}
.container .title image {
  width: 52rpx;
  height: 52rpx;
  margin-right: 6rpx;
}
.container .desc {
  font-size: 26rpx;
  color: #FFFFFF;
  margin-top: 8rpx;
  margin-bottom: 30rpx;
}
.container .content {
  width: 690rpx;
  height: 606rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}
.container .content .class-detail {
  display: flex;
  padding-bottom: 15rpx;
  border-bottom: 1rpx dashed #D1D1D1;
}
.container .content .class-detail image {
  width: 148rpx;
  height: 110rpx;
  margin-right: 10rpx;
}
.container .content .class-detail .right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.container .content .class-detail .right .title {
  font-weight: bold;
  font-size: 26rpx;
  color: #414141;
}
.container .content .class-detail .right .desc {
  font-weight: 400;
  font-size: 18rpx;
  color: #777777;
}
.container .content .class-detail .right .money {
  font-weight: bold;
  font-size: 26rpx;
  color: #343434;
}
.container .content .ma {
  width: 254rpx;
  height: 254rpx;
  margin: 40rpx auto;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.container .content .num {
  font-size: 26rpx;
  color: #343434;
}
.container .content .time {
  margin-top: 6rpx;
  font-size: 22rpx;
  color: #606060;
}
.container .order-detail {
  width: 690rpx;
  background: #FFFFFF;
  padding: 25rpx 30rpx;
  box-sizing: border-box;
  border-radius: 20rpx;
  margin-bottom: 100rpx;
}
.container .order-detail .title {
  font-weight: bold;
  font-size: 28rpx;
  color: #201E2E;
  padding-bottom: 18rpx;
  border-bottom: 1rpx dashed #D1D1D1;
}
.container .order-detail .detail {
  padding: 30rpx 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-bottom: 1rpx dashed #D1D1D1;
}
.container .order-detail .detail .every {
  width: 100%;
  font-weight: 500;
  font-size: 24rpx;
  color: #A2A2A2;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.container .order-detail .detail .every text:last-child {
  font-weight: 500;
  font-size: 24rpx;
  color: #201E2E;
}
.container .order-detail .total {
  margin-top: 25rpx;
  color: #A4A4A4;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.container .order-detail .total text {
  font-weight: 500;
  font-size: 34rpx;
  color: #E62E2E;
}

