const USER = 'user'; // 存储用户信息的 key
const TOKEN = 'TOKEN'; // 存储 token 的 key
const ORG = 'USER-ORG'; // 存储 ORG 的 key
// 获取用户信息（包括 token）
export const getUserInfo = () => {
	const userInfo = uni.getStorageSync(USER) || {};
	const token = uni.getStorageSync(TOKEN); // 获取 token
	// if (token) {
	// 	userInfo.token = token; // 将 token 添加到用户信息中
	// }
	return userInfo;
};

// 设置用户信息（包括 token）
export const setUserInfo = (user) => {
	try {
		// 保存用户信息到 storage
		uni.setStorageSync(USER, user);

		// 如果 user 对象中有 token，保存 token
		// if (user.token) {
		// 	uni.setStorageSync(TOKEN, user.token);
		// }
	} catch (e) {
		console.log(e);
	}
};
// // 设置机构加密编码
// export const setOrg = (org) => {
// 	// 保存org到 storage
// 	uni.setStorageSync(ORG, org);
// };
// //获取机构加密编码
// export const getOrg = () => {
// 	return uni.getStorageSync(ORG); // 获取 ORG

// };
// 删除用户信息和 token
export const clearUserInfo = () => {
	try {
		uni.removeStorageSync(USER); // 删除用户信息
		uni.removeStorageSync(TOKEN); // 删除 token
		// uni.removeStorageSync(ORG)
	} catch (e) {
		console.log(e);
	}
};

// 获取 token
export const getToken = () => {
	return uni.getStorageSync(TOKEN); // 获取 token
};

// 设置 token
export const setToken = (token) => {
	try {
		uni.setStorageSync(TOKEN, token); // 设置 token
	} catch (e) {
		console.log(e);
	}
};