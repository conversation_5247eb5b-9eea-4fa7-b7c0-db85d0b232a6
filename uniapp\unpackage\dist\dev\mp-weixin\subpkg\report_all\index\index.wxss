@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-63a500a1 {
  min-height: 100vh;
  background: #F6F7FB !important;
}
.content.data-v-63a500a1 {
  position: relative;
  background: #F6F7FB !important;
}
.content .content_hadrimg.data-v-63a500a1 {
  width: 100%;
  height: 490rpx;
  position: absolute;
  top: 0;
}
.content .content_hadrimg .nav-title.data-v-63a500a1 {
  width: 100%;
  position: absolute;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 34rpx;
  color: #FFFFFF;
}
.content .back-left.data-v-63a500a1 {
  position: absolute !important;
  left: 30rpx !important;
  z-index: 9999;
}
@-webkit-keyframes rotate-data-v-63a500a1 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes rotate-data-v-63a500a1 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@-webkit-keyframes rotateCounter-data-v-63a500a1 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
    /* 负值实现逆时针旋转 */
}
}
@keyframes rotateCounter-data-v-63a500a1 {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
    /* 负值实现逆时针旋转 */
}
}
.content .btn-ai-bg.data-v-63a500a1 {
  position: absolute;
  top: 370rpx;
  width: 750rpx;
  background: #F6F7FB;
  padding-top: 35rpx;
  box-sizing: border-box;
  border-radius: 30rpx 30rpx 0rpx 0rpx;
}
.content .btn-ai-bg .btn-ai.data-v-63a500a1 {
  width: 690rpx;
  position: relative;
  margin: 0 auto;
  padding: 35rpx 30rpx;
  box-sizing: border-box;
  background: #FFFFFF;
  border-radius: 40rpx;
}
.content .btn-ai-bg .btn-ai .num.data-v-63a500a1 {
  position: absolute;
  font-weight: 400;
  font-size: 26rpx;
  color: #989898;
  right: 30rpx;
  top: 35rpx;
}
.content .btn-ai-bg .btn-ai .bg-container.data-v-63a500a1 {
  height: 480rpx;
  width: 480rpx;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.content .btn-ai-bg .btn-ai .ai3-bg-container.data-v-63a500a1 {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.content .btn-ai-bg .btn-ai .ai3-bg.data-v-63a500a1 {
  background-image: url("https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai3.png");
  height: 480rpx;
  width: 480rpx;
  background-repeat: no-repeat;
  background-size: contain;
}
.content .btn-ai-bg .btn-ai .ai3-bg-animation.data-v-63a500a1 {
  -webkit-animation: rotate-data-v-63a500a1 4s linear infinite;
          animation: rotate-data-v-63a500a1 4s linear infinite;
}
.content .btn-ai-bg .btn-ai .ai2-bg-container.data-v-63a500a1 {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.content .btn-ai-bg .btn-ai .ai2-bg.data-v-63a500a1 {
  background-image: url("https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai2.png");
  height: 360rpx;
  width: 425rpx;
  background-repeat: no-repeat;
  background-size: contain;
  z-index: 99;
}
.content .btn-ai-bg .btn-ai .ai2-bg-animation.data-v-63a500a1 {
  -webkit-animation: rotateCounter-data-v-63a500a1 4.2s linear infinite;
          animation: rotateCounter-data-v-63a500a1 4.2s linear infinite;
}
.content .btn-ai-bg .btn-ai .ai-bg.data-v-63a500a1 {
  background-image: url("https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/ai.png");
  height: 236rpx;
  width: 236rpx;
  background-repeat: no-repeat;
  background-size: contain;
  z-index: 100;
}
.content .btn-ai-bg .btn-ai .btn-report.data-v-63a500a1 {
  width: 521rpx;
  height: 78rpx;
  line-height: 78rpx;
  text-align: center;
  font-weight: bold;
  font-size: 28rpx;
  color: #FFFFFF;
  margin: 40rpx auto 0;
  background: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);
  border-radius: 40rpx;
}
.content .report-list.data-v-63a500a1 {
  margin-top: 1050rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  position: relative;
}
.content .report-list .title.data-v-63a500a1 {
  position: relative;
  font-weight: 800;
  font-size: 30rpx;
  color: #060606;
}
.content .report-list .title.data-v-63a500a1::before {
  content: "";
  position: absolute;
  top: 38rpx;
  width: 120rpx;
  height: 19rpx;
  background: #DBFF9C;
  border-radius: 39rpx;
  z-index: -1;
}
.content .report-list .list.data-v-63a500a1 {
  width: 690rpx;
  height: 245rpx;
  padding: 20rpx 20rpx 20rpx 30rpx;
  box-sizing: border-box;
  background: #FFFFFF;
  margin-top: 25rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border-radius: 18rpx;
  box-shadow: 0rpx 8rpx 8rpx 1rpx #EBEBEB;
}
.content .report-list .list.data-v-63a500a1:last-child {
  margin-bottom: 100rpx;
}
.content .report-list .list .report-title.data-v-63a500a1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content .report-list .list .report-title .name.data-v-63a500a1 {
  font-weight: bold;
  font-size: 34rpx;
  color: #00C2A0;
}
.content .report-list .list .report-title .status.data-v-63a500a1 {
  width: 124rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  font-size: 28rpx;
  color: #00C2A0;
  background: #D6FFEC;
  border-radius: 30rpx;
}
.content .report-list .list .des.data-v-63a500a1 {
  font-size: 28rpx;
  color: #5A5A5A;
  line-height: 1.3;
}
.content .report-list .list .time-detail.data-v-63a500a1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content .report-list .list .time-detail .time.data-v-63a500a1 {
  font-weight: 500;
  font-size: 26rpx;
  color: #818181;
}
.content .report-list .list .time-detail .detail.data-v-63a500a1 {
  font-weight: 500;
  font-size: 26rpx;
  color: #00C2A0;
}

