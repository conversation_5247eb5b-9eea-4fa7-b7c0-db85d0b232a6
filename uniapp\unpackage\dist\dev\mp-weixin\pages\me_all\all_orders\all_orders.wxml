<view class="container data-v-6e8e75b3"><view class="tabs data-v-6e8e75b3"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['meunClick',['$0'],[[['list','',index,'status']]]]]]]}}" class="{{['data-v-6e8e75b3',activeIndex==item.status?'active':'']}}" bindtap="__e">{{item.title}}</text></block></view><view class="searchBox data-v-6e8e75b3"><input type="text" placeholder="搜索手机号" data-event-opts="{{[['input',[['__set_model',['','phone','$event',[]]]]]]}}" value="{{phone}}" bindinput="__e" class="data-v-6e8e75b3"/><image src="../../../static/me/icon20.png" mode data-event-opts="{{[['tap',[['open',['$event']]]]]}}" bindtap="__e" class="data-v-6e8e75b3"></image></view><block wx:for="{{allList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['details',['$0'],[[['allList','id',item.id,'id']]]]]]]}}" class="list data-v-6e8e75b3" bindtap="__e"><view class="time-top data-v-6e8e75b3"><view class="time data-v-6e8e75b3">{{"下单时间："+item.transTime}}</view><view class="status data-v-6e8e75b3"><block wx:if="{{item.status==='unredeemed'}}"><view class="btn data-v-6e8e75b3">扫码核销</view></block><text class="data-v-6e8e75b3">{{item.statusName}}</text></view></view><view class="class-content data-v-6e8e75b3"><view class="imageBox data-v-6e8e75b3"><block wx:for="{{item.orderProducts}}" wx:for-item="i" wx:for-index="index"><image src="{{i.productCover}}" mode class="data-v-6e8e75b3"></image></block></view><view class="right data-v-6e8e75b3"><view class="money data-v-6e8e75b3">{{"￥"+item.totalPrice}}</view><view class="num data-v-6e8e75b3">{{"共"+item.totalNum+"件"}}</view></view></view><view class="phone-money data-v-6e8e75b3"><view class="phone data-v-6e8e75b3">{{"下单手机号："+(item.studentPhone||'')}}</view><view class="money data-v-6e8e75b3">实际支付：<text class="data-v-6e8e75b3">{{"￥"+item.checkoutPrice}}</text></view></view></view></block><uni-calendar class="uni-calendar--hook data-v-6e8e75b3 vue-ref" vue-id="6340c1b3-1" clear-date="{{true}}" date="{{transTime}}" insert="{{info.insert}}" lunar="{{info.lunar}}" startDate="{{info.startDate}}" endDate="{{info.endDate}}" data-ref="calendar" data-event-opts="{{[['^confirm',[['confirm']]],['^close',[['close']]]]}}" bind:confirm="__e" bind:close="__e" bind:__l="__l"></uni-calendar></view>