@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content.data-v-408d747c {
  min-height: 100vh;
  background: linear-gradient(180deg, #CBF2E0 0%, #F6F7FB 43.3%);
  padding-bottom: 160rpx;
}
.content_header.data-v-408d747c {
  position: relative;
  height: 140rpx;
  background: #00C2A0;
}
.content_header .nav-title.data-v-408d747c {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 10;
}
.content_header .nav-title text.data-v-408d747c {
  font-size: 34rpx;
  color: #2D2D2D;
  font-weight: 400;
}
.content_header .back-left.data-v-408d747c {
  position: absolute;
  left: 30rpx;
  z-index: 10;
}
.notification.data-v-408d747c {
  display: flex;
  align-items: center;
  margin: 30rpx;
  padding: 14rpx 28rpx;
  background: #F5FFFD;
  border-radius: 31rpx;
}
.notification .notification-icon.data-v-408d747c {
  width: 49rpx;
  height: 49rpx;
  margin-right: 28rpx;
}
.notification .notification-text.data-v-408d747c {
  font-size: 28rpx;
  color: #5A5A5A;
  flex: 1;
}
.step-indicator.data-v-408d747c {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30rpx 0;
}
.step-indicator .step-item.data-v-408d747c {
  margin: 0 162rpx;
}
.step-indicator .step-item .step-number.data-v-408d747c {
  width: 43rpx;
  height: 43rpx;
  border-radius: 50%;
  background: #FF9B3A;
  color: #FFFFFF;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.step-indicator .step-item.active .step-number.data-v-408d747c {
  background: #FF9B3A;
}
.form-title.data-v-408d747c {
  position: relative;
  margin: 30rpx;
}
.form-title .title-bg.data-v-408d747c {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 92rpx;
  height: 19rpx;
  background: #DBFF9C;
}
.form-title .title-text.data-v-408d747c {
  font-size: 30rpx;
  color: #060606;
  font-weight: 400;
}
.ai-section.data-v-408d747c {
  padding: 30rpx 100rpx;
}
.ai-section .ai-container.data-v-408d747c {
  position: relative;
  height: 545rpx;
}
.ai-section .ai-container .ai-bg-4.data-v-408d747c {
  position: absolute;
  top: 0;
  left: 0;
  width: 551rpx;
  height: 545rpx;
  z-index: 1;
}
.ai-section .ai-container .ai-bg-1.data-v-408d747c {
  position: absolute;
  top: 22rpx;
  left: 20rpx;
  width: 510rpx;
  height: 508rpx;
  z-index: 2;
}
.ai-section .ai-container .ai-bg-2.data-v-408d747c {
  position: absolute;
  top: 68rpx;
  left: 27rpx;
  width: 494rpx;
  height: 419rpx;
  z-index: 3;
}
.ai-section .ai-container .ai-center.data-v-408d747c {
  position: absolute;
  top: 126rpx;
  left: 120rpx;
  width: 306rpx;
  height: 306rpx;
  z-index: 4;
}
.ai-section .ai-container .ai-center .ai-main.data-v-408d747c {
  width: 100%;
  height: 100%;
}
.ai-section .ai-container .ai-center .ai-text.data-v-408d747c {
  position: absolute;
  top: 77rpx;
  left: 40rpx;
  width: 227rpx;
  height: 154rpx;
}
.form-content.data-v-408d747c {
  padding: 0 30rpx;
}
.form-content .form-item.data-v-408d747c {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 28rpx;
  margin-bottom: 30rpx;
}
.form-content .form-item .form-label.data-v-408d747c {
  font-size: 30rpx;
  color: #504E4E;
  margin-bottom: 20rpx;
}
.form-content .form-item .form-textarea.data-v-408d747c {
  width: 100%;
  font-size: 28rpx;
  color: #989898;
  min-height: 80rpx;
  line-height: 1.5;
}
.bottom-buttons.data-v-408d747c {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #F6F7FB;
  padding: 30rpx 100rpx 60rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.bottom-buttons .generate-btn.data-v-408d747c {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #26C8AC 0%, #19C990 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bottom-buttons .generate-btn text.data-v-408d747c {
  font-size: 30rpx;
  color: #FFFFFF;
  font-weight: 400;
}
.bottom-buttons .next-btn.data-v-408d747c {
  width: 100%;
  height: 81rpx;
  background: linear-gradient(135deg, #26C8AC 0%, #19C990 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bottom-buttons .next-btn text.data-v-408d747c {
  font-size: 30rpx;
  color: #FFFFFF;
  font-weight: 400;
}

